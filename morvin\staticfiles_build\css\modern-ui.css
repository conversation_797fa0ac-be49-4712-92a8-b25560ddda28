/* Modern UI Styles for Hall System */

/* Set body background to white and ensure text visibility */
body {
    background-color: #ffffff !important;
    color: #333333;
}

/* Main content area styling */
.main-content {
    background-color: #f8f9fa;
}

.page-content {
    background-color: #f8f9fa;
}

/* Modern card styling - replace the dark borders */
.card {
    border: 1px solid #e3e6f0 !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    background-color: #ffffff !important;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
}

/* Prevent card hover effects from affecting modals */
.modal .card:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.card-body {
    border: none !important;
    border-radius: 12px !important;
    background-color: #ffffff !important;
    color: #333333 !important;
}

/* Ensure all text is visible */
.card-body h4,
.card-body h5,
.card-body h6,
.card-body p,
.card-body span,
.card-body div {
    color: #333333 !important;
}

/* Header title styling */
.header-title {
    color: #2c3e50 !important;
    font-weight: 600;
}

/* Page title box modernization - solid color instead of gradient */
.page-title-box {
    background-color: #525ce5 !important;
    border: none !important;
    border-radius: 0 0 24px 24px !important;
    color: #ffffff !important;
}

/* Sidebar modernization */
.vertical-menu {
    background-color: #ffffff !important;
    border-right: 1px solid #e3e6f0 !important;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06) !important;
}

#sidebar-menu ul li a {
    color: #495057 !important;
    border-radius: 8px !important;
    margin: 2px 8px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

#sidebar-menu ul li a:hover {
    background-color: #f8f9fa !important;
    color: #667eea !important;
}

#sidebar-menu ul li a.mm-active {
    background-color: #667eea !important;
    color: #ffffff !important;
}

/* Progress bars modernization - solid color instead of gradient */
.progress {
    background-color: #e9ecef !important;
    border-radius: 10px !important;
    height: 8px !important;
}

.progress-bar {
    border-radius: 10px !important;
    background-color: #667eea !important;
}

/* Button modernization - solid colors instead of gradients */
.btn {
    border-radius: 8px !important;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: #667eea !important;
    border: none !important;
}

.btn-primary:hover {
    background-color: #5a6fd8 !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Prevent button hover effects from affecting modals */
.modal .btn:hover {
    transform: none !important;
}

.btn-success {
    background-color: #28a745 !important;
    border: none !important;
}

.btn-success:hover {
    background-color: #218838 !important;
}

.btn-info {
    background-color: #17a2b8 !important;
    border: none !important;
}

.btn-info:hover {
    background-color: #138496 !important;
}

.btn-warning {
    background-color: #ffc107 !important;
    border: none !important;
    color: #212529 !important;
}

.btn-warning:hover {
    background-color: #e0a800 !important;
}

/* Modal specific fixes to prevent glitching */
.modal {
    z-index: 1055 !important;
}

.modal-dialog {
    position: relative;
    margin: 1.75rem auto;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    outline: 0;
    transform: none !important;
}

.modal-content:hover {
    transform: none !important;
}

/* Table modernization */
.table {
    background-color: #ffffff !important;
    border-radius: 12px !important;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    border: none !important;
    font-weight: 600;
}

.table tbody tr {
    border: none !important;
}

.table tbody tr:hover {
    background-color: #f8f9fa !important;
}

.table tbody td {
    border-color: #e9ecef !important;
    color: #495057 !important;
}

/* Form controls modernization */
.form-control,
.form-select {
    border: 1px solid #e3e6f0 !important;
    border-radius: 8px !important;
    background-color: #ffffff !important;
    color: #495057 !important;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15) !important;
}

/* Calendar modernization */
.fc .fc-toolbar {
    background-color: #ffffff !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 1rem !important;
}

.fc-event {
    border-radius: 6px !important;
    border: none !important;
    background-color: #667eea !important;
}

/* Dashboard specific overrides */
.dash-info-widget {
    background-color: #ffffff !important;
    border: 1px solid #e3e6f0 !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

/* Statistics charts area */
.apex-charts {
    background-color: #ffffff !important;
    border-radius: 12px !important;
}

/* Mini stat icons - solid color instead of gradient */
.mini-stat-icon {
    background-color: #667eea !important;
    border-radius: 50% !important;
    color: #ffffff !important;
}

/* Avatar styling - solid color instead of gradient */
.avatar-title {
    background-color: #667eea !important;
    border-radius: 50% !important;
    color: #ffffff !important;
}

/* Vendor list and other tables */
.table-responsive {
    border-radius: 12px !important;
    overflow: hidden;
}

/* Ensure dropdown menus are also modernized */
.dropdown-menu {
    border: 1px solid #e3e6f0 !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    background-color: #ffffff !important;
}

.dropdown-item {
    color: #495057 !important;
    border-radius: 8px !important;
    margin: 2px 8px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa !important;
    color: #667eea !important;
}

/* Header profile and notifications */
.header-item {
    color: #495057 !important;
    border-radius: 8px !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.header-item:hover {
    background-color: #f8f9fa !important;
    color: #667eea !important;
}

/* Footer modernization */
.footer {
    background-color: #ffffff !important;
    border-top: 1px solid #e3e6f0 !important;
    color: #6c757d !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem !important;
    }
    
    .card-body {
        padding: 1rem !important;
    }
}

/* Animation for loading states */
@keyframes modernFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: modernFadeIn 0.5s ease-out;
}

/* Override any remaining dark borders globally */
[style*="border: 2px solid black"] {
    border: 1px solid #e3e6f0 !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    background-color: #ffffff !important;
}

/* Override any remaining black backgrounds */
[style*="background-color: black"] {
    background-color: #f8f9fa !important;
}

/* Ensure all text is readable */
* {
    color: inherit;
}

/* Dark theme support (if needed) */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #ffffff !important; /* Keep white as requested */
        color: #333333 !important;
    }
} 