:root {
    --primary: #3a86ff;
    --secondary: #8338ec;
    --success: #06d6a0;
    --warning: #ffbe0b;
    --danger: #ef476f;
    --light: #f8f9fa;
    --dark: #212529;
    --gray: #6c757d;
    --light-gray: #e9ecef;
}

.calendar-page {
    padding: 15px;
    color: var(--dark);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.month-navigator {
    display: flex;
    align-items: center;
    gap: 15px;
}

.month-title {
    font-size: 20px;
    font-weight: 600;
    min-width: 150px;
    text-align: center;
}

.nav-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--gray);
    padding: 5px 10px;
    border-radius: 4px;
}

.nav-btn:hover {
    background-color: var(--light-gray);
}

.days-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--gray);
    font-size: 14px;
    align-items: center;
}

.days-header > div {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 0;
}

.days-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 6px;
    grid-auto-rows: min-content;
    margin-bottom: 20px;
    align-items: stretch;
}

.day-cell {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    min-height: 50px;
    max-height: 60px;
    font-size: 15px;
    text-align: center;
    width: 100%;
    box-sizing: border-box;
    border: 1px solid transparent;
    background-color: #f8f9fa;
}

.day-cell:hover {
    background-color: var(--light-gray);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.day-cell.fully-booked:hover {
    background-color: #ffcdd2;
    border-color: #ef9a9a;
}

.day-cell.partially-available:hover {
    background-color: #ffe0b2;
    border-color: #ffb74d;
}

.day-cell.fully-available:hover {
    background-color: #c8e6c9;
    border-color: #a5d6a7;
}

.day-cell.tentative:hover {
    background-color: #ffecb3;
    border-color: #ffcc02;
}

.day-cell.today {
    background-color: var(--primary);
    color: white;
    font-weight: 600;
}

.day-cell.selected {
    background-color: var(--secondary);
    color: white;
    font-weight: 600;
}

.day-cell.empty-cell {
    background: transparent;
    cursor: default;
    opacity: 0;
}

.day-cell.other-month {
    color: var(--light-gray);
}

.day-cell.has-events {
    border: 2px solid var(--warning);
}

.day-cell.fully-booked {
    background-color: #ffebee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
}

.day-cell.partially-available {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc02;
}

.day-cell.fully-available {
    background-color: #e8f5e9;
    color: #388e3c;
    border: 1px solid #c8e6c9;
}

.day-cell.tentative {
    background-color: #fff8e1;
    color: #f57f17;
    border: 1px solid #ffecb3;
}

.availability-container {
    margin-top: 25px;
    animation: fadeIn 0.3s ease;
}

.availability-header {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.venue-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 18px;
}

.venue-card {
    border: 1px solid var(--light-gray);
    border-radius: 8px;
    padding: 15px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.venue-name {
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--dark);
}

.time-slot {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid var(--light-gray);
}

.time-slot:last-child {
    border-bottom: none;
}

.slot-time {
    font-weight: 500;
}

.slot-status {
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.slot-status.available {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.slot-status.booked {
    background-color: #ffebee;
    color: #c62828;
}

.slot-status.tentative {
    background-color: #fff8e1;
    color: #f57f17;
}

.no-events-message {
    text-align: center;
    color: var(--gray);
    font-style: italic;
    padding: 40px 20px;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Layout improvements for better one-page fit */
.calendar-container {
    margin-bottom: 20px;
}

/* Responsive design */
@media (max-width: 768px) {
    .calendar-page {
        padding: 15px;
    }
    
    .venue-cards {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .calendar-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .day-cell {
        min-height: 45px;
        font-size: 14px;
    }
}

@media (min-width: 1200px) {
    .venue-cards {
        grid-template-columns: repeat(4, 1fr);
    }
} 