import os
import shutil
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Setup production database by copying local database'

    def handle(self, *args, **options):
        # Source database (local)
        local_db_path = os.path.join(os.path.dirname(settings.BASE_DIR), 'persistent_data', 'db_data')
        
        # Target database (production)
        if os.environ.get('RAILWAY_ENVIRONMENT'):
            prod_db_path = '/app/persistent_data/db_data'
            prod_dir = '/app/persistent_data'
        else:
            self.stdout.write(self.style.ERROR('This command is only for production deployment'))
            return
        
        # Create production directory
        os.makedirs(prod_dir, exist_ok=True)
        
        # Copy database if local file exists
        if os.path.exists(local_db_path):
            shutil.copy2(local_db_path, prod_db_path)
            self.stdout.write(self.style.SUCCESS(f'Successfully copied database to {prod_db_path}'))
        else:
            self.stdout.write(self.style.ERROR(f'Local database not found at {local_db_path}'))
            
        # Set proper permissions
        try:
            os.chmod(prod_db_path, 0o644)
            self.stdout.write(self.style.SUCCESS('Database permissions set successfully'))
        except:
            pass 