/*!
 * Bootstrap v5.1.0 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --bs-blue: #525ce5;
  --bs-indigo: #564ab1;
  --bs-purple: #6f42c1;
  --bs-pink: #e83e8c;
  --bs-red: #f14e4e;
  --bs-orange: #f1734f;
  --bs-yellow: #eeb148;
  --bs-green: #23c58f;
  --bs-teal: #050505;
  --bs-cyan: #5ba4e5;
  --bs-white: #fff;
  --bs-gray: #74788d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f9fafc;
  --bs-gray-200: #edf1f5;
  --bs-gray-300: #eaedf1;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #74788d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #525ce5;
  --bs-secondary: #74788d;
  --bs-success: #23c58f;
  --bs-info: #5ba4e5;
  --bs-warning: #eeb148;
  --bs-danger: #f14e4e;
  --bs-pink: #e83e8c;
  --bs-light: #f9fafc;
  --bs-dark: #343a40;
  --bs-primary-rgb: 82, 92, 229;
  --bs-secondary-rgb: 116, 120, 141;
  --bs-success-rgb: 35, 197, 143;
  --bs-info-rgb: 91, 164, 229;
  --bs-warning-rgb: 238, 177, 72;
  --bs-danger-rgb: 241, 78, 78;
  --bs-pink-rgb: 232, 62, 140;
  --bs-light-rgb: 249, 250, 252;
  --bs-dark-rgb: 52, 58, 64;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-rgb: 73, 80, 87;
  --bs-font-sans-serif: "Inter", sans-serif;
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0)
  );
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 0.875rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #495057;
  --bs-body-bg: #f5f7fa;
}
*,
::after,
::before {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}
body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}
hr {
  margin: 1rem 0;
  color: rgba(0, 0, 0, 0.1);
  background-color: currentColor;
  border: 0;
  opacity: 0.7;
}
hr:not([size]) {
  height: 1px;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}
.h1,
h1 {
  font-size: calc(1.34375rem + 1.125vw);
}
@media (min-width: 1200px) {
  .h1,
  h1 {
    font-size: 2.1875rem;
  }
}
.h2,
h2 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  .h2,
  h2 {
    font-size: 1.75rem;
  }
}
.h3,
h3 {
  font-size: calc(1.27813rem + 0.3375vw);
}
@media (min-width: 1200px) {
  .h3,
  h3 {
    font-size: 1.53125rem;
  }
}
.h4,
h4 {
  font-size: calc(1.25625rem + 0.075vw);
}
@media (min-width: 1200px) {
  .h4,
  h4 {
    font-size: 1.3125rem;
  }
}
.h5,
h5 {
  font-size: 1.09375rem;
}
.h6,
h6 {
  font-size: 0.875rem;
}
p {
  margin-top: 0;
  margin-bottom: 1rem;
}
abbr[data-bs-original-title],
abbr[title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
  text-decoration-skip-ink: none;
}
address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
ol,
ul {
  padding-left: 2rem;
}
dl,
ol,
ul {
  margin-top: 0;
  margin-bottom: 1rem;
}
ol ol,
ol ul,
ul ol,
ul ul {
  margin-bottom: 0;
}
dt {
  font-weight: 600;
}
dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1rem;
}
b,
strong {
  font-weight: bolder;
}
.small,
small {
  font-size: 80%;
}
.mark,
mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}
sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
a {
  color: #525ce5;
  text-decoration: none;
}
a:hover {
  color: #1f2acc;
  text-decoration: underline;
}
a:not([href]):not([class]),
a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}
code,
kbd,
pre,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
  direction: ltr;
  unicode-bidi: bidi-override;
}
pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 87.5%;
  color: #212529;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}
code {
  font-size: 87.5%;
  color: #e83e8c;
  word-wrap: break-word;
}
a > code {
  color: inherit;
}
kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
  font-weight: 600;
}
figure {
  margin: 0 0 1rem;
}
img,
svg {
  vertical-align: middle;
}
table {
  caption-side: bottom;
  border-collapse: collapse;
}
caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #74788d;
  text-align: left;
}
th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}
tbody,
td,
tfoot,
th,
thead,
tr {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}
label {
  display: inline-block;
}
button {
  border-radius: 0;
}
button:focus:not(:focus-visible) {
  outline: 0;
}
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
button,
select {
  text-transform: none;
}
[role="button"] {
  cursor: pointer;
}
select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}
[list]::-webkit-calendar-picker-indicator {
  display: none;
}
[type="button"],
[type="reset"],
[type="submit"],
button {
  -webkit-appearance: button;
}
[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled),
button:not(:disabled) {
  cursor: pointer;
}
::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
textarea {
  resize: vertical;
}
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-year-field {
  padding: 0;
}
::-webkit-inner-spin-button {
  height: auto;
}
[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-color-swatch-wrapper {
  padding: 0;
}
::file-selector-button {
  font: inherit;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
iframe {
  border: 0;
}
summary {
  display: list-item;
  cursor: pointer;
}
progress {
  vertical-align: baseline;
}
[hidden] {
  display: none ;
}
.lead {
  font-size: 1.09375rem;
  font-weight: 300;
}
.display-1 {
  font-size: calc(1.725rem + 5.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 6rem;
  }
}
.display-2 {
  font-size: calc(1.675rem + 5.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 5.5rem;
  }
}
.display-3 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4.5rem;
  }
}
.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}
.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}
.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
}
.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}
.initialism {
  font-size: 80%;
  text-transform: uppercase;
}
.blockquote {
  margin-bottom: 1rem;
  font-size: 1.09375rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}
.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 80%;
  color: #74788d;
}
.blockquote-footer::before {
  content: "\2014\00A0";
}
.img-fluid {
  max-width: 100%;
  height: auto;
}
.img-thumbnail {
  padding: 0.25rem;
  background-color: #f5f7fa;
  border: 1px solid #eaedf1;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}
.figure {
  display: inline-block;
}
.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}
.figure-caption {
  font-size: 90%;
  color: #74788d;
}
.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  width: 100%;
  padding-right: var(--bs-gutter-x, 12px);
  padding-left: var(--bs-gutter-x, 12px);
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .container,
  .container-sm {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container,
  .container-md,
  .container-sm {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1320px;
  }
}
.row {
  --bs-gutter-x: 24px;
  --bs-gutter-y: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-top: calc(var(--bs-gutter-y) * -1);
  margin-right: calc(var(--bs-gutter-x) * -0.5);
  margin-left: calc(var(--bs-gutter-x) * -0.5);
}
.row > * {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}
.col {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0%;
  flex: 1 0 0%;
}
.row-cols-auto > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
}
.row-cols-1 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 100%;
}
.row-cols-2 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 50%;
}
.row-cols-3 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 33.33333%;
}
.row-cols-4 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 25%;
}
.row-cols-5 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 20%;
}
.row-cols-6 > * {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 16.66667%;
}
.col-auto {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
}
.col-1 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 8.33333%;
}
.col-2 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 16.66667%;
}
.col-3 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 25%;
}
.col-4 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 33.33333%;
}
.col-5 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 41.66667%;
}
.col-6 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 50%;
}
.col-7 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 58.33333%;
}
.col-8 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 66.66667%;
}
.col-9 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 75%;
}
.col-10 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 83.33333%;
}
.col-11 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 91.66667%;
}
.col-12 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: 100%;
}
.offset-1 {
  margin-left: 8.33333%;
}
.offset-2 {
  margin-left: 16.66667%;
}
.offset-3 {
  margin-left: 25%;
}
.offset-4 {
  margin-left: 33.33333%;
}
.offset-5 {
  margin-left: 41.66667%;
}
.offset-6 {
  margin-left: 50%;
}
.offset-7 {
  margin-left: 58.33333%;
}
.offset-8 {
  margin-left: 66.66667%;
}
.offset-9 {
  margin-left: 75%;
}
.offset-10 {
  margin-left: 83.33333%;
}
.offset-11 {
  margin-left: 91.66667%;
}
.g-0,
.gx-0 {
  --bs-gutter-x: 0;
}
.g-0,
.gy-0 {
  --bs-gutter-y: 0;
}
.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem;
}
.g-1,
.gy-1 {
  --bs-gutter-y: 0.25rem;
}
.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem;
}
.g-2,
.gy-2 {
  --bs-gutter-y: 0.5rem;
}
.g-3,
.gx-3 {
  --bs-gutter-x: 1rem;
}
.g-3,
.gy-3 {
  --bs-gutter-y: 1rem;
}
.g-4,
.gx-4 {
  --bs-gutter-x: 1.5rem;
}
.g-4,
.gy-4 {
  --bs-gutter-y: 1.5rem;
}
.g-5,
.gx-5 {
  --bs-gutter-x: 3rem;
}
.g-5,
.gy-5 {
  --bs-gutter-y: 3rem;
}
@media (min-width: 576px) {
  .col-sm {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }
  .row-cols-sm-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-sm-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-sm-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-sm-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .row-cols-sm-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-sm-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-sm-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-sm-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .col-sm-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333%;
  }
  .col-sm-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-sm-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .col-sm-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .col-sm-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66667%;
  }
  .col-sm-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333%;
  }
  .col-sm-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66667%;
  }
  .col-sm-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }
  .col-sm-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333%;
  }
  .col-sm-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66667%;
  }
  .col-sm-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333%;
  }
  .offset-sm-2 {
    margin-left: 16.66667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333%;
  }
  .offset-sm-5 {
    margin-left: 41.66667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333%;
  }
  .offset-sm-8 {
    margin-left: 66.66667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333%;
  }
  .offset-sm-11 {
    margin-left: 91.66667%;
  }
  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0;
  }
  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0;
  }
  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1rem;
  }
  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1rem;
  }
  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 3rem;
  }
  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }
  .row-cols-md-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-md-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-md-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-md-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .row-cols-md-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-md-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-md-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-md-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .col-md-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333%;
  }
  .col-md-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-md-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .col-md-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .col-md-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66667%;
  }
  .col-md-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333%;
  }
  .col-md-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66667%;
  }
  .col-md-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }
  .col-md-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333%;
  }
  .col-md-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66667%;
  }
  .col-md-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333%;
  }
  .offset-md-2 {
    margin-left: 16.66667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333%;
  }
  .offset-md-5 {
    margin-left: 41.66667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333%;
  }
  .offset-md-8 {
    margin-left: 66.66667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333%;
  }
  .offset-md-11 {
    margin-left: 91.66667%;
  }
  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0;
  }
  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0;
  }
  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1rem;
  }
  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1rem;
  }
  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 3rem;
  }
  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }
  .row-cols-lg-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-lg-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-lg-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-lg-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .row-cols-lg-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-lg-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-lg-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-lg-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .col-lg-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333%;
  }
  .col-lg-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-lg-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .col-lg-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .col-lg-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66667%;
  }
  .col-lg-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .col-lg-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333%;
  }
  .col-lg-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66667%;
  }
  .col-lg-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }
  .col-lg-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333%;
  }
  .col-lg-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66667%;
  }
  .col-lg-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333%;
  }
  .offset-lg-2 {
    margin-left: 16.66667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333%;
  }
  .offset-lg-5 {
    margin-left: 41.66667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333%;
  }
  .offset-lg-8 {
    margin-left: 66.66667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333%;
  }
  .offset-lg-11 {
    margin-left: 91.66667%;
  }
  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0;
  }
  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0;
  }
  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1rem;
  }
  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1rem;
  }
  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 3rem;
  }
  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }
  .row-cols-xl-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xl-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xl-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xl-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .row-cols-xl-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xl-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-xl-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .col-xl-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333%;
  }
  .col-xl-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-xl-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .col-xl-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66667%;
  }
  .col-xl-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xl-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333%;
  }
  .col-xl-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66667%;
  }
  .col-xl-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xl-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333%;
  }
  .col-xl-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66667%;
  }
  .col-xl-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333%;
  }
  .offset-xl-2 {
    margin-left: 16.66667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333%;
  }
  .offset-xl-5 {
    margin-left: 41.66667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333%;
  }
  .offset-xl-8 {
    margin-left: 66.66667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333%;
  }
  .offset-xl-11 {
    margin-left: 91.66667%;
  }
  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0;
  }
  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0;
  }
  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
  }
  .row-cols-xxl-auto > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xxl-1 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xxl-2 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xxl-3 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .row-cols-xxl-4 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xxl-5 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xxl-6 > * {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-xxl-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
  }
  .col-xxl-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 8.33333%;
  }
  .col-xxl-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 16.66667%;
  }
  .col-xxl-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xxl-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 33.33333%;
  }
  .col-xxl-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 41.66667%;
  }
  .col-xxl-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xxl-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 58.33333%;
  }
  .col-xxl-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 66.66667%;
  }
  .col-xxl-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xxl-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 83.33333%;
  }
  .col-xxl-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 91.66667%;
  }
  .col-xxl-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xxl-0 {
    margin-left: 0;
  }
  .offset-xxl-1 {
    margin-left: 8.33333%;
  }
  .offset-xxl-2 {
    margin-left: 16.66667%;
  }
  .offset-xxl-3 {
    margin-left: 25%;
  }
  .offset-xxl-4 {
    margin-left: 33.33333%;
  }
  .offset-xxl-5 {
    margin-left: 41.66667%;
  }
  .offset-xxl-6 {
    margin-left: 50%;
  }
  .offset-xxl-7 {
    margin-left: 58.33333%;
  }
  .offset-xxl-8 {
    margin-left: 66.66667%;
  }
  .offset-xxl-9 {
    margin-left: 75%;
  }
  .offset-xxl-10 {
    margin-left: 83.33333%;
  }
  .offset-xxl-11 {
    margin-left: 91.66667%;
  }
  .g-xxl-0,
  .gx-xxl-0 {
    --bs-gutter-x: 0;
  }
  .g-xxl-0,
  .gy-xxl-0 {
    --bs-gutter-y: 0;
  }
  .g-xxl-1,
  .gx-xxl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .g-xxl-1,
  .gy-xxl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .g-xxl-2,
  .gx-xxl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xxl-2,
  .gy-xxl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xxl-3,
  .gx-xxl-3 {
    --bs-gutter-x: 1rem;
  }
  .g-xxl-3,
  .gy-xxl-3 {
    --bs-gutter-y: 1rem;
  }
  .g-xxl-4,
  .gx-xxl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xxl-4,
  .gy-xxl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xxl-5,
  .gx-xxl-5 {
    --bs-gutter-x: 3rem;
  }
  .g-xxl-5,
  .gy-xxl-5 {
    --bs-gutter-y: 3rem;
  }
}
.table {
  --bs-table-bg: transparent;
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: #495057;
  --bs-table-striped-bg: #f9fafc;
  --bs-table-active-color: #495057;
  --bs-table-active-bg: #f9fafc;
  --bs-table-hover-color: #495057;
  --bs-table-hover-bg: #f9fafc;
  width: 100%;
  margin-bottom: 1rem;
  color: #495057;
  vertical-align: top;
  border-color: #edf1f5;
}
.table > :not(caption) > * > * {
  padding: 0.75rem 0.75rem;
  background-color: var(--bs-table-bg);
  border-bottom-width: 1px;
  -webkit-box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
  box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}
.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #edf1f5;
}
.caption-top {
  caption-side: top;
}
.table-sm > :not(caption) > * > * {
  padding: 0.3rem 0.3rem;
}
.table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}
.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-striped > tbody > tr:nth-of-type(odd) {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color);
}
.table-active {
  --bs-table-accent-bg: var(--bs-table-active-bg);
  color: var(--bs-table-active-color);
}
.table-hover > tbody > tr:hover {
  --bs-table-accent-bg: var(--bs-table-hover-bg);
  color: var(--bs-table-hover-color);
}
.table-primary {
  --bs-table-bg: #dcdefa;
  --bs-table-striped-bg: #d1d3ee;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #c6c8e1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #cccde7;
  --bs-table-hover-color: #000;
  color: #000;
  border-color: #c6c8e1;
}
.table-secondary {
  --bs-table-bg: #e3e4e8;
  --bs-table-striped-bg: #d8d9dc;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #cccdd1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #d2d3d7;
  --bs-table-hover-color: #000;
  color: #000;
  border-color: #cccdd1;
}
.table-success {
  --bs-table-bg: #d3f3e9;
  --bs-table-striped-bg: #c8e7dd;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #bedbd2;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #c3e1d8;
  --bs-table-hover-color: #000;
  color: #000;
  border-color: #bedbd2;
}
.table-info {
  --bs-table-bg: #deedfa;
  --bs-table-striped-bg: #d3e1ee;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #c8d5e1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #cddbe7;
  --bs-table-hover-color: #000;
  color: #000;
  border-color: #c8d5e1;
}
.table-warning {
  --bs-table-bg: #fcefda;
  --bs-table-striped-bg: #efe3cf;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e3d7c4;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e9ddca;
  --bs-table-hover-color: #000;
  color: #000;
  border-color: #e3d7c4;
}
.table-danger {
  --bs-table-bg: #fcdcdc;
  --bs-table-striped-bg: #efd1d1;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e3c6c6;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e9cccc;
  --bs-table-hover-color: #000;
  color: #000;
  border-color: #e3c6c6;
}
.table-light {
  --bs-table-bg: #f9fafc;
  --bs-table-striped-bg: #edeeef;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e0e1e3;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e6e7e9;
  --bs-table-hover-color: #000;
  color: #000;
  border-color: #e0e1e3;
}
.table-dark {
  --bs-table-bg: #343a40;
  --bs-table-striped-bg: #3e444a;
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: #484e53;
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: #43494e;
  --bs-table-hover-color: #fff;
  color: #fff;
  border-color: #484e53;
}
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
@media (max-width: 575.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1399.98px) {
  .table-responsive-xxl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.form-label {
  margin-bottom: 0.5rem;
}
.col-form-label {
  padding-top: calc(0.47rem + 1px);
  padding-bottom: calc(0.47rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}
.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.09375rem;
}
.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.76563rem;
}
@media (min-width: 1200px) {
  .col-form-label-sm {
    font-size: 0.76562rem;
  }
}
.form-text {
  margin-top: 0.25rem;
  font-size: 80%;
  color: #74788d;
}
.form-control {
  display: block;
  width: 100%;
  padding: 0.47rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0.25rem;
  -webkit-transition: border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    -webkit-transition: none;
    transition: none;
  }
}
.form-control[type="file"] {
  overflow: hidden;
}
.form-control[type="file"]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #b1bbc4;
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.form-control::-webkit-date-and-time-value {
  height: 1.5em;
}
.form-control::-webkit-input-placeholder {
  color: #74788d;
  opacity: 1;
}
.form-control::-moz-placeholder {
  color: #74788d;
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: #74788d;
  opacity: 1;
}
.form-control::-ms-input-placeholder {
  color: #74788d;
  opacity: 1;
}
.form-control::placeholder {
  color: #74788d;
  opacity: 1;
}
.form-control:disabled,
.form-control[readonly] {
  background-color: #edf1f5;
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0.47rem 0.75rem;
  margin: -0.47rem -0.75rem;
  -webkit-margin-end: 0.75rem;
  margin-inline-end: 0.75rem;
  color: #495057;
  background-color: #edf1f5;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    -webkit-transition: none;
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: #e1e5e9;
}
.form-control::-webkit-file-upload-button {
  padding: 0.47rem 0.75rem;
  margin: -0.47rem -0.75rem;
  -webkit-margin-end: 0.75rem;
  margin-inline-end: 0.75rem;
  color: #495057;
  background-color: #edf1f5;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
  background-color: #e1e5e9;
}
.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.47rem 0;
  margin-bottom: 0;
  line-height: 1.5;
  color: #495057;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-lg,
.form-control-plaintext.form-control-sm {
  padding-right: 0;
  padding-left: 0;
}
.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.76563rem;
  border-radius: 0.2rem;
}
@media (min-width: 1200px) {
  .form-control-sm {
    font-size: 0.76562rem;
  }
}
.form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  -webkit-margin-end: 0.5rem;
  margin-inline-end: 0.5rem;
}
.form-control-sm::-webkit-file-upload-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  -webkit-margin-end: 0.5rem;
  margin-inline-end: 0.5rem;
}
.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  border-radius: 0.4rem;
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  -webkit-margin-end: 1rem;
  margin-inline-end: 1rem;
}
.form-control-lg::-webkit-file-upload-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  -webkit-margin-end: 1rem;
  margin-inline-end: 1rem;
}
textarea.form-control {
  min-height: calc(1.5em + 0.94rem + 2px);
}
textarea.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
}
textarea.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
}
.form-control-color {
  width: 3rem;
  height: auto;
  padding: 0.47rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  height: 1.5em;
  border-radius: 0.25rem;
}
.form-control-color::-webkit-color-swatch {
  height: 1.5em;
  border-radius: 0.25rem;
}
.form-select {
  display: block;
  width: 100%;
  padding: 0.47rem 1.75rem 0.47rem 0.75rem;
  -moz-padding-start: calc(0.75rem - 3px);
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  -webkit-transition: border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    -webkit-transition: none;
    transition: none;
  }
}
.form-select:focus {
  border-color: #b1bbc4;
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
}
.form-select[multiple],
.form-select[size]:not([size="1"]) {
  padding-right: 0.75rem;
  background-image: none;
}
.form-select:disabled {
  color: #74788d;
  background-color: #edf1f5;
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #495057;
}
.form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.76563rem;
}
@media (min-width: 1200px) {
  .form-select-sm {
    font-size: 0.76562rem;
  }
}
.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.09375rem;
}
.form-check {
  display: block;
  min-height: 1.3125rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}
.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 0, 0.25);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-print-color-adjust: exact;
  color-adjust: exact;
  -webkit-transition: background-color 0.15s ease-in-out,
    background-position 0.15s ease-in-out, border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out,
    background-position 0.15s ease-in-out, border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out,
    background-position 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out,
    background-position 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-check-input {
    -webkit-transition: none;
    transition: none;
  }
}
.form-check-input[type="checkbox"] {
  border-radius: 0.25em;
}
.form-check-input[type="radio"] {
  border-radius: 50%;
}
.form-check-input:active {
  -webkit-filter: brightness(90%);
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #b1bbc4;
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
}
.form-check-input:checked {
  background-color: #525ce5;
  border-color: #525ce5;
}
.form-check-input:checked[type="checkbox"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.form-check-input:checked[type="radio"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.form-check-input[type="checkbox"]:indeterminate {
  background-color: #525ce5;
  border-color: #525ce5;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  -webkit-filter: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input:disabled ~ .form-check-label,
.form-check-input[disabled] ~ .form-check-label {
  opacity: 0.5;
}
.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  width: 2em;
  margin-left: -2.5em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left center;
  border-radius: 2em;
  -webkit-transition: background-position 0.15s ease-in-out;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    -webkit-transition: none;
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23b1bbc4'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}
.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check:disabled + .btn,
.btn-check[disabled] + .btn {
  pointer-events: none;
  -webkit-filter: none;
  filter: none;
  opacity: 0.65;
}
.form-range {
  width: 100%;
  height: 1.3rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  -webkit-box-shadow: 0 0 0 1px #f5f7fa, none;
  box-shadow: 0 0 0 1px #f5f7fa, none;
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #f5f7fa, none;
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #525ce5;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: #cbcef7;
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #eaedf1;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #525ce5;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: #cbcef7;
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #eaedf1;
  border-color: transparent;
  border-radius: 1rem;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.form-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}
.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 0.75rem;
  pointer-events: none;
  border: 1px solid transparent;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transition: opacity 0.1s ease-in-out,
    -webkit-transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, -webkit-transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out,
    -webkit-transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    -webkit-transition: none;
    transition: none;
  }
}
.form-floating > .form-control {
  padding: 1rem 0.75rem;
}
.form-floating > .form-control::-webkit-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control:-ms-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::-ms-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::placeholder {
  color: transparent;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-ms-input-placeholder) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus,
.form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:not(:-ms-input-placeholder) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  -webkit-transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.input-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus {
  z-index: 3;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 3;
}
.input-group-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.47rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #edf1f5;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}
.input-group-lg > .btn,
.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text {
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  border-radius: 0.4rem;
}
.input-group-sm > .btn,
.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text {
  padding: 0.25rem 0.5rem;
  font-size: 0.76563rem;
  border-radius: 0.2rem;
}
@media (min-width: 1200px) {
  .input-group-sm > .btn,
  .input-group-sm > .form-control,
  .input-group-sm > .form-select,
  .input-group-sm > .input-group-text {
    font-size: 0.76562rem;
  }
}
.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 2.5rem;
}
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3),
.input-group:not(.has-validation)
  > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4),
.input-group.has-validation
  > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group
  > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
    .valid-feedback
  ):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #23c58f;
}
.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.4rem 0.7rem;
  margin-top: 0.1rem;
  font-size: 0.76563rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(35, 197, 143, 0.9);
  border-radius: 0.25rem;
}
@media (min-width: 1200px) {
  .valid-tooltip {
    font-size: 0.76562rem;
  }
}
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip,
.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip {
  display: block;
}
.form-control.is-valid,
.was-validated .form-control:valid {
  border-color: #23c58f;
  padding-right: calc(1.5em + 0.94rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2323c58f' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.235rem) center;
  background-size: calc(0.75em + 0.47rem) calc(0.75em + 0.47rem);
}
.form-control.is-valid:focus,
.was-validated .form-control:valid:focus {
  border-color: #23c58f;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.25);
}
.was-validated textarea.form-control:valid,
textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.94rem);
  background-position: top calc(0.375em + 0.235rem) right
    calc(0.375em + 0.235rem);
}
.form-select.is-valid,
.was-validated .form-select:valid {
  border-color: #23c58f;
}
.form-select.is-valid:not([multiple]):not([size]),
.form-select.is-valid:not([multiple])[size="1"],
.was-validated .form-select:valid:not([multiple]):not([size]),
.was-validated .form-select:valid:not([multiple])[size="1"] {
  padding-right: calc(0.75em + 3.205rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"),
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2323c58f' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.5rem;
  background-size: 16px 12px, calc(0.75em + 0.47rem) calc(0.75em + 0.47rem);
}
.form-select.is-valid:focus,
.was-validated .form-select:valid:focus {
  border-color: #23c58f;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.25);
}
.form-check-input.is-valid,
.was-validated .form-check-input:valid {
  border-color: #23c58f;
}
.form-check-input.is-valid:checked,
.was-validated .form-check-input:valid:checked {
  background-color: #23c58f;
}
.form-check-input.is-valid:focus,
.was-validated .form-check-input:valid:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.25);
}
.form-check-input.is-valid ~ .form-check-label,
.was-validated .form-check-input:valid ~ .form-check-label {
  color: #23c58f;
}
.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}
.input-group .form-control.is-valid,
.input-group .form-select.is-valid,
.was-validated .input-group .form-control:valid,
.was-validated .input-group .form-select:valid {
  z-index: 1;
}
.input-group .form-control.is-valid:focus,
.input-group .form-select.is-valid:focus,
.was-validated .input-group .form-control:valid:focus,
.was-validated .input-group .form-select:valid:focus {
  z-index: 3;
}
.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #f14e4e;
}
.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.4rem 0.7rem;
  margin-top: 0.1rem;
  font-size: 0.76563rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(241, 78, 78, 0.9);
  border-radius: 0.25rem;
}
@media (min-width: 1200px) {
  .invalid-tooltip {
    font-size: 0.76562rem;
  }
}
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip,
.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip {
  display: block;
}
.form-control.is-invalid,
.was-validated .form-control:invalid {
  border-color: #f14e4e;
  padding-right: calc(1.5em + 0.94rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23f14e4e'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23f14e4e' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.235rem) center;
  background-size: calc(0.75em + 0.47rem) calc(0.75em + 0.47rem);
}
.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
  border-color: #f14e4e;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.25);
}
.was-validated textarea.form-control:invalid,
textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.94rem);
  background-position: top calc(0.375em + 0.235rem) right
    calc(0.375em + 0.235rem);
}
.form-select.is-invalid,
.was-validated .form-select:invalid {
  border-color: #f14e4e;
}
.form-select.is-invalid:not([multiple]):not([size]),
.form-select.is-invalid:not([multiple])[size="1"],
.was-validated .form-select:invalid:not([multiple]):not([size]),
.was-validated .form-select:invalid:not([multiple])[size="1"] {
  padding-right: calc(0.75em + 3.205rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"),
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23f14e4e'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23f14e4e' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.5rem;
  background-size: 16px 12px, calc(0.75em + 0.47rem) calc(0.75em + 0.47rem);
}
.form-select.is-invalid:focus,
.was-validated .form-select:invalid:focus {
  border-color: #f14e4e;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.25);
}
.form-check-input.is-invalid,
.was-validated .form-check-input:invalid {
  border-color: #f14e4e;
}
.form-check-input.is-invalid:checked,
.was-validated .form-check-input:invalid:checked {
  background-color: #f14e4e;
}
.form-check-input.is-invalid:focus,
.was-validated .form-check-input:invalid:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.25);
}
.form-check-input.is-invalid ~ .form-check-label,
.was-validated .form-check-input:invalid ~ .form-check-label {
  color: #f14e4e;
}
.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}
.input-group .form-control.is-invalid,
.input-group .form-select.is-invalid,
.was-validated .input-group .form-control:invalid,
.was-validated .input-group .form-select:invalid {
  z-index: 2;
}
.input-group .form-control.is-invalid:focus,
.input-group .form-select.is-invalid:focus,
.was-validated .input-group .form-control:invalid:focus,
.was-validated .input-group .form-select:invalid:focus {
  z-index: 3;
}
.btn {
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.47rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  -webkit-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    -webkit-transition: none;
    transition: none;
  }
}
.btn:hover {
  color: #495057;
  text-decoration: none;
}
.btn-check:focus + .btn,
.btn:focus {
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
}
.btn.disabled,
.btn:disabled,
fieldset:disabled .btn {
  pointer-events: none;
  opacity: 0.65;
}
.btn-primary {
  color: #fff;
  background-color: #525ce5;
  border-color: #525ce5;
}
.btn-primary:hover {
  color: #fff;
  background-color: #464ec3;
  border-color: #424ab7;
}
.btn-check:focus + .btn-primary,
.btn-primary:focus {
  color: #fff;
  background-color: #464ec3;
  border-color: #424ab7;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(108, 116, 233, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(108, 116, 233, 0.5);
}
.btn-check:active + .btn-primary,
.btn-check:checked + .btn-primary,
.btn-primary.active,
.btn-primary:active,
.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #424ab7;
  border-color: #3e45ac;
}
.btn-check:active + .btn-primary:focus,
.btn-check:checked + .btn-primary:focus,
.btn-primary.active:focus,
.btn-primary:active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(108, 116, 233, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(108, 116, 233, 0.5);
}
.btn-primary.disabled,
.btn-primary:disabled {
  color: #fff;
  background-color: #525ce5;
  border-color: #525ce5;
}
.btn-secondary {
  color: #fff;
  background-color: #74788d;
  border-color: #74788d;
}
.btn-secondary:hover {
  color: #fff;
  background-color: #636678;
  border-color: #5d6071;
}
.btn-check:focus + .btn-secondary,
.btn-secondary:focus {
  color: #fff;
  background-color: #636678;
  border-color: #5d6071;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(137, 140, 158, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(137, 140, 158, 0.5);
}
.btn-check:active + .btn-secondary,
.btn-check:checked + .btn-secondary,
.btn-secondary.active,
.btn-secondary:active,
.show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #5d6071;
  border-color: #575a6a;
}
.btn-check:active + .btn-secondary:focus,
.btn-check:checked + .btn-secondary:focus,
.btn-secondary.active:focus,
.btn-secondary:active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(137, 140, 158, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(137, 140, 158, 0.5);
}
.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #fff;
  background-color: #74788d;
  border-color: #74788d;
}
.btn-success {
  color: #fff;
  background-color: #23c58f;
  border-color: #23c58f;
}
.btn-success:hover {
  color: #fff;
  background-color: #1ea77a;
  border-color: #1c9e72;
}
.btn-check:focus + .btn-success,
.btn-success:focus {
  color: #fff;
  background-color: #1ea77a;
  border-color: #1c9e72;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(68, 206, 160, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(68, 206, 160, 0.5);
}
.btn-check:active + .btn-success,
.btn-check:checked + .btn-success,
.btn-success.active,
.btn-success:active,
.show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #1c9e72;
  border-color: #1a946b;
}
.btn-check:active + .btn-success:focus,
.btn-check:checked + .btn-success:focus,
.btn-success.active:focus,
.btn-success:active:focus,
.show > .btn-success.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(68, 206, 160, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(68, 206, 160, 0.5);
}
.btn-success.disabled,
.btn-success:disabled {
  color: #fff;
  background-color: #23c58f;
  border-color: #23c58f;
}
.btn-info {
  color: #fff;
  background-color: #5ba4e5;
  border-color: #5ba4e5;
}
.btn-info:hover {
  color: #fff;
  background-color: #4d8bc3;
  border-color: #4983b7;
}
.btn-check:focus + .btn-info,
.btn-info:focus {
  color: #fff;
  background-color: #4d8bc3;
  border-color: #4983b7;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(116, 178, 233, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(116, 178, 233, 0.5);
}
.btn-check:active + .btn-info,
.btn-check:checked + .btn-info,
.btn-info.active,
.btn-info:active,
.show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #4983b7;
  border-color: #447bac;
}
.btn-check:active + .btn-info:focus,
.btn-check:checked + .btn-info:focus,
.btn-info.active:focus,
.btn-info:active:focus,
.show > .btn-info.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(116, 178, 233, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(116, 178, 233, 0.5);
}
.btn-info.disabled,
.btn-info:disabled {
  color: #fff;
  background-color: #5ba4e5;
  border-color: #5ba4e5;
}
.btn-warning {
  color: #fff;
  background-color: #eeb148;
  border-color: #eeb148;
}
.btn-warning:hover {
  color: #fff;
  background-color: #ca963d;
  border-color: #be8e3a;
}
.btn-check:focus + .btn-warning,
.btn-warning:focus {
  color: #fff;
  background-color: #ca963d;
  border-color: #be8e3a;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(241, 189, 99, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(241, 189, 99, 0.5);
}
.btn-check:active + .btn-warning,
.btn-check:checked + .btn-warning,
.btn-warning.active,
.btn-warning:active,
.show > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #be8e3a;
  border-color: #b38536;
}
.btn-check:active + .btn-warning:focus,
.btn-check:checked + .btn-warning:focus,
.btn-warning.active:focus,
.btn-warning:active:focus,
.show > .btn-warning.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(241, 189, 99, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(241, 189, 99, 0.5);
}
.btn-warning.disabled,
.btn-warning:disabled {
  color: #fff;
  background-color: #eeb148;
  border-color: #eeb148;
}
.btn-danger {
  color: #fff;
  background-color: #f14e4e;
  border-color: #f14e4e;
}
.btn-danger:hover {
  color: #fff;
  background-color: #cd4242;
  border-color: #c13e3e;
}
.btn-check:focus + .btn-danger,
.btn-danger:focus {
  color: #fff;
  background-color: #cd4242;
  border-color: #c13e3e;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(243, 105, 105, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(243, 105, 105, 0.5);
}
.btn-check:active + .btn-danger,
.btn-check:checked + .btn-danger,
.btn-danger.active,
.btn-danger:active,
.show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #c13e3e;
  border-color: #b53b3b;
}
.btn-check:active + .btn-danger:focus,
.btn-check:checked + .btn-danger:focus,
.btn-danger.active:focus,
.btn-danger:active:focus,
.show > .btn-danger.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(243, 105, 105, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(243, 105, 105, 0.5);
}
.btn-danger.disabled,
.btn-danger:disabled {
  color: #fff;
  background-color: #f14e4e;
  border-color: #f14e4e;
}
.btn-pink {
  color: #fff;
  background-color: #e83e8c;
  border-color: #e83e8c;
}
.btn-pink:hover {
  color: #fff;
  background-color: #c53577;
  border-color: #ba3270;
}
.btn-check:focus + .btn-pink,
.btn-pink:focus {
  color: #fff;
  background-color: #c53577;
  border-color: #ba3270;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(235, 91, 157, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(235, 91, 157, 0.5);
}
.btn-check:active + .btn-pink,
.btn-check:checked + .btn-pink,
.btn-pink.active,
.btn-pink:active,
.show > .btn-pink.dropdown-toggle {
  color: #fff;
  background-color: #ba3270;
  border-color: #ae2f69;
}
.btn-check:active + .btn-pink:focus,
.btn-check:checked + .btn-pink:focus,
.btn-pink.active:focus,
.btn-pink:active:focus,
.show > .btn-pink.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(235, 91, 157, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(235, 91, 157, 0.5);
}
.btn-pink.disabled,
.btn-pink:disabled {
  color: #fff;
  background-color: #e83e8c;
  border-color: #e83e8c;
}
.btn-light {
  color: #000;
  background-color: #f9fafc;
  border-color: #f9fafc;
}
.btn-light:hover {
  color: #000;
  background-color: #fafbfc;
  border-color: #fafbfc;
}
.btn-check:focus + .btn-light,
.btn-light:focus {
  color: #000;
  background-color: #fafbfc;
  border-color: #fafbfc;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(212, 213, 214, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(212, 213, 214, 0.5);
}
.btn-check:active + .btn-light,
.btn-check:checked + .btn-light,
.btn-light.active,
.btn-light:active,
.show > .btn-light.dropdown-toggle {
  color: #000;
  background-color: #fafbfd;
  border-color: #fafbfc;
}
.btn-check:active + .btn-light:focus,
.btn-check:checked + .btn-light:focus,
.btn-light.active:focus,
.btn-light:active:focus,
.show > .btn-light.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(212, 213, 214, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(212, 213, 214, 0.5);
}
.btn-light.disabled,
.btn-light:disabled {
  color: #000;
  background-color: #f9fafc;
  border-color: #f9fafc;
}
.btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-dark:hover {
  color: #fff;
  background-color: #2c3136;
  border-color: #2a2e33;
}
.btn-check:focus + .btn-dark,
.btn-dark:focus {
  color: #fff;
  background-color: #2c3136;
  border-color: #2a2e33;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 88, 93, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(82, 88, 93, 0.5);
}
.btn-check:active + .btn-dark,
.btn-check:checked + .btn-dark,
.btn-dark.active,
.btn-dark:active,
.show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #2a2e33;
  border-color: #272c30;
}
.btn-check:active + .btn-dark:focus,
.btn-check:checked + .btn-dark:focus,
.btn-dark.active:focus,
.btn-dark:active:focus,
.show > .btn-dark.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 88, 93, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(82, 88, 93, 0.5);
}
.btn-dark.disabled,
.btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-outline-primary {
  color: #525ce5;
  border-color: #525ce5;
}
.btn-outline-primary:hover {
  color: #fff;
  background-color: #525ce5;
  border-color: #525ce5;
}
.btn-check:focus + .btn-outline-primary,
.btn-outline-primary:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.5);
}
.btn-check:active + .btn-outline-primary,
.btn-check:checked + .btn-outline-primary,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show,
.btn-outline-primary:active {
  color: #fff;
  background-color: #525ce5;
  border-color: #525ce5;
}
.btn-check:active + .btn-outline-primary:focus,
.btn-check:checked + .btn-outline-primary:focus,
.btn-outline-primary.active:focus,
.btn-outline-primary.dropdown-toggle.show:focus,
.btn-outline-primary:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.5);
}
.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  color: #525ce5;
  background-color: transparent;
}
.btn-outline-secondary {
  color: #74788d;
  border-color: #74788d;
}
.btn-outline-secondary:hover {
  color: #fff;
  background-color: #74788d;
  border-color: #74788d;
}
.btn-check:focus + .btn-outline-secondary,
.btn-outline-secondary:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(116, 120, 141, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(116, 120, 141, 0.5);
}
.btn-check:active + .btn-outline-secondary,
.btn-check:checked + .btn-outline-secondary,
.btn-outline-secondary.active,
.btn-outline-secondary.dropdown-toggle.show,
.btn-outline-secondary:active {
  color: #fff;
  background-color: #74788d;
  border-color: #74788d;
}
.btn-check:active + .btn-outline-secondary:focus,
.btn-check:checked + .btn-outline-secondary:focus,
.btn-outline-secondary.active:focus,
.btn-outline-secondary.dropdown-toggle.show:focus,
.btn-outline-secondary:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(116, 120, 141, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(116, 120, 141, 0.5);
}
.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  color: #74788d;
  background-color: transparent;
}
.btn-outline-success {
  color: #23c58f;
  border-color: #23c58f;
}
.btn-outline-success:hover {
  color: #fff;
  background-color: #23c58f;
  border-color: #23c58f;
}
.btn-check:focus + .btn-outline-success,
.btn-outline-success:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.5);
}
.btn-check:active + .btn-outline-success,
.btn-check:checked + .btn-outline-success,
.btn-outline-success.active,
.btn-outline-success.dropdown-toggle.show,
.btn-outline-success:active {
  color: #fff;
  background-color: #23c58f;
  border-color: #23c58f;
}
.btn-check:active + .btn-outline-success:focus,
.btn-check:checked + .btn-outline-success:focus,
.btn-outline-success.active:focus,
.btn-outline-success.dropdown-toggle.show:focus,
.btn-outline-success:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(35, 197, 143, 0.5);
}
.btn-outline-success.disabled,
.btn-outline-success:disabled {
  color: #23c58f;
  background-color: transparent;
}
.btn-outline-info {
  color: #5ba4e5;
  border-color: #5ba4e5;
}
.btn-outline-info:hover {
  color: #fff;
  background-color: #5ba4e5;
  border-color: #5ba4e5;
}
.btn-check:focus + .btn-outline-info,
.btn-outline-info:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(91, 164, 229, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(91, 164, 229, 0.5);
}
.btn-check:active + .btn-outline-info,
.btn-check:checked + .btn-outline-info,
.btn-outline-info.active,
.btn-outline-info.dropdown-toggle.show,
.btn-outline-info:active {
  color: #fff;
  background-color: #5ba4e5;
  border-color: #5ba4e5;
}
.btn-check:active + .btn-outline-info:focus,
.btn-check:checked + .btn-outline-info:focus,
.btn-outline-info.active:focus,
.btn-outline-info.dropdown-toggle.show:focus,
.btn-outline-info:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(91, 164, 229, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(91, 164, 229, 0.5);
}
.btn-outline-info.disabled,
.btn-outline-info:disabled {
  color: #5ba4e5;
  background-color: transparent;
}
.btn-outline-warning {
  color: #eeb148;
  border-color: #eeb148;
}
.btn-outline-warning:hover {
  color: #fff;
  background-color: #eeb148;
  border-color: #eeb148;
}
.btn-check:focus + .btn-outline-warning,
.btn-outline-warning:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(238, 177, 72, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(238, 177, 72, 0.5);
}
.btn-check:active + .btn-outline-warning,
.btn-check:checked + .btn-outline-warning,
.btn-outline-warning.active,
.btn-outline-warning.dropdown-toggle.show,
.btn-outline-warning:active {
  color: #fff;
  background-color: #eeb148;
  border-color: #eeb148;
}
.btn-check:active + .btn-outline-warning:focus,
.btn-check:checked + .btn-outline-warning:focus,
.btn-outline-warning.active:focus,
.btn-outline-warning.dropdown-toggle.show:focus,
.btn-outline-warning:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(238, 177, 72, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(238, 177, 72, 0.5);
}
.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
  color: #eeb148;
  background-color: transparent;
}
.btn-outline-danger {
  color: #f14e4e;
  border-color: #f14e4e;
}
.btn-outline-danger:hover {
  color: #fff;
  background-color: #f14e4e;
  border-color: #f14e4e;
}
.btn-check:focus + .btn-outline-danger,
.btn-outline-danger:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.5);
}
.btn-check:active + .btn-outline-danger,
.btn-check:checked + .btn-outline-danger,
.btn-outline-danger.active,
.btn-outline-danger.dropdown-toggle.show,
.btn-outline-danger:active {
  color: #fff;
  background-color: #f14e4e;
  border-color: #f14e4e;
}
.btn-check:active + .btn-outline-danger:focus,
.btn-check:checked + .btn-outline-danger:focus,
.btn-outline-danger.active:focus,
.btn-outline-danger.dropdown-toggle.show:focus,
.btn-outline-danger:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(241, 78, 78, 0.5);
}
.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
  color: #f14e4e;
  background-color: transparent;
}
.btn-outline-pink {
  color: #e83e8c;
  border-color: #e83e8c;
}
.btn-outline-pink:hover {
  color: #fff;
  background-color: #e83e8c;
  border-color: #e83e8c;
}
.btn-check:focus + .btn-outline-pink,
.btn-outline-pink:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(232, 62, 140, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(232, 62, 140, 0.5);
}
.btn-check:active + .btn-outline-pink,
.btn-check:checked + .btn-outline-pink,
.btn-outline-pink.active,
.btn-outline-pink.dropdown-toggle.show,
.btn-outline-pink:active {
  color: #fff;
  background-color: #e83e8c;
  border-color: #e83e8c;
}
.btn-check:active + .btn-outline-pink:focus,
.btn-check:checked + .btn-outline-pink:focus,
.btn-outline-pink.active:focus,
.btn-outline-pink.dropdown-toggle.show:focus,
.btn-outline-pink:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(232, 62, 140, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(232, 62, 140, 0.5);
}
.btn-outline-pink.disabled,
.btn-outline-pink:disabled {
  color: #e83e8c;
  background-color: transparent;
}
.btn-outline-light {
  color: #f9fafc;
  border-color: #f9fafc;
}
.btn-outline-light:hover {
  color: #000;
  background-color: #f9fafc;
  border-color: #f9fafc;
}
.btn-check:focus + .btn-outline-light,
.btn-outline-light:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(249, 250, 252, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(249, 250, 252, 0.5);
}
.btn-check:active + .btn-outline-light,
.btn-check:checked + .btn-outline-light,
.btn-outline-light.active,
.btn-outline-light.dropdown-toggle.show,
.btn-outline-light:active {
  color: #000;
  background-color: #f9fafc;
  border-color: #f9fafc;
}
.btn-check:active + .btn-outline-light:focus,
.btn-check:checked + .btn-outline-light:focus,
.btn-outline-light.active:focus,
.btn-outline-light.dropdown-toggle.show:focus,
.btn-outline-light:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(249, 250, 252, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(249, 250, 252, 0.5);
}
.btn-outline-light.disabled,
.btn-outline-light:disabled {
  color: #f9fafc;
  background-color: transparent;
}
.btn-outline-dark {
  color: #343a40;
  border-color: #343a40;
}
.btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-check:focus + .btn-outline-dark,
.btn-outline-dark:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(52, 58, 64, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(52, 58, 64, 0.5);
}
.btn-check:active + .btn-outline-dark,
.btn-check:checked + .btn-outline-dark,
.btn-outline-dark.active,
.btn-outline-dark.dropdown-toggle.show,
.btn-outline-dark:active {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}
.btn-check:active + .btn-outline-dark:focus,
.btn-check:checked + .btn-outline-dark:focus,
.btn-outline-dark.active:focus,
.btn-outline-dark.dropdown-toggle.show:focus,
.btn-outline-dark:active:focus {
  -webkit-box-shadow: 0 0 0 0.15rem rgba(52, 58, 64, 0.5);
  box-shadow: 0 0 0 0.15rem rgba(52, 58, 64, 0.5);
}
.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}
.btn-link {
  font-weight: 400;
  color: #525ce5;
  text-decoration: none;
}
.btn-link:hover {
  color: #1f2acc;
  text-decoration: underline;
}
.btn-link:focus {
  text-decoration: underline;
}
.btn-link.disabled,
.btn-link:disabled {
  color: #74788d;
}
.btn-group-lg > .btn,
.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1.09375rem;
  border-radius: 0.4rem;
}
.btn-group-sm > .btn,
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.76563rem;
  border-radius: 0.2rem;
}
@media (min-width: 1200px) {
  .btn-group-sm > .btn,
  .btn-sm {
    font-size: 0.76562rem;
  }
}
.fade {
  -webkit-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    -webkit-transition: none;
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}
.collapse:not(.show) {
  display: none;
}
.collapsing {
  height: 0;
  overflow: hidden;
  -webkit-transition: height 0.35s ease;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    -webkit-transition: none;
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  -webkit-transition: width 0.35s ease;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    -webkit-transition: none;
    transition: none;
  }
}
.dropdown,
.dropend,
.dropstart,
.dropup {
  position: relative;
}
.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-menu {
  position: absolute;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  font-size: 0.875rem;
  color: #495057;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 0 solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: 0.125rem;
}
.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}
.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}
@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .dropdown-menu-xxl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .dropdown-menu-xxl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}
.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}
.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #edf1f5;
}
.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.35rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:focus,
.dropdown-item:hover {
  color: #16181b;
  text-decoration: none;
  background-color: #f9fafc;
}
.dropdown-item.active,
.dropdown-item:active {
  color: #16181b;
  text-decoration: none;
  background-color: #f9fafc;
}
.dropdown-item.disabled,
.dropdown-item:disabled {
  color: #74788d;
  pointer-events: none;
  background-color: transparent;
}
.dropdown-menu.show {
  display: block;
}
.dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.76563rem;
  color: #74788d;
  white-space: nowrap;
}
@media (min-width: 1200px) {
  .dropdown-header {
    font-size: 0.76562rem;
  }
}
.dropdown-item-text {
  display: block;
  padding: 0.35rem 1.5rem;
  color: #212529;
}
.dropdown-menu-dark {
  color: #eaedf1;
  background-color: #343a40;
  border-color: rgba(0, 0, 0, 0.15);
}
.dropdown-menu-dark .dropdown-item {
  color: #eaedf1;
}
.dropdown-menu-dark .dropdown-item:focus,
.dropdown-menu-dark .dropdown-item:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.15);
}
.dropdown-menu-dark .dropdown-item.active,
.dropdown-menu-dark .dropdown-item:active {
  color: #16181b;
  background-color: #f9fafc;
}
.dropdown-menu-dark .dropdown-item.disabled,
.dropdown-menu-dark .dropdown-item:disabled {
  color: #adb5bd;
}
.dropdown-menu-dark .dropdown-divider {
  border-color: #edf1f5;
}
.dropdown-menu-dark .dropdown-item-text {
  color: #eaedf1;
}
.dropdown-menu-dark .dropdown-header {
  color: #adb5bd;
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group-vertical > .btn,
.btn-group > .btn {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn.active,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:hover,
.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn.active,
.btn-group > .btn:active,
.btn-group > .btn:focus,
.btn-group > .btn:hover {
  z-index: 1;
}
.btn-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}
.btn-group > .btn-group:not(:first-child),
.btn-group > .btn:not(:first-child) {
  margin-left: -1px;
}
.btn-group > .btn-group:not(:last-child) > .btn,
.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn:nth-child(n + 3),
.btn-group > :not(.btn-check) + .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
.dropdown-toggle-split::after,
.dropend .dropdown-toggle-split::after,
.dropup .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle-split::before {
  margin-right: 0;
}
.btn-group-sm > .btn + .dropdown-toggle-split,
.btn-sm + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}
.btn-group-lg > .btn + .dropdown-toggle-split,
.btn-lg + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}
.btn-group-vertical {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn-group:not(:first-child),
.btn-group-vertical > .btn:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn-group:not(:last-child) > .btn,
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child) > .btn,
.btn-group-vertical > .btn ~ .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: #525ce5;
  -webkit-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    -webkit-transition: none;
    transition: none;
  }
}
.nav-link:focus,
.nav-link:hover {
  color: #1f2acc;
  text-decoration: none;
}
.nav-link.disabled {
  color: #74788d;
  pointer-events: none;
  cursor: default;
}
.nav-tabs {
  border-bottom: 1px solid #ced4da;
}
.nav-tabs .nav-link {
  margin-bottom: -1px;
  background: 0 0;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: #edf1f5 #edf1f5 #ced4da;
  isolation: isolate;
}
.nav-tabs .nav-link.disabled {
  color: #74788d;
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  color: #495057;
  background-color: #fff;
  border-color: #ced4da #ced4da #fff;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.nav-pills .nav-link {
  background: 0 0;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #525ce5;
}
.nav-fill .nav-item,
.nav-fill > .nav-link {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}
.nav-justified .nav-item,
.nav-justified > .nav-link {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}
.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.navbar {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-lg,
.navbar > .container-md,
.navbar > .container-sm,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: inherit;
  flex-wrap: inherit;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: 0.33594rem;
  padding-bottom: 0.33594rem;
  margin-right: 1rem;
  font-size: 1.09375rem;
  white-space: nowrap;
}
.navbar-brand:focus,
.navbar-brand:hover {
  text-decoration: none;
}
.navbar-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}
.navbar-nav .dropdown-menu {
  position: static;
}
.navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.navbar-collapse {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.09375rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  -webkit-transition: -webkit-box-shadow 0.15s ease-in-out;
  transition: -webkit-box-shadow 0.15s ease-in-out;
  transition: box-shadow 0.15s ease-in-out;
  transition: box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    -webkit-transition: none;
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.15rem;
  box-shadow: 0 0 0 0.15rem;
}
.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}
.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}
@media (min-width: 576px) {
  .navbar-expand-sm {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible ;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    transform: none;
  }
  .navbar-expand-sm .offcanvas-bottom,
  .navbar-expand-sm .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-sm .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible ;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    transform: none;
  }
  .navbar-expand-md .offcanvas-bottom,
  .navbar-expand-md .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-md .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible ;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    transform: none;
  }
  .navbar-expand-lg .offcanvas-bottom,
  .navbar-expand-lg .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-lg .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible ;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    transform: none;
  }
  .navbar-expand-xl .offcanvas-bottom,
  .navbar-expand-xl .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xl .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    visibility: visible ;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    -webkit-transition: none;
    transition: none;
    -webkit-transform: none;
    transform: none;
  }
  .navbar-expand-xxl .offcanvas-bottom,
  .navbar-expand-xxl .offcanvas-top {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xxl .offcanvas-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: -webkit-box ;
  display: -ms-flexbox ;
  display: flex ;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas {
  position: inherit;
  bottom: 0;
  z-index: 1000;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  visibility: visible ;
  background-color: transparent;
  border-right: 0;
  border-left: 0;
  -webkit-transition: none;
  transition: none;
  -webkit-transform: none;
  transform: none;
}
.navbar-expand .offcanvas-bottom,
.navbar-expand .offcanvas-top {
  height: auto;
  border-top: 0;
  border-bottom: 0;
}
.navbar-expand .offcanvas-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}
.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.55);
}
.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
  color: rgba(0, 0, 0, 0.7);
}
.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .show > .nav-link {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.55);
  border-color: rgba(0, 0, 0, 0.1);
}
.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.55);
}
.navbar-light .navbar-text a,
.navbar-light .navbar-text a:focus,
.navbar-light .navbar-text a:hover {
  color: rgba(0, 0, 0, 0.9);
}
.navbar-dark .navbar-brand {
  color: #fff;
}
.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.55);
}
.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
  color: rgba(255, 255, 255, 0.75);
}
.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .show > .nav-link {
  color: #fff;
}
.navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.55);
  border-color: rgba(255, 255, 255, 0.1);
}
.navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.55);
}
.navbar-dark .navbar-text a,
.navbar-dark .navbar-text a:focus,
.navbar-dark .navbar-text a:hover {
  color: #fff;
}
.card {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 0 solid #eaedf1;
  border-radius: 0.25rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}
.card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1.25rem 1.25rem;
}
.card-title {
  margin-bottom: 0.5rem;
}
.card-subtitle {
  margin-top: -0.25rem;
  margin-bottom: 0;
}
.card-text:last-child {
  margin-bottom: 0;
}
.card-link:hover {
  text-decoration: none;
}
.card-link + .card-link {
  margin-left: 1.25rem;
}
.card-header {
  padding: 0.625rem 1.25rem;
  margin-bottom: 0;
  background-color: #eaedf1;
  border-bottom: 0 solid #eaedf1;
}
.card-header:first-child {
  border-radius: 0.25rem 0.25rem 0 0;
}
.card-footer {
  padding: 0.625rem 1.25rem;
  background-color: #eaedf1;
  border-top: 0 solid #eaedf1;
}
.card-footer:last-child {
  border-radius: 0 0 0.25rem 0.25rem;
}
.card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.625rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}
.card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}
.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1rem;
  border-radius: 0.25rem;
}
.card-img,
.card-img-bottom,
.card-img-top {
  width: 100%;
}
.card-img,
.card-img-top {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.card-img,
.card-img-bottom {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.card-group > .card {
  margin-bottom: 12px;
}
@media (min-width: 576px) {
  .card-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
  }
  .card-group > .card {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-header,
  .card-group > .card:not(:last-child) .card-img-top {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-footer,
  .card-group > .card:not(:last-child) .card-img-bottom {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-header,
  .card-group > .card:not(:first-child) .card-img-top {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-footer,
  .card-group > .card:not(:first-child) .card-img-bottom {
    border-bottom-left-radius: 0;
  }
}
.accordion-button {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 0.875rem;
  color: #495057;
  text-align: left;
  background-color: transparent;
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  -webkit-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    border-radius 0.15s ease, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, border-radius 0.15s ease,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    border-radius 0.15s ease;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    border-radius 0.15s ease, -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    -webkit-transition: none;
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: #4a53ce;
  background-color: #eeeffc;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
}
.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%234a53ce'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}
.accordion-button::after {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-left: auto;
  content: "";
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23495057'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 16px;
  -webkit-transition: -webkit-transform 0.2s ease-in-out;
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    -webkit-transition: none;
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: #b1bbc4;
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.accordion-header {
  margin-bottom: 0;
}
.accordion-item {
  background-color: transparent;
  border: 1px solid rgba(0, 0, 0, 0.125);
}
.accordion-item:first-of-type {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.accordion-body {
  padding: 1rem 1.25rem;
}
.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button {
  border-radius: 0;
}
.breadcrumb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #edf1f5;
  border-radius: 0.25rem;
}
.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: 0.5rem;
  color: #74788d;
  content: var(--bs-breadcrumb-divider, "/");
}
.breadcrumb-item.active {
  color: #eaedf1;
}
.pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
}
.page-link {
  position: relative;
  display: block;
  color: #74788d;
  background-color: #fff;
  border: 1px solid #ced4da;
  -webkit-transition: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out,
    -webkit-box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    -webkit-transition: none;
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: #1f2acc;
  text-decoration: none;
  background-color: #edf1f5;
  border-color: #ced4da;
}
.page-link:focus {
  z-index: 3;
  color: #1f2acc;
  background-color: #edf1f5;
  outline: 0;
  -webkit-box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
  box-shadow: 0 0 0 0.15rem rgba(82, 92, 229, 0.25);
}
.page-item:not(:first-child) .page-link {
  margin-left: -1px;
}
.page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #525ce5;
  border-color: #525ce5;
}
.page-item.disabled .page-link {
  color: #ced4da;
  pointer-events: none;
  background-color: #fff;
  border-color: #ced4da;
}
.page-link {
  padding: 0.5rem 0.75rem;
}
.page-item:first-child .page-link {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.09375rem;
}
.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.4rem;
  border-bottom-left-radius: 0.4rem;
}
.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.4rem;
  border-bottom-right-radius: 0.4rem;
}
.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.76563rem;
}
@media (min-width: 1200px) {
  .pagination-sm .page-link {
    font-size: 0.76562rem;
  }
}
.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}
.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 500;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.alert-heading {
  color: inherit;
}
.alert-link {
  font-weight: 600;
}
.alert-dismissible {
  padding-right: 3.75rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 0.9375rem 1.25rem;
}
.alert-primary {
  color: #313789;
  background-color: #dcdefa;
  border-color: #cbcef7;
}
.alert-primary .alert-link {
  color: #272c6e;
}
.alert-secondary {
  color: #464855;
  background-color: #e3e4e8;
  border-color: #d5d7dd;
}
.alert-secondary .alert-link {
  color: #383a44;
}
.alert-success {
  color: #157656;
  background-color: #d3f3e9;
  border-color: #bdeedd;
}
.alert-success .alert-link {
  color: #115e45;
}
.alert-info {
  color: #376289;
  background-color: #deedfa;
  border-color: #cee4f7;
}
.alert-info .alert-link {
  color: #2c4e6e;
}
.alert-warning {
  color: #8f6a2b;
  background-color: #fcefda;
  border-color: #fae8c8;
}
.alert-warning .alert-link {
  color: #725522;
}
.alert-danger {
  color: #912f2f;
  background-color: #fcdcdc;
  border-color: #fbcaca;
}
.alert-danger .alert-link {
  color: #742626;
}
.alert-pink {
  color: #8b2554;
  background-color: #fad8e8;
  border-color: #f8c5dd;
}
.alert-pink .alert-link {
  color: #6f1e43;
}
.alert-light {
  color: #959697;
  background-color: #fefefe;
  border-color: #fdfefe;
}
.alert-light .alert-link {
  color: #777879;
}
.alert-dark {
  color: #1f2326;
  background-color: #d6d8d9;
  border-color: #c2c4c6;
}
.alert-dark .alert-link {
  color: #191c1e;
}
@-webkit-keyframes progress-bar-stripes {
  0% {
    background-position-x: 0.625rem;
  }
}
@keyframes progress-bar-stripes {
  0% {
    background-position-x: 0.625rem;
  }
}
.progress {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 0.625rem;
  overflow: hidden;
  font-size: 0.65625rem;
  background-color: #eaedf1;
  border-radius: 0.25rem;
}
.progress-bar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #525ce5;
  -webkit-transition: width 0.6s ease;
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    -webkit-transition: none;
    transition: none;
  }
}
.progress-bar-striped {
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );
  background-size: 0.625rem 0.625rem;
}
.progress-bar-animated {
  -webkit-animation: 1s linear infinite progress-bar-stripes;
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    -webkit-animation: none;
    animation: none;
  }
}
.list-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: 0.25rem;
}
.list-group-numbered {
  list-style-type: none;
  counter-reset: section;
}
.list-group-numbered > li::before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}
.list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}
.list-group-item-action:focus,
.list-group-item-action:hover {
  z-index: 1;
  color: #495057;
  text-decoration: none;
  background-color: #f9fafc;
}
.list-group-item-action:active {
  color: #495057;
  background-color: #edf1f5;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  color: #212529;
  background-color: #fff;
  border: 1px solid #edf1f5;
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled,
.list-group-item:disabled {
  color: #74788d;
  pointer-events: none;
  background-color: #fff;
}
.list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #525ce5;
  border-color: #525ce5;
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}
.list-group-horizontal {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child {
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child {
  border-top-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: 1px;
  border-left-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px;
  border-left-width: 1px;
}
@media (min-width: 576px) {
  .list-group-horizontal-sm {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .list-group-horizontal-xxl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-top-width: 1px;
    border-left-width: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
    border-left-width: 1px;
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}
.list-group-item-primary {
  color: #313789;
  background-color: #dcdefa;
}
.list-group-item-primary.list-group-item-action:focus,
.list-group-item-primary.list-group-item-action:hover {
  color: #313789;
  background-color: #c6c8e1;
}
.list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #313789;
  border-color: #313789;
}
.list-group-item-secondary {
  color: #464855;
  background-color: #e3e4e8;
}
.list-group-item-secondary.list-group-item-action:focus,
.list-group-item-secondary.list-group-item-action:hover {
  color: #464855;
  background-color: #cccdd1;
}
.list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #464855;
  border-color: #464855;
}
.list-group-item-success {
  color: #157656;
  background-color: #d3f3e9;
}
.list-group-item-success.list-group-item-action:focus,
.list-group-item-success.list-group-item-action:hover {
  color: #157656;
  background-color: #bedbd2;
}
.list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #157656;
  border-color: #157656;
}
.list-group-item-info {
  color: #376289;
  background-color: #deedfa;
}
.list-group-item-info.list-group-item-action:focus,
.list-group-item-info.list-group-item-action:hover {
  color: #376289;
  background-color: #c8d5e1;
}
.list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #376289;
  border-color: #376289;
}
.list-group-item-warning {
  color: #8f6a2b;
  background-color: #fcefda;
}
.list-group-item-warning.list-group-item-action:focus,
.list-group-item-warning.list-group-item-action:hover {
  color: #8f6a2b;
  background-color: #e3d7c4;
}
.list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #8f6a2b;
  border-color: #8f6a2b;
}
.list-group-item-danger {
  color: #912f2f;
  background-color: #fcdcdc;
}
.list-group-item-danger.list-group-item-action:focus,
.list-group-item-danger.list-group-item-action:hover {
  color: #912f2f;
  background-color: #e3c6c6;
}
.list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #912f2f;
  border-color: #912f2f;
}
.list-group-item-pink {
  color: #8b2554;
  background-color: #fad8e8;
}
.list-group-item-pink.list-group-item-action:focus,
.list-group-item-pink.list-group-item-action:hover {
  color: #8b2554;
  background-color: #e1c2d1;
}
.list-group-item-pink.list-group-item-action.active {
  color: #fff;
  background-color: #8b2554;
  border-color: #8b2554;
}
.list-group-item-light {
  color: #959697;
  background-color: #fefefe;
}
.list-group-item-light.list-group-item-action:focus,
.list-group-item-light.list-group-item-action:hover {
  color: #959697;
  background-color: #e5e5e5;
}
.list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #959697;
  border-color: #959697;
}
.list-group-item-dark {
  color: #1f2326;
  background-color: #d6d8d9;
}
.list-group-item-dark.list-group-item-action:focus,
.list-group-item-dark.list-group-item-action:hover {
  color: #1f2326;
  background-color: #c1c2c3;
}
.list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #1f2326;
  border-color: #1f2326;
}
.btn-close {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: #000;
  background: transparent
    url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e")
    center/1em auto no-repeat;
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
}
.btn-close:hover {
  color: #000;
  text-decoration: none;
  opacity: 0.75;
}
.btn-close:focus {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  opacity: 1;
}
.btn-close.disabled,
.btn-close:disabled {
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  opacity: 0.25;
}
.btn-close-white {
  -webkit-filter: invert(1) grayscale(100%) brightness(200%);
  filter: invert(1) grayscale(100%) brightness(200%);
}
.toast {
  width: 350px;
  max-width: 100%;
  font-size: 0.875rem;
  pointer-events: auto;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  border-radius: 0.25rem;
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}
.toast-container {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.toast-container > :not(:last-child) {
  margin-bottom: 12px;
}
.toast-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #74788d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.toast-header .btn-close {
  margin-right: -0.375rem;
  margin-left: 0.75rem;
}
.toast-body {
  padding: 0.75rem;
  word-wrap: break-word;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1055;
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -50px);
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    -webkit-transition: none;
    transition: none;
  }
}
.modal.show .modal-dialog {
  -webkit-transform: none;
  transform: none;
}
.modal.modal-static .modal-dialog {
  -webkit-transform: scale(1.02);
  transform: scale(1.02);
}
.modal-dialog-scrollable {
  height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}
.modal-dialog-centered {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - 1rem);
}
.modal-content {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #eaedf1;
  border-radius: 0.4rem;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.5;
}
.modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #edf1f5;
  border-top-left-radius: calc(0.4rem - 1px);
  border-top-right-radius: calc(0.4rem - 1px);
}
.modal-header .btn-close {
  padding: 0.5rem 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
}
.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}
.modal-body {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem;
}
.modal-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #edf1f5;
  border-bottom-right-radius: calc(0.4rem - 1px);
  border-bottom-left-radius: calc(0.4rem - 1px);
}
.modal-footer > * {
  margin: 0.25rem;
}
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog-scrollable {
    height: calc(100% - 3.5rem);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}
.modal-fullscreen .modal-footer {
  border-radius: 0;
}
@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
}
.tooltip {
  position: absolute;
  z-index: 1080;
  display: block;
  margin: 0;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.76563rem;
  word-wrap: break-word;
  opacity: 0;
}
@media (min-width: 1200px) {
  .tooltip {
    font-size: 0.76562rem;
  }
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .tooltip-arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}
.bs-tooltip-auto[data-popper-placement^="top"],
.bs-tooltip-top {
  padding: 0.4rem 0;
}
.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow,
.bs-tooltip-top .tooltip-arrow {
  bottom: 0;
}
.bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before,
.bs-tooltip-top .tooltip-arrow::before {
  top: -1px;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}
.bs-tooltip-auto[data-popper-placement^="right"],
.bs-tooltip-end {
  padding: 0 0.4rem;
}
.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow,
.bs-tooltip-end .tooltip-arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before,
.bs-tooltip-end .tooltip-arrow::before {
  right: -1px;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}
.bs-tooltip-auto[data-popper-placement^="bottom"],
.bs-tooltip-bottom {
  padding: 0.4rem 0;
}
.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow,
.bs-tooltip-bottom .tooltip-arrow {
  top: 0;
}
.bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before,
.bs-tooltip-bottom .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}
.bs-tooltip-auto[data-popper-placement^="left"],
.bs-tooltip-start {
  padding: 0 0.4rem;
}
.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow,
.bs-tooltip-start .tooltip-arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before,
.bs-tooltip-start .tooltip-arrow::before {
  left: -1px;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}
.tooltip-inner {
  max-width: 200px;
  padding: 0.4rem 0.7rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1070;
  display: block;
  max-width: 276px;
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.76563rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #edf1f5;
  border-radius: 0.4rem;
}
@media (min-width: 1200px) {
  .popover {
    font-size: 0.76562rem;
  }
}
.popover .popover-arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
}
.popover .popover-arrow::after,
.popover .popover-arrow::before {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow,
.bs-popover-top > .popover-arrow {
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before,
.bs-popover-top > .popover-arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #edf1f5;
}
.bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after,
.bs-popover-top > .popover-arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow,
.bs-popover-end > .popover-arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before,
.bs-popover-end > .popover-arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #edf1f5;
}
.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after,
.bs-popover-end > .popover-arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow,
.bs-popover-bottom > .popover-arrow {
  top: calc(-0.5rem - 1px);
}
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before,
.bs-popover-bottom > .popover-arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #edf1f5;
}
.bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after,
.bs-popover-bottom > .popover-arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}
.bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before,
.bs-popover-bottom .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7;
}
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow,
.bs-popover-start > .popover-arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before,
.bs-popover-start > .popover-arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #edf1f5;
}
.bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after,
.bs-popover-start > .popover-arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}
.popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #edf1f5;
  border-top-left-radius: calc(0.4rem - 1px);
  border-top-right-radius: calc(0.4rem - 1px);
}
.popover-header:empty {
  display: none;
}
.popover-body {
  padding: 0.5rem 0.75rem;
  color: #495057;
}
.carousel {
  position: relative;
}
.carousel.pointer-event {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}
.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}
.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: -webkit-transform 0.6s ease-in-out;
  transition: -webkit-transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    -webkit-transition: none;
    transition: none;
  }
}
.carousel-item-next,
.carousel-item-prev,
.carousel-item.active {
  display: block;
}
.active.carousel-item-end,
.carousel-item-next:not(.carousel-item-start) {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}
.active.carousel-item-start,
.carousel-item-prev:not(.carousel-item-end) {
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}
.carousel-fade .carousel-item {
  opacity: 0;
  -webkit-transition-property: opacity;
  transition-property: opacity;
  -webkit-transform: none;
  transform: none;
}
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end,
.carousel-fade .carousel-item.active {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-end,
.carousel-fade .active.carousel-item-start {
  z-index: 0;
  opacity: 0;
  -webkit-transition: opacity 0s 0.6s;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-end,
  .carousel-fade .active.carousel-item-start {
    -webkit-transition: none;
    transition: none;
  }
}
.carousel-control-next,
.carousel-control-prev {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: 0 0;
  border: 0;
  opacity: 0.5;
  -webkit-transition: opacity 0.15s ease;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-next,
  .carousel-control-prev {
    -webkit-transition: none;
    transition: none;
  }
}
.carousel-control-next:focus,
.carousel-control-next:hover,
.carousel-control-prev:focus,
.carousel-control-prev:hover {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}
.carousel-control-prev {
  left: 0;
}
.carousel-control-next {
  right: 0;
}
.carousel-control-next-icon,
.carousel-control-prev-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}
.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators [data-bs-target] {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  -webkit-box-flex: 0;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  -webkit-transition: opacity 0.6s ease;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    -webkit-transition: none;
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}
.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #fff;
  text-align: center;
}
.carousel-dark .carousel-control-next-icon,
.carousel-dark .carousel-control-prev-icon {
  -webkit-filter: invert(1) grayscale(100);
  filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}
.carousel-dark .carousel-caption {
  color: #000;
}
@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: 0.75s linear infinite spinner-border;
  animation: 0.75s linear infinite spinner-border;
}
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}
@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: 0.75s linear infinite spinner-grow;
  animation: 0.75s linear infinite spinner-grow;
}
.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}
@media (prefers-reduced-motion: reduce) {
  .spinner-border,
  .spinner-grow {
    -webkit-animation-duration: 1.5s;
    animation-duration: 1.5s;
  }
}
.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: 1045;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 100%;
  visibility: hidden;
  background-color: #fff;
  background-clip: padding-box;
  outline: 0;
  -webkit-transition: -webkit-transform 0.3s ease-in-out;
  transition: -webkit-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, -webkit-transform 0.3s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    -webkit-transition: none;
    transition: none;
  }
}
.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.5;
}
.offcanvas-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem;
}
.offcanvas-header .btn-close {
  padding: 0.5rem 0.5rem;
  margin-top: -0.5rem;
  margin-right: -0.5rem;
  margin-bottom: -0.5rem;
}
.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5;
}
.offcanvas-body {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  padding: 1rem 1rem;
  overflow-y: auto;
}
.offcanvas-start {
  top: 0;
  left: 0;
  width: 400px;
  border-right: 1px solid #eaedf1;
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}
.offcanvas-end {
  top: 0;
  right: 0;
  width: 400px;
  border-left: 1px solid #eaedf1;
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}
.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-bottom: 1px solid #eaedf1;
  -webkit-transform: translateY(-100%);
  transform: translateY(-100%);
}
.offcanvas-bottom {
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-top: 1px solid #eaedf1;
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
}
.offcanvas.show {
  -webkit-transform: none;
  transform: none;
}
.placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentColor;
  opacity: 0.5;
}
.placeholder.btn::before {
  display: inline-block;
  content: "";
}
.placeholder-xs {
  min-height: 0.6em;
}
.placeholder-sm {
  min-height: 0.8em;
}
.placeholder-lg {
  min-height: 1.2em;
}
.placeholder-glow .placeholder {
  -webkit-animation: placeholder-glow 2s ease-in-out infinite;
  animation: placeholder-glow 2s ease-in-out infinite;
}
@-webkit-keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
.placeholder-wave {
  -webkit-mask-image: linear-gradient(
    130deg,
    #000 55%,
    rgba(0, 0, 0, 0.8) 75%,
    #000 95%
  );
  mask-image: linear-gradient(
    130deg,
    #000 55%,
    rgba(0, 0, 0, 0.8) 75%,
    #000 95%
  );
  -webkit-mask-size: 200% 100%;
  mask-size: 200% 100%;
  -webkit-animation: placeholder-wave 2s linear infinite;
  animation: placeholder-wave 2s linear infinite;
}
@-webkit-keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0;
    mask-position: -200% 0;
  }
}
@keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0;
    mask-position: -200% 0;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}
.link-primary {
  color: #525ce5;
}
.link-primary:focus,
.link-primary:hover {
  color: #424ab7;
}
.link-secondary {
  color: #74788d;
}
.link-secondary:focus,
.link-secondary:hover {
  color: #5d6071;
}
.link-success {
  color: #23c58f;
}
.link-success:focus,
.link-success:hover {
  color: #1c9e72;
}
.link-info {
  color: #5ba4e5;
}
.link-info:focus,
.link-info:hover {
  color: #4983b7;
}
.link-warning {
  color: #eeb148;
}
.link-warning:focus,
.link-warning:hover {
  color: #be8e3a;
}
.link-danger {
  color: #f14e4e;
}
.link-danger:focus,
.link-danger:hover {
  color: #c13e3e;
}
.link-pink {
  color: #e83e8c;
}
.link-pink:focus,
.link-pink:hover {
  color: #ba3270;
}
.link-light {
  color: #f9fafc;
}
.link-light:focus,
.link-light:hover {
  color: #fafbfd;
}
.link-dark {
  color: #343a40;
}
.link-dark:focus,
.link-dark:hover {
  color: #2a2e33;
}
.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}
.ratio-4x3 {
  --bs-aspect-ratio: calc(3 / 4 * 100%);
}
.ratio-16x9 {
  --bs-aspect-ratio: calc(9 / 16 * 100%);
}
.ratio-21x9 {
  --bs-aspect-ratio: calc(9 / 21 * 100%);
}
.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}
.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}
.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}
@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
.hstack {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}
.vstack {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}
.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute ;
  width: 1px ;
  height: 1px ;
  padding: 0 ;
  margin: -1px ;
  overflow: hidden ;
  clip: rect(0, 0, 0, 0) ;
  white-space: nowrap ;
  border: 0 ;
}
.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.vr {
  display: inline-block;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentColor;
  opacity: 0.7;
}
.align-baseline {
  vertical-align: baseline ;
}
.align-top {
  vertical-align: top ;
}
.align-middle {
  vertical-align: middle ;
}
.align-bottom {
  vertical-align: bottom ;
}
.align-text-bottom {
  vertical-align: text-bottom ;
}
.align-text-top {
  vertical-align: text-top ;
}
.float-start {
  float: left ;
}
.float-end {
  float: right ;
}
.float-none {
  float: none ;
}
.opacity-0 {
  opacity: 0 ;
}
.opacity-25 {
  opacity: 0.25 ;
}
.opacity-50 {
  opacity: 0.5 ;
}
.opacity-75 {
  opacity: 0.75 ;
}
.opacity-100 {
  opacity: 1 ;
}
.overflow-auto {
  overflow: auto ;
}
.overflow-hidden {
  overflow: hidden ;
}
.overflow-visible {
  overflow: visible ;
}
.overflow-scroll {
  overflow: scroll ;
}
.d-inline {
  display: inline ;
}
.d-inline-block {
  display: inline-block ;
}
.d-block {
  display: block ;
}
.d-grid {
  display: grid ;
}
.d-table {
  display: table ;
}
.d-table-row {
  display: table-row ;
}
.d-table-cell {
  display: table-cell ;
}
.d-flex {
  display: -webkit-box ;
  display: -ms-flexbox ;
  display: flex ;
}
.d-inline-flex {
  display: -webkit-inline-box ;
  display: -ms-inline-flexbox ;
  display: inline-flex ;
}
.d-none {
  display: none ;
}
.shadow {
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1) ;
  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1) ;
}
.shadow-sm {
  -webkit-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) ;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) ;
}
.shadow-lg {
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) ;
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) ;
}
.shadow-none {
  -webkit-box-shadow: none ;
  box-shadow: none ;
}
.position-static {
  position: static ;
}
.position-relative {
  position: relative ;
}
.position-absolute {
  position: absolute ;
}
.position-fixed {
  position: fixed ;
}
.position-sticky {
  position: sticky ;
}
.top-0 {
  top: 0 ;
}
.top-50 {
  top: 50% ;
}
.top-100 {
  top: 100% ;
}
.bottom-0 {
  bottom: 0 ;
}
.bottom-50 {
  bottom: 50% ;
}
.bottom-100 {
  bottom: 100% ;
}
.start-0 {
  left: 0 ;
}
.start-50 {
  left: 50% ;
}
.start-100 {
  left: 100% ;
}
.end-0 {
  right: 0 ;
}
.end-50 {
  right: 50% ;
}
.end-100 {
  right: 100% ;
}
.translate-middle {
  -webkit-transform: translate(-50%, -50%) ;
  transform: translate(-50%, -50%) ;
}
.translate-middle-x {
  -webkit-transform: translateX(-50%) ;
  transform: translateX(-50%) ;
}
.translate-middle-y {
  -webkit-transform: translateY(-50%) ;
  transform: translateY(-50%) ;
}
.border {
  border: 1px solid #edf1f5 ;
}
.border-0 {
  border: 0 ;
}
.border-top {
  border-top: 1px solid #edf1f5 ;
}
.border-top-0 {
  border-top: 0 ;
}
.border-end {
  border-right: 1px solid #edf1f5 ;
}
.border-end-0 {
  border-right: 0 ;
}
.border-bottom {
  border-bottom: 1px solid #edf1f5 ;
}
.border-bottom-0 {
  border-bottom: 0 ;
}
.border-start {
  border-left: 1px solid #edf1f5 ;
}
.border-start-0 {
  border-left: 0 ;
}
.border-primary {
  border-color: #525ce5 ;
}
.border-secondary {
  border-color: #74788d ;
}
.border-success {
  border-color: #23c58f ;
}
.border-info {
  border-color: #5ba4e5 ;
}
.border-warning {
  border-color: #eeb148 ;
}
.border-danger {
  border-color: #f14e4e ;
}
.border-pink {
  border-color: #e83e8c ;
}
.border-light {
  border-color: #f9fafc ;
}
.border-dark {
  border-color: #343a40 ;
}
.border-white {
  border-color: #fff ;
}
.border-0 {
  border-width: 0 ;
}
.border-1 {
  border-width: 1px ;
}
.border-2 {
  border-width: 2px ;
}
.border-3 {
  border-width: 3px ;
}
.border-4 {
  border-width: 4px ;
}
.border-5 {
  border-width: 5px ;
}
.w-25 {
  width: 25% ;
}
.w-50 {
  width: 50% ;
}
.w-75 {
  width: 75% ;
}
.w-100 {
  width: 100% ;
}
.w-auto {
  width: auto ;
}
.mw-100 {
  max-width: 100% ;
}
.vw-100 {
  width: 100vw ;
}
.min-vw-100 {
  min-width: 100vw ;
}
.h-25 {
  height: 25% ;
}
.h-50 {
  height: 50% ;
}
.h-75 {
  height: 75% ;
}
.h-100 {
  height: 100% ;
}
.h-auto {
  height: auto ;
}
.mh-100 {
  max-height: 100% ;
}
.vh-100 {
  height: 100vh ;
}
.min-vh-100 {
  min-height: 100vh ;
}
.flex-fill {
  -webkit-box-flex: 1 ;
  -ms-flex: 1 1 auto ;
  flex: 1 1 auto ;
}
.flex-row {
  -webkit-box-orient: horizontal ;
  -webkit-box-direction: normal ;
  -ms-flex-direction: row ;
  flex-direction: row ;
}
.flex-column {
  -webkit-box-orient: vertical ;
  -webkit-box-direction: normal ;
  -ms-flex-direction: column ;
  flex-direction: column ;
}
.flex-row-reverse {
  -webkit-box-orient: horizontal ;
  -webkit-box-direction: reverse ;
  -ms-flex-direction: row-reverse ;
  flex-direction: row-reverse ;
}
.flex-column-reverse {
  -webkit-box-orient: vertical ;
  -webkit-box-direction: reverse ;
  -ms-flex-direction: column-reverse ;
  flex-direction: column-reverse ;
}
.flex-grow-0 {
  -webkit-box-flex: 0 ;
  -ms-flex-positive: 0 ;
  flex-grow: 0 ;
}
.flex-grow-1 {
  -webkit-box-flex: 1 ;
  -ms-flex-positive: 1 ;
  flex-grow: 1 ;
}
.flex-shrink-0 {
  -ms-flex-negative: 0 ;
  flex-shrink: 0 ;
}
.flex-shrink-1 {
  -ms-flex-negative: 1 ;
  flex-shrink: 1 ;
}
.flex-wrap {
  -ms-flex-wrap: wrap ;
  flex-wrap: wrap ;
}
.flex-nowrap {
  -ms-flex-wrap: nowrap ;
  flex-wrap: nowrap ;
}
.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse ;
  flex-wrap: wrap-reverse ;
}
.gap-0 {
  gap: 0 ;
}
.gap-1 {
  gap: 0.25rem ;
}
.gap-2 {
  gap: 0.5rem ;
}
.gap-3 {
  gap: 1rem ;
}
.gap-4 {
  gap: 1.5rem ;
}
.gap-5 {
  gap: 3rem ;
}
.justify-content-start {
  -webkit-box-pack: start ;
  -ms-flex-pack: start ;
  justify-content: flex-start ;
}
.justify-content-end {
  -webkit-box-pack: end ;
  -ms-flex-pack: end ;
  justify-content: flex-end ;
}
.justify-content-center {
  -webkit-box-pack: center ;
  -ms-flex-pack: center ;
  justify-content: center ;
}
.justify-content-between {
  -webkit-box-pack: justify ;
  -ms-flex-pack: justify ;
  justify-content: space-between ;
}
.justify-content-around {
  -ms-flex-pack: distribute ;
  justify-content: space-around ;
}
.justify-content-evenly {
  -webkit-box-pack: space-evenly ;
  -ms-flex-pack: space-evenly ;
  justify-content: space-evenly ;
}
.align-items-start {
  -webkit-box-align: start ;
  -ms-flex-align: start ;
  align-items: flex-start ;
}
.align-items-end {
  -webkit-box-align: end ;
  -ms-flex-align: end ;
  align-items: flex-end ;
}
.align-items-center {
  -webkit-box-align: center ;
  -ms-flex-align: center ;
  align-items: center ;
}
.align-items-baseline {
  -webkit-box-align: baseline ;
  -ms-flex-align: baseline ;
  align-items: baseline ;
}
.align-items-stretch {
  -webkit-box-align: stretch ;
  -ms-flex-align: stretch ;
  align-items: stretch ;
}
.align-content-start {
  -ms-flex-line-pack: start ;
  align-content: flex-start ;
}
.align-content-end {
  -ms-flex-line-pack: end ;
  align-content: flex-end ;
}
.align-content-center {
  -ms-flex-line-pack: center ;
  align-content: center ;
}
.align-content-between {
  -ms-flex-line-pack: justify ;
  align-content: space-between ;
}
.align-content-around {
  -ms-flex-line-pack: distribute ;
  align-content: space-around ;
}
.align-content-stretch {
  -ms-flex-line-pack: stretch ;
  align-content: stretch ;
}
.align-self-auto {
  -ms-flex-item-align: auto ;
  align-self: auto ;
}
.align-self-start {
  -ms-flex-item-align: start ;
  align-self: flex-start ;
}
.align-self-end {
  -ms-flex-item-align: end ;
  align-self: flex-end ;
}
.align-self-center {
  -ms-flex-item-align: center ;
  align-self: center ;
}
.align-self-baseline {
  -ms-flex-item-align: baseline ;
  align-self: baseline ;
}
.align-self-stretch {
  -ms-flex-item-align: stretch ;
  align-self: stretch ;
}
.order-first {
  -webkit-box-ordinal-group: 0 ;
  -ms-flex-order: -1 ;
  order: -1 ;
}
.order-0 {
  -webkit-box-ordinal-group: 1 ;
  -ms-flex-order: 0 ;
  order: 0 ;
}
.order-1 {
  -webkit-box-ordinal-group: 2 ;
  -ms-flex-order: 1 ;
  order: 1 ;
}
.order-2 {
  -webkit-box-ordinal-group: 3 ;
  -ms-flex-order: 2 ;
  order: 2 ;
}
.order-3 {
  -webkit-box-ordinal-group: 4 ;
  -ms-flex-order: 3 ;
  order: 3 ;
}
.order-4 {
  -webkit-box-ordinal-group: 5 ;
  -ms-flex-order: 4 ;
  order: 4 ;
}
.order-5 {
  -webkit-box-ordinal-group: 6 ;
  -ms-flex-order: 5 ;
  order: 5 ;
}
.order-last {
  -webkit-box-ordinal-group: 7 ;
  -ms-flex-order: 6 ;
  order: 6 ;
}
.m-0 {
  margin: 0 ;
}
.m-1 {
  margin: 0.25rem ;
}
.m-2 {
  margin: 0.5rem ;
}
.m-3 {
  margin: 1rem ;
}
.m-4 {
  margin: 1.5rem ;
}
.m-5 {
  margin: 3rem ;
}
.m-auto {
  margin: auto ;
}
.mx-0 {
  margin-right: 0 ;
  margin-left: 0 ;
}
.mx-1 {
  margin-right: 0.25rem ;
  margin-left: 0.25rem ;
}
.mx-2 {
  margin-right: 0.5rem ;
  margin-left: 0.5rem ;
}
.mx-3 {
  margin-right: 1rem ;
  margin-left: 1rem ;
}
.mx-4 {
  margin-right: 1.5rem ;
  margin-left: 1.5rem ;
}
.mx-5 {
  margin-right: 3rem ;
  margin-left: 3rem ;
}
.mx-auto {
  margin-right: auto ;
  margin-left: auto ;
}
.my-0 {
  margin-top: 0 ;
  margin-bottom: 0 ;
}
.my-1 {
  margin-top: 0.25rem ;
  margin-bottom: 0.25rem ;
}
.my-2 {
  margin-top: 0.5rem ;
  margin-bottom: 0.5rem ;
}
.my-3 {
  margin-top: 1rem ;
  margin-bottom: 1rem ;
}
.my-4 {
  margin-top: 1.5rem ;
  margin-bottom: 1.5rem ;
}
.my-5 {
  margin-top: 3rem ;
  margin-bottom: 3rem ;
}
.my-auto {
  margin-top: auto ;
  margin-bottom: auto ;
}
.mt-0 {
  margin-top: 0 ;
}
.mt-1 {
  margin-top: 0.25rem ;
}
.mt-2 {
  margin-top: 0.5rem ;
}
.mt-3 {
  margin-top: 1rem ;
}
.mt-4 {
  margin-top: 1.5rem ;
}
.mt-5 {
  margin-top: 3rem ;
}
.mt-auto {
  margin-top: auto ;
}
.me-0 {
  margin-right: 0 ;
}
.me-1 {
  margin-right: 0.25rem ;
}
.me-2 {
  margin-right: 0.5rem ;
}
.me-3 {
  margin-right: 1rem ;
}
.me-4 {
  margin-right: 1.5rem ;
}
.me-5 {
  margin-right: 3rem ;
}
.me-auto {
  margin-right: auto ;
}
.mb-0 {
  margin-bottom: 0 ;
}
.mb-1 {
  margin-bottom: 0.25rem ;
}
.mb-2 {
  margin-bottom: 0.5rem ;
}
.mb-3 {
  margin-bottom: 1rem ;
}
.mb-4 {
  margin-bottom: 1.5rem ;
}
.mb-5 {
  margin-bottom: 3rem ;
}
.mb-auto {
  margin-bottom: auto ;
}
.ms-0 {
  margin-left: 0 ;
}
.ms-1 {
  margin-left: 0.25rem ;
}
.ms-2 {
  margin-left: 0.5rem ;
}
.ms-3 {
  margin-left: 1rem ;
}
.ms-4 {
  margin-left: 1.5rem ;
}
.ms-5 {
  margin-left: 3rem ;
}
.ms-auto {
  margin-left: auto ;
}
.m-n1 {
  margin: -0.25rem ;
}
.m-n2 {
  margin: -0.5rem ;
}
.m-n3 {
  margin: -1rem ;
}
.m-n4 {
  margin: -1.5rem ;
}
.m-n5 {
  margin: -3rem ;
}
.mx-n1 {
  margin-right: -0.25rem ;
  margin-left: -0.25rem ;
}
.mx-n2 {
  margin-right: -0.5rem ;
  margin-left: -0.5rem ;
}
.mx-n3 {
  margin-right: -1rem ;
  margin-left: -1rem ;
}
.mx-n4 {
  margin-right: -1.5rem ;
  margin-left: -1.5rem ;
}
.mx-n5 {
  margin-right: -3rem ;
  margin-left: -3rem ;
}
.my-n1 {
  margin-top: -0.25rem ;
  margin-bottom: -0.25rem ;
}
.my-n2 {
  margin-top: -0.5rem ;
  margin-bottom: -0.5rem ;
}
.my-n3 {
  margin-top: -1rem ;
  margin-bottom: -1rem ;
}
.my-n4 {
  margin-top: -1.5rem ;
  margin-bottom: -1.5rem ;
}
.my-n5 {
  margin-top: -3rem ;
  margin-bottom: -3rem ;
}
.mt-n1 {
  margin-top: -0.25rem ;
}
.mt-n2 {
  margin-top: -0.5rem ;
}
.mt-n3 {
  margin-top: -1rem ;
}
.mt-n4 {
  margin-top: -1.5rem ;
}
.mt-n5 {
  margin-top: -3rem ;
}
.me-n1 {
  margin-right: -0.25rem ;
}
.me-n2 {
  margin-right: -0.5rem ;
}
.me-n3 {
  margin-right: -1rem ;
}
.me-n4 {
  margin-right: -1.5rem ;
}
.me-n5 {
  margin-right: -3rem ;
}
.mb-n1 {
  margin-bottom: -0.25rem ;
}
.mb-n2 {
  margin-bottom: -0.5rem ;
}
.mb-n3 {
  margin-bottom: -1rem ;
}
.mb-n4 {
  margin-bottom: -1.5rem ;
}
.mb-n5 {
  margin-bottom: -3rem ;
}
.ms-n1 {
  margin-left: -0.25rem ;
}
.ms-n2 {
  margin-left: -0.5rem ;
}
.ms-n3 {
  margin-left: -1rem ;
}
.ms-n4 {
  margin-left: -1.5rem ;
}
.ms-n5 {
  margin-left: -3rem ;
}
.p-0 {
  padding: 0 ;
}
.p-1 {
  padding: 0.25rem ;
}
.p-2 {
  padding: 0.5rem ;
}
.p-3 {
  padding: 1rem ;
}
.p-4 {
  padding: 1.5rem ;
}
.p-5 {
  padding: 3rem ;
}
.px-0 {
  padding-right: 0 ;
  padding-left: 0 ;
}
.px-1 {
  padding-right: 0.25rem ;
  padding-left: 0.25rem ;
}
.px-2 {
  padding-right: 0.5rem ;
  padding-left: 0.5rem ;
}
.px-3 {
  padding-right: 1rem ;
  padding-left: 1rem ;
}
.px-4 {
  padding-right: 1.5rem ;
  padding-left: 1.5rem ;
}
.px-5 {
  padding-right: 3rem ;
  padding-left: 3rem ;
}
.py-0 {
  padding-top: 0 ;
  padding-bottom: 0 ;
}
.py-1 {
  padding-top: 0.25rem ;
  padding-bottom: 0.25rem ;
}
.py-2 {
  padding-top: 0.5rem ;
  padding-bottom: 0.5rem ;
}
.py-3 {
  padding-top: 1rem ;
  padding-bottom: 1rem ;
}
.py-4 {
  padding-top: 1.5rem ;
  padding-bottom: 1.5rem ;
}
.py-5 {
  padding-top: 3rem ;
  padding-bottom: 3rem ;
}
.pt-0 {
  padding-top: 0 ;
}
.pt-1 {
  padding-top: 0.25rem ;
}
.pt-2 {
  padding-top: 0.5rem ;
}
.pt-3 {
  padding-top: 1rem ;
}
.pt-4 {
  padding-top: 1.5rem ;
}
.pt-5 {
  padding-top: 3rem ;
}
.pe-0 {
  padding-right: 0 ;
}
.pe-1 {
  padding-right: 0.25rem ;
}
.pe-2 {
  padding-right: 0.5rem ;
}
.pe-3 {
  padding-right: 1rem ;
}
.pe-4 {
  padding-right: 1.5rem ;
}
.pe-5 {
  padding-right: 3rem ;
}
.pb-0 {
  padding-bottom: 0 ;
}
.pb-1 {
  padding-bottom: 0.25rem ;
}
.pb-2 {
  padding-bottom: 0.5rem ;
}
.pb-3 {
  padding-bottom: 1rem ;
}
.pb-4 {
  padding-bottom: 1.5rem ;
}
.pb-5 {
  padding-bottom: 3rem ;
}
.ps-0 {
  padding-left: 0 ;
}
.ps-1 {
  padding-left: 0.25rem ;
}
.ps-2 {
  padding-left: 0.5rem ;
}
.ps-3 {
  padding-left: 1rem ;
}
.ps-4 {
  padding-left: 1.5rem ;
}
.ps-5 {
  padding-left: 3rem ;
}
.font-monospace {
  font-family: var(--bs-font-monospace) ;
}
.fs-1 {
  font-size: calc(1.34375rem + 1.125vw) ;
}
.fs-2 {
  font-size: calc(1.3rem + 0.6vw) ;
}
.fs-3 {
  font-size: calc(1.27813rem + 0.3375vw) ;
}
.fs-4 {
  font-size: calc(1.25625rem + 0.075vw) ;
}
.fs-5 {
  font-size: 1.09375rem ;
}
.fs-6 {
  font-size: 0.875rem ;
}
.fst-italic {
  font-style: italic ;
}
.fst-normal {
  font-style: normal ;
}
.fw-light {
  font-weight: 300 ;
}
.fw-lighter {
  font-weight: lighter ;
}
.fw-normal {
  font-weight: 400 ;
}
.fw-bold {
  font-weight: 600 ;
}
.fw-bolder {
  font-weight: bolder ;
}
.lh-1 {
  line-height: 1 ;
}
.lh-sm {
  line-height: 1.25 ;
}
.lh-base {
  line-height: 1.5 ;
}
.lh-lg {
  line-height: 2 ;
}
.text-start {
  text-align: left ;
}
.text-end {
  text-align: right ;
}
.text-center {
  text-align: center ;
}
.text-decoration-none {
  text-decoration: none ;
}
.text-decoration-underline {
  text-decoration: underline ;
}
.text-decoration-line-through {
  text-decoration: line-through ;
}
.text-lowercase {
  text-transform: lowercase ;
}
.text-uppercase {
  text-transform: uppercase ;
}
.text-capitalize {
  text-transform: capitalize ;
}
.text-wrap {
  white-space: normal ;
}
.text-nowrap {
  white-space: nowrap ;
}
.text-break {
  word-wrap: break-word ;
  word-break: break-word ;
}
.text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) ;
}
.text-secondary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) ;
}
.text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) ;
}
.text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) ;
}
.text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) ;
}
.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) ;
}
.text-pink {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-pink-rgb), var(--bs-text-opacity)) ;
}
.text-light {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) ;
}
.text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) ;
}
.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) ;
}
.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) ;
}
.text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-rgb), var(--bs-text-opacity)) ;
}
.text-muted {
  --bs-text-opacity: 1;
  color: #74788d ;
}
.text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) ;
}
.text-white-50 {
  --bs-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) ;
}
.text-reset {
  --bs-text-opacity: 1;
  color: inherit ;
}
.text-opacity-25 {
  --bs-text-opacity: 0.25;
}
.text-opacity-50 {
  --bs-text-opacity: 0.5;
}
.text-opacity-75 {
  --bs-text-opacity: 0.75;
}
.text-opacity-100 {
  --bs-text-opacity: 1;
}
.bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-primary-rgb),
    var(--bs-bg-opacity)
  ) ;
}
.bg-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-secondary-rgb),
    var(--bs-bg-opacity)
  ) ;
}
.bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-success-rgb),
    var(--bs-bg-opacity)
  ) ;
}
.bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) ;
}
.bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(
    var(--bs-warning-rgb),
    var(--bs-bg-opacity)
  ) ;
}
.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) ;
}
.bg-pink {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-pink-rgb), var(--bs-bg-opacity)) ;
}
.bg-light {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) ;
}
.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) ;
}
.bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) ;
}
.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) ;
}
.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-rgb), var(--bs-bg-opacity)) ;
}
.bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent ;
}
.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}
.bg-opacity-25 {
  --bs-bg-opacity: 0.25;
}
.bg-opacity-50 {
  --bs-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --bs-bg-opacity: 0.75;
}
.bg-opacity-100 {
  --bs-bg-opacity: 1;
}
.bg-gradient {
  background-image: var(--bs-gradient) ;
}
.user-select-all {
  -webkit-user-select: all ;
  -moz-user-select: all ;
  -ms-user-select: all ;
  user-select: all ;
}
.user-select-auto {
  -webkit-user-select: auto ;
  -moz-user-select: auto ;
  -ms-user-select: auto ;
  user-select: auto ;
}
.user-select-none {
  -webkit-user-select: none ;
  -moz-user-select: none ;
  -ms-user-select: none ;
  user-select: none ;
}
.pe-none {
  pointer-events: none ;
}
.pe-auto {
  pointer-events: auto ;
}
.rounded {
  border-radius: 0.25rem ;
}
.rounded-0 {
  border-radius: 0 ;
}
.rounded-1 {
  border-radius: 0.2rem ;
}
.rounded-2 {
  border-radius: 0.25rem ;
}
.rounded-3 {
  border-radius: 0.4rem ;
}
.rounded-circle {
  border-radius: 50% ;
}
.rounded-pill {
  border-radius: 50rem ;
}
.rounded-top {
  border-top-left-radius: 0.25rem ;
  border-top-right-radius: 0.25rem ;
}
.rounded-end {
  border-top-right-radius: 0.25rem ;
  border-bottom-right-radius: 0.25rem ;
}
.rounded-bottom {
  border-bottom-right-radius: 0.25rem ;
  border-bottom-left-radius: 0.25rem ;
}
.rounded-start {
  border-bottom-left-radius: 0.25rem ;
  border-top-left-radius: 0.25rem ;
}
.visible {
  visibility: visible ;
}
.invisible {
  visibility: hidden ;
}
@media (min-width: 576px) {
  .float-sm-start {
    float: left ;
  }
  .float-sm-end {
    float: right ;
  }
  .float-sm-none {
    float: none ;
  }
  .d-sm-inline {
    display: inline ;
  }
  .d-sm-inline-block {
    display: inline-block ;
  }
  .d-sm-block {
    display: block ;
  }
  .d-sm-grid {
    display: grid ;
  }
  .d-sm-table {
    display: table ;
  }
  .d-sm-table-row {
    display: table-row ;
  }
  .d-sm-table-cell {
    display: table-cell ;
  }
  .d-sm-flex {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
  }
  .d-sm-inline-flex {
    display: -webkit-inline-box ;
    display: -ms-inline-flexbox ;
    display: inline-flex ;
  }
  .d-sm-none {
    display: none ;
  }
  .flex-sm-fill {
    -webkit-box-flex: 1 ;
    -ms-flex: 1 1 auto ;
    flex: 1 1 auto ;
  }
  .flex-sm-row {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: row ;
    flex-direction: row ;
  }
  .flex-sm-column {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: column ;
    flex-direction: column ;
  }
  .flex-sm-row-reverse {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: row-reverse ;
    flex-direction: row-reverse ;
  }
  .flex-sm-column-reverse {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: column-reverse ;
    flex-direction: column-reverse ;
  }
  .flex-sm-grow-0 {
    -webkit-box-flex: 0 ;
    -ms-flex-positive: 0 ;
    flex-grow: 0 ;
  }
  .flex-sm-grow-1 {
    -webkit-box-flex: 1 ;
    -ms-flex-positive: 1 ;
    flex-grow: 1 ;
  }
  .flex-sm-shrink-0 {
    -ms-flex-negative: 0 ;
    flex-shrink: 0 ;
  }
  .flex-sm-shrink-1 {
    -ms-flex-negative: 1 ;
    flex-shrink: 1 ;
  }
  .flex-sm-wrap {
    -ms-flex-wrap: wrap ;
    flex-wrap: wrap ;
  }
  .flex-sm-nowrap {
    -ms-flex-wrap: nowrap ;
    flex-wrap: nowrap ;
  }
  .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse ;
    flex-wrap: wrap-reverse ;
  }
  .gap-sm-0 {
    gap: 0 ;
  }
  .gap-sm-1 {
    gap: 0.25rem ;
  }
  .gap-sm-2 {
    gap: 0.5rem ;
  }
  .gap-sm-3 {
    gap: 1rem ;
  }
  .gap-sm-4 {
    gap: 1.5rem ;
  }
  .gap-sm-5 {
    gap: 3rem ;
  }
  .justify-content-sm-start {
    -webkit-box-pack: start ;
    -ms-flex-pack: start ;
    justify-content: flex-start ;
  }
  .justify-content-sm-end {
    -webkit-box-pack: end ;
    -ms-flex-pack: end ;
    justify-content: flex-end ;
  }
  .justify-content-sm-center {
    -webkit-box-pack: center ;
    -ms-flex-pack: center ;
    justify-content: center ;
  }
  .justify-content-sm-between {
    -webkit-box-pack: justify ;
    -ms-flex-pack: justify ;
    justify-content: space-between ;
  }
  .justify-content-sm-around {
    -ms-flex-pack: distribute ;
    justify-content: space-around ;
  }
  .justify-content-sm-evenly {
    -webkit-box-pack: space-evenly ;
    -ms-flex-pack: space-evenly ;
    justify-content: space-evenly ;
  }
  .align-items-sm-start {
    -webkit-box-align: start ;
    -ms-flex-align: start ;
    align-items: flex-start ;
  }
  .align-items-sm-end {
    -webkit-box-align: end ;
    -ms-flex-align: end ;
    align-items: flex-end ;
  }
  .align-items-sm-center {
    -webkit-box-align: center ;
    -ms-flex-align: center ;
    align-items: center ;
  }
  .align-items-sm-baseline {
    -webkit-box-align: baseline ;
    -ms-flex-align: baseline ;
    align-items: baseline ;
  }
  .align-items-sm-stretch {
    -webkit-box-align: stretch ;
    -ms-flex-align: stretch ;
    align-items: stretch ;
  }
  .align-content-sm-start {
    -ms-flex-line-pack: start ;
    align-content: flex-start ;
  }
  .align-content-sm-end {
    -ms-flex-line-pack: end ;
    align-content: flex-end ;
  }
  .align-content-sm-center {
    -ms-flex-line-pack: center ;
    align-content: center ;
  }
  .align-content-sm-between {
    -ms-flex-line-pack: justify ;
    align-content: space-between ;
  }
  .align-content-sm-around {
    -ms-flex-line-pack: distribute ;
    align-content: space-around ;
  }
  .align-content-sm-stretch {
    -ms-flex-line-pack: stretch ;
    align-content: stretch ;
  }
  .align-self-sm-auto {
    -ms-flex-item-align: auto ;
    align-self: auto ;
  }
  .align-self-sm-start {
    -ms-flex-item-align: start ;
    align-self: flex-start ;
  }
  .align-self-sm-end {
    -ms-flex-item-align: end ;
    align-self: flex-end ;
  }
  .align-self-sm-center {
    -ms-flex-item-align: center ;
    align-self: center ;
  }
  .align-self-sm-baseline {
    -ms-flex-item-align: baseline ;
    align-self: baseline ;
  }
  .align-self-sm-stretch {
    -ms-flex-item-align: stretch ;
    align-self: stretch ;
  }
  .order-sm-first {
    -webkit-box-ordinal-group: 0 ;
    -ms-flex-order: -1 ;
    order: -1 ;
  }
  .order-sm-0 {
    -webkit-box-ordinal-group: 1 ;
    -ms-flex-order: 0 ;
    order: 0 ;
  }
  .order-sm-1 {
    -webkit-box-ordinal-group: 2 ;
    -ms-flex-order: 1 ;
    order: 1 ;
  }
  .order-sm-2 {
    -webkit-box-ordinal-group: 3 ;
    -ms-flex-order: 2 ;
    order: 2 ;
  }
  .order-sm-3 {
    -webkit-box-ordinal-group: 4 ;
    -ms-flex-order: 3 ;
    order: 3 ;
  }
  .order-sm-4 {
    -webkit-box-ordinal-group: 5 ;
    -ms-flex-order: 4 ;
    order: 4 ;
  }
  .order-sm-5 {
    -webkit-box-ordinal-group: 6 ;
    -ms-flex-order: 5 ;
    order: 5 ;
  }
  .order-sm-last {
    -webkit-box-ordinal-group: 7 ;
    -ms-flex-order: 6 ;
    order: 6 ;
  }
  .m-sm-0 {
    margin: 0 ;
  }
  .m-sm-1 {
    margin: 0.25rem ;
  }
  .m-sm-2 {
    margin: 0.5rem ;
  }
  .m-sm-3 {
    margin: 1rem ;
  }
  .m-sm-4 {
    margin: 1.5rem ;
  }
  .m-sm-5 {
    margin: 3rem ;
  }
  .m-sm-auto {
    margin: auto ;
  }
  .mx-sm-0 {
    margin-right: 0 ;
    margin-left: 0 ;
  }
  .mx-sm-1 {
    margin-right: 0.25rem ;
    margin-left: 0.25rem ;
  }
  .mx-sm-2 {
    margin-right: 0.5rem ;
    margin-left: 0.5rem ;
  }
  .mx-sm-3 {
    margin-right: 1rem ;
    margin-left: 1rem ;
  }
  .mx-sm-4 {
    margin-right: 1.5rem ;
    margin-left: 1.5rem ;
  }
  .mx-sm-5 {
    margin-right: 3rem ;
    margin-left: 3rem ;
  }
  .mx-sm-auto {
    margin-right: auto ;
    margin-left: auto ;
  }
  .my-sm-0 {
    margin-top: 0 ;
    margin-bottom: 0 ;
  }
  .my-sm-1 {
    margin-top: 0.25rem ;
    margin-bottom: 0.25rem ;
  }
  .my-sm-2 {
    margin-top: 0.5rem ;
    margin-bottom: 0.5rem ;
  }
  .my-sm-3 {
    margin-top: 1rem ;
    margin-bottom: 1rem ;
  }
  .my-sm-4 {
    margin-top: 1.5rem ;
    margin-bottom: 1.5rem ;
  }
  .my-sm-5 {
    margin-top: 3rem ;
    margin-bottom: 3rem ;
  }
  .my-sm-auto {
    margin-top: auto ;
    margin-bottom: auto ;
  }
  .mt-sm-0 {
    margin-top: 0 ;
  }
  .mt-sm-1 {
    margin-top: 0.25rem ;
  }
  .mt-sm-2 {
    margin-top: 0.5rem ;
  }
  .mt-sm-3 {
    margin-top: 1rem ;
  }
  .mt-sm-4 {
    margin-top: 1.5rem ;
  }
  .mt-sm-5 {
    margin-top: 3rem ;
  }
  .mt-sm-auto {
    margin-top: auto ;
  }
  .me-sm-0 {
    margin-right: 0 ;
  }
  .me-sm-1 {
    margin-right: 0.25rem ;
  }
  .me-sm-2 {
    margin-right: 0.5rem ;
  }
  .me-sm-3 {
    margin-right: 1rem ;
  }
  .me-sm-4 {
    margin-right: 1.5rem ;
  }
  .me-sm-5 {
    margin-right: 3rem ;
  }
  .me-sm-auto {
    margin-right: auto ;
  }
  .mb-sm-0 {
    margin-bottom: 0 ;
  }
  .mb-sm-1 {
    margin-bottom: 0.25rem ;
  }
  .mb-sm-2 {
    margin-bottom: 0.5rem ;
  }
  .mb-sm-3 {
    margin-bottom: 1rem ;
  }
  .mb-sm-4 {
    margin-bottom: 1.5rem ;
  }
  .mb-sm-5 {
    margin-bottom: 3rem ;
  }
  .mb-sm-auto {
    margin-bottom: auto ;
  }
  .ms-sm-0 {
    margin-left: 0 ;
  }
  .ms-sm-1 {
    margin-left: 0.25rem ;
  }
  .ms-sm-2 {
    margin-left: 0.5rem ;
  }
  .ms-sm-3 {
    margin-left: 1rem ;
  }
  .ms-sm-4 {
    margin-left: 1.5rem ;
  }
  .ms-sm-5 {
    margin-left: 3rem ;
  }
  .ms-sm-auto {
    margin-left: auto ;
  }
  .m-sm-n1 {
    margin: -0.25rem ;
  }
  .m-sm-n2 {
    margin: -0.5rem ;
  }
  .m-sm-n3 {
    margin: -1rem ;
  }
  .m-sm-n4 {
    margin: -1.5rem ;
  }
  .m-sm-n5 {
    margin: -3rem ;
  }
  .mx-sm-n1 {
    margin-right: -0.25rem ;
    margin-left: -0.25rem ;
  }
  .mx-sm-n2 {
    margin-right: -0.5rem ;
    margin-left: -0.5rem ;
  }
  .mx-sm-n3 {
    margin-right: -1rem ;
    margin-left: -1rem ;
  }
  .mx-sm-n4 {
    margin-right: -1.5rem ;
    margin-left: -1.5rem ;
  }
  .mx-sm-n5 {
    margin-right: -3rem ;
    margin-left: -3rem ;
  }
  .my-sm-n1 {
    margin-top: -0.25rem ;
    margin-bottom: -0.25rem ;
  }
  .my-sm-n2 {
    margin-top: -0.5rem ;
    margin-bottom: -0.5rem ;
  }
  .my-sm-n3 {
    margin-top: -1rem ;
    margin-bottom: -1rem ;
  }
  .my-sm-n4 {
    margin-top: -1.5rem ;
    margin-bottom: -1.5rem ;
  }
  .my-sm-n5 {
    margin-top: -3rem ;
    margin-bottom: -3rem ;
  }
  .mt-sm-n1 {
    margin-top: -0.25rem ;
  }
  .mt-sm-n2 {
    margin-top: -0.5rem ;
  }
  .mt-sm-n3 {
    margin-top: -1rem ;
  }
  .mt-sm-n4 {
    margin-top: -1.5rem ;
  }
  .mt-sm-n5 {
    margin-top: -3rem ;
  }
  .me-sm-n1 {
    margin-right: -0.25rem ;
  }
  .me-sm-n2 {
    margin-right: -0.5rem ;
  }
  .me-sm-n3 {
    margin-right: -1rem ;
  }
  .me-sm-n4 {
    margin-right: -1.5rem ;
  }
  .me-sm-n5 {
    margin-right: -3rem ;
  }
  .mb-sm-n1 {
    margin-bottom: -0.25rem ;
  }
  .mb-sm-n2 {
    margin-bottom: -0.5rem ;
  }
  .mb-sm-n3 {
    margin-bottom: -1rem ;
  }
  .mb-sm-n4 {
    margin-bottom: -1.5rem ;
  }
  .mb-sm-n5 {
    margin-bottom: -3rem ;
  }
  .ms-sm-n1 {
    margin-left: -0.25rem ;
  }
  .ms-sm-n2 {
    margin-left: -0.5rem ;
  }
  .ms-sm-n3 {
    margin-left: -1rem ;
  }
  .ms-sm-n4 {
    margin-left: -1.5rem ;
  }
  .ms-sm-n5 {
    margin-left: -3rem ;
  }
  .p-sm-0 {
    padding: 0 ;
  }
  .p-sm-1 {
    padding: 0.25rem ;
  }
  .p-sm-2 {
    padding: 0.5rem ;
  }
  .p-sm-3 {
    padding: 1rem ;
  }
  .p-sm-4 {
    padding: 1.5rem ;
  }
  .p-sm-5 {
    padding: 3rem ;
  }
  .px-sm-0 {
    padding-right: 0 ;
    padding-left: 0 ;
  }
  .px-sm-1 {
    padding-right: 0.25rem ;
    padding-left: 0.25rem ;
  }
  .px-sm-2 {
    padding-right: 0.5rem ;
    padding-left: 0.5rem ;
  }
  .px-sm-3 {
    padding-right: 1rem ;
    padding-left: 1rem ;
  }
  .px-sm-4 {
    padding-right: 1.5rem ;
    padding-left: 1.5rem ;
  }
  .px-sm-5 {
    padding-right: 3rem ;
    padding-left: 3rem ;
  }
  .py-sm-0 {
    padding-top: 0 ;
    padding-bottom: 0 ;
  }
  .py-sm-1 {
    padding-top: 0.25rem ;
    padding-bottom: 0.25rem ;
  }
  .py-sm-2 {
    padding-top: 0.5rem ;
    padding-bottom: 0.5rem ;
  }
  .py-sm-3 {
    padding-top: 1rem ;
    padding-bottom: 1rem ;
  }
  .py-sm-4 {
    padding-top: 1.5rem ;
    padding-bottom: 1.5rem ;
  }
  .py-sm-5 {
    padding-top: 3rem ;
    padding-bottom: 3rem ;
  }
  .pt-sm-0 {
    padding-top: 0 ;
  }
  .pt-sm-1 {
    padding-top: 0.25rem ;
  }
  .pt-sm-2 {
    padding-top: 0.5rem ;
  }
  .pt-sm-3 {
    padding-top: 1rem ;
  }
  .pt-sm-4 {
    padding-top: 1.5rem ;
  }
  .pt-sm-5 {
    padding-top: 3rem ;
  }
  .pe-sm-0 {
    padding-right: 0 ;
  }
  .pe-sm-1 {
    padding-right: 0.25rem ;
  }
  .pe-sm-2 {
    padding-right: 0.5rem ;
  }
  .pe-sm-3 {
    padding-right: 1rem ;
  }
  .pe-sm-4 {
    padding-right: 1.5rem ;
  }
  .pe-sm-5 {
    padding-right: 3rem ;
  }
  .pb-sm-0 {
    padding-bottom: 0 ;
  }
  .pb-sm-1 {
    padding-bottom: 0.25rem ;
  }
  .pb-sm-2 {
    padding-bottom: 0.5rem ;
  }
  .pb-sm-3 {
    padding-bottom: 1rem ;
  }
  .pb-sm-4 {
    padding-bottom: 1.5rem ;
  }
  .pb-sm-5 {
    padding-bottom: 3rem ;
  }
  .ps-sm-0 {
    padding-left: 0 ;
  }
  .ps-sm-1 {
    padding-left: 0.25rem ;
  }
  .ps-sm-2 {
    padding-left: 0.5rem ;
  }
  .ps-sm-3 {
    padding-left: 1rem ;
  }
  .ps-sm-4 {
    padding-left: 1.5rem ;
  }
  .ps-sm-5 {
    padding-left: 3rem ;
  }
  .text-sm-start {
    text-align: left ;
  }
  .text-sm-end {
    text-align: right ;
  }
  .text-sm-center {
    text-align: center ;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left ;
  }
  .float-md-end {
    float: right ;
  }
  .float-md-none {
    float: none ;
  }
  .d-md-inline {
    display: inline ;
  }
  .d-md-inline-block {
    display: inline-block ;
  }
  .d-md-block {
    display: block ;
  }
  .d-md-grid {
    display: grid ;
  }
  .d-md-table {
    display: table ;
  }
  .d-md-table-row {
    display: table-row ;
  }
  .d-md-table-cell {
    display: table-cell ;
  }
  .d-md-flex {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
  }
  .d-md-inline-flex {
    display: -webkit-inline-box ;
    display: -ms-inline-flexbox ;
    display: inline-flex ;
  }
  .d-md-none {
    display: none ;
  }
  .flex-md-fill {
    -webkit-box-flex: 1 ;
    -ms-flex: 1 1 auto ;
    flex: 1 1 auto ;
  }
  .flex-md-row {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: row ;
    flex-direction: row ;
  }
  .flex-md-column {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: column ;
    flex-direction: column ;
  }
  .flex-md-row-reverse {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: row-reverse ;
    flex-direction: row-reverse ;
  }
  .flex-md-column-reverse {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: column-reverse ;
    flex-direction: column-reverse ;
  }
  .flex-md-grow-0 {
    -webkit-box-flex: 0 ;
    -ms-flex-positive: 0 ;
    flex-grow: 0 ;
  }
  .flex-md-grow-1 {
    -webkit-box-flex: 1 ;
    -ms-flex-positive: 1 ;
    flex-grow: 1 ;
  }
  .flex-md-shrink-0 {
    -ms-flex-negative: 0 ;
    flex-shrink: 0 ;
  }
  .flex-md-shrink-1 {
    -ms-flex-negative: 1 ;
    flex-shrink: 1 ;
  }
  .flex-md-wrap {
    -ms-flex-wrap: wrap ;
    flex-wrap: wrap ;
  }
  .flex-md-nowrap {
    -ms-flex-wrap: nowrap ;
    flex-wrap: nowrap ;
  }
  .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse ;
    flex-wrap: wrap-reverse ;
  }
  .gap-md-0 {
    gap: 0 ;
  }
  .gap-md-1 {
    gap: 0.25rem ;
  }
  .gap-md-2 {
    gap: 0.5rem ;
  }
  .gap-md-3 {
    gap: 1rem ;
  }
  .gap-md-4 {
    gap: 1.5rem ;
  }
  .gap-md-5 {
    gap: 3rem ;
  }
  .justify-content-md-start {
    -webkit-box-pack: start ;
    -ms-flex-pack: start ;
    justify-content: flex-start ;
  }
  .justify-content-md-end {
    -webkit-box-pack: end ;
    -ms-flex-pack: end ;
    justify-content: flex-end ;
  }
  .justify-content-md-center {
    -webkit-box-pack: center ;
    -ms-flex-pack: center ;
    justify-content: center ;
  }
  .justify-content-md-between {
    -webkit-box-pack: justify ;
    -ms-flex-pack: justify ;
    justify-content: space-between ;
  }
  .justify-content-md-around {
    -ms-flex-pack: distribute ;
    justify-content: space-around ;
  }
  .justify-content-md-evenly {
    -webkit-box-pack: space-evenly ;
    -ms-flex-pack: space-evenly ;
    justify-content: space-evenly ;
  }
  .align-items-md-start {
    -webkit-box-align: start ;
    -ms-flex-align: start ;
    align-items: flex-start ;
  }
  .align-items-md-end {
    -webkit-box-align: end ;
    -ms-flex-align: end ;
    align-items: flex-end ;
  }
  .align-items-md-center {
    -webkit-box-align: center ;
    -ms-flex-align: center ;
    align-items: center ;
  }
  .align-items-md-baseline {
    -webkit-box-align: baseline ;
    -ms-flex-align: baseline ;
    align-items: baseline ;
  }
  .align-items-md-stretch {
    -webkit-box-align: stretch ;
    -ms-flex-align: stretch ;
    align-items: stretch ;
  }
  .align-content-md-start {
    -ms-flex-line-pack: start ;
    align-content: flex-start ;
  }
  .align-content-md-end {
    -ms-flex-line-pack: end ;
    align-content: flex-end ;
  }
  .align-content-md-center {
    -ms-flex-line-pack: center ;
    align-content: center ;
  }
  .align-content-md-between {
    -ms-flex-line-pack: justify ;
    align-content: space-between ;
  }
  .align-content-md-around {
    -ms-flex-line-pack: distribute ;
    align-content: space-around ;
  }
  .align-content-md-stretch {
    -ms-flex-line-pack: stretch ;
    align-content: stretch ;
  }
  .align-self-md-auto {
    -ms-flex-item-align: auto ;
    align-self: auto ;
  }
  .align-self-md-start {
    -ms-flex-item-align: start ;
    align-self: flex-start ;
  }
  .align-self-md-end {
    -ms-flex-item-align: end ;
    align-self: flex-end ;
  }
  .align-self-md-center {
    -ms-flex-item-align: center ;
    align-self: center ;
  }
  .align-self-md-baseline {
    -ms-flex-item-align: baseline ;
    align-self: baseline ;
  }
  .align-self-md-stretch {
    -ms-flex-item-align: stretch ;
    align-self: stretch ;
  }
  .order-md-first {
    -webkit-box-ordinal-group: 0 ;
    -ms-flex-order: -1 ;
    order: -1 ;
  }
  .order-md-0 {
    -webkit-box-ordinal-group: 1 ;
    -ms-flex-order: 0 ;
    order: 0 ;
  }
  .order-md-1 {
    -webkit-box-ordinal-group: 2 ;
    -ms-flex-order: 1 ;
    order: 1 ;
  }
  .order-md-2 {
    -webkit-box-ordinal-group: 3 ;
    -ms-flex-order: 2 ;
    order: 2 ;
  }
  .order-md-3 {
    -webkit-box-ordinal-group: 4 ;
    -ms-flex-order: 3 ;
    order: 3 ;
  }
  .order-md-4 {
    -webkit-box-ordinal-group: 5 ;
    -ms-flex-order: 4 ;
    order: 4 ;
  }
  .order-md-5 {
    -webkit-box-ordinal-group: 6 ;
    -ms-flex-order: 5 ;
    order: 5 ;
  }
  .order-md-last {
    -webkit-box-ordinal-group: 7 ;
    -ms-flex-order: 6 ;
    order: 6 ;
  }
  .m-md-0 {
    margin: 0 ;
  }
  .m-md-1 {
    margin: 0.25rem ;
  }
  .m-md-2 {
    margin: 0.5rem ;
  }
  .m-md-3 {
    margin: 1rem ;
  }
  .m-md-4 {
    margin: 1.5rem ;
  }
  .m-md-5 {
    margin: 3rem ;
  }
  .m-md-auto {
    margin: auto ;
  }
  .mx-md-0 {
    margin-right: 0 ;
    margin-left: 0 ;
  }
  .mx-md-1 {
    margin-right: 0.25rem ;
    margin-left: 0.25rem ;
  }
  .mx-md-2 {
    margin-right: 0.5rem ;
    margin-left: 0.5rem ;
  }
  .mx-md-3 {
    margin-right: 1rem ;
    margin-left: 1rem ;
  }
  .mx-md-4 {
    margin-right: 1.5rem ;
    margin-left: 1.5rem ;
  }
  .mx-md-5 {
    margin-right: 3rem ;
    margin-left: 3rem ;
  }
  .mx-md-auto {
    margin-right: auto ;
    margin-left: auto ;
  }
  .my-md-0 {
    margin-top: 0 ;
    margin-bottom: 0 ;
  }
  .my-md-1 {
    margin-top: 0.25rem ;
    margin-bottom: 0.25rem ;
  }
  .my-md-2 {
    margin-top: 0.5rem ;
    margin-bottom: 0.5rem ;
  }
  .my-md-3 {
    margin-top: 1rem ;
    margin-bottom: 1rem ;
  }
  .my-md-4 {
    margin-top: 1.5rem ;
    margin-bottom: 1.5rem ;
  }
  .my-md-5 {
    margin-top: 3rem ;
    margin-bottom: 3rem ;
  }
  .my-md-auto {
    margin-top: auto ;
    margin-bottom: auto ;
  }
  .mt-md-0 {
    margin-top: 0 ;
  }
  .mt-md-1 {
    margin-top: 0.25rem ;
  }
  .mt-md-2 {
    margin-top: 0.5rem ;
  }
  .mt-md-3 {
    margin-top: 1rem ;
  }
  .mt-md-4 {
    margin-top: 1.5rem ;
  }
  .mt-md-5 {
    margin-top: 3rem ;
  }
  .mt-md-auto {
    margin-top: auto ;
  }
  .me-md-0 {
    margin-right: 0 ;
  }
  .me-md-1 {
    margin-right: 0.25rem ;
  }
  .me-md-2 {
    margin-right: 0.5rem ;
  }
  .me-md-3 {
    margin-right: 1rem ;
  }
  .me-md-4 {
    margin-right: 1.5rem ;
  }
  .me-md-5 {
    margin-right: 3rem ;
  }
  .me-md-auto {
    margin-right: auto ;
  }
  .mb-md-0 {
    margin-bottom: 0 ;
  }
  .mb-md-1 {
    margin-bottom: 0.25rem ;
  }
  .mb-md-2 {
    margin-bottom: 0.5rem ;
  }
  .mb-md-3 {
    margin-bottom: 1rem ;
  }
  .mb-md-4 {
    margin-bottom: 1.5rem ;
  }
  .mb-md-5 {
    margin-bottom: 3rem ;
  }
  .mb-md-auto {
    margin-bottom: auto ;
  }
  .ms-md-0 {
    margin-left: 0 ;
  }
  .ms-md-1 {
    margin-left: 0.25rem ;
  }
  .ms-md-2 {
    margin-left: 0.5rem ;
  }
  .ms-md-3 {
    margin-left: 1rem ;
  }
  .ms-md-4 {
    margin-left: 1.5rem ;
  }
  .ms-md-5 {
    margin-left: 3rem ;
  }
  .ms-md-auto {
    margin-left: auto ;
  }
  .m-md-n1 {
    margin: -0.25rem ;
  }
  .m-md-n2 {
    margin: -0.5rem ;
  }
  .m-md-n3 {
    margin: -1rem ;
  }
  .m-md-n4 {
    margin: -1.5rem ;
  }
  .m-md-n5 {
    margin: -3rem ;
  }
  .mx-md-n1 {
    margin-right: -0.25rem ;
    margin-left: -0.25rem ;
  }
  .mx-md-n2 {
    margin-right: -0.5rem ;
    margin-left: -0.5rem ;
  }
  .mx-md-n3 {
    margin-right: -1rem ;
    margin-left: -1rem ;
  }
  .mx-md-n4 {
    margin-right: -1.5rem ;
    margin-left: -1.5rem ;
  }
  .mx-md-n5 {
    margin-right: -3rem ;
    margin-left: -3rem ;
  }
  .my-md-n1 {
    margin-top: -0.25rem ;
    margin-bottom: -0.25rem ;
  }
  .my-md-n2 {
    margin-top: -0.5rem ;
    margin-bottom: -0.5rem ;
  }
  .my-md-n3 {
    margin-top: -1rem ;
    margin-bottom: -1rem ;
  }
  .my-md-n4 {
    margin-top: -1.5rem ;
    margin-bottom: -1.5rem ;
  }
  .my-md-n5 {
    margin-top: -3rem ;
    margin-bottom: -3rem ;
  }
  .mt-md-n1 {
    margin-top: -0.25rem ;
  }
  .mt-md-n2 {
    margin-top: -0.5rem ;
  }
  .mt-md-n3 {
    margin-top: -1rem ;
  }
  .mt-md-n4 {
    margin-top: -1.5rem ;
  }
  .mt-md-n5 {
    margin-top: -3rem ;
  }
  .me-md-n1 {
    margin-right: -0.25rem ;
  }
  .me-md-n2 {
    margin-right: -0.5rem ;
  }
  .me-md-n3 {
    margin-right: -1rem ;
  }
  .me-md-n4 {
    margin-right: -1.5rem ;
  }
  .me-md-n5 {
    margin-right: -3rem ;
  }
  .mb-md-n1 {
    margin-bottom: -0.25rem ;
  }
  .mb-md-n2 {
    margin-bottom: -0.5rem ;
  }
  .mb-md-n3 {
    margin-bottom: -1rem ;
  }
  .mb-md-n4 {
    margin-bottom: -1.5rem ;
  }
  .mb-md-n5 {
    margin-bottom: -3rem ;
  }
  .ms-md-n1 {
    margin-left: -0.25rem ;
  }
  .ms-md-n2 {
    margin-left: -0.5rem ;
  }
  .ms-md-n3 {
    margin-left: -1rem ;
  }
  .ms-md-n4 {
    margin-left: -1.5rem ;
  }
  .ms-md-n5 {
    margin-left: -3rem ;
  }
  .p-md-0 {
    padding: 0 ;
  }
  .p-md-1 {
    padding: 0.25rem ;
  }
  .p-md-2 {
    padding: 0.5rem ;
  }
  .p-md-3 {
    padding: 1rem ;
  }
  .p-md-4 {
    padding: 1.5rem ;
  }
  .p-md-5 {
    padding: 3rem ;
  }
  .px-md-0 {
    padding-right: 0 ;
    padding-left: 0 ;
  }
  .px-md-1 {
    padding-right: 0.25rem ;
    padding-left: 0.25rem ;
  }
  .px-md-2 {
    padding-right: 0.5rem ;
    padding-left: 0.5rem ;
  }
  .px-md-3 {
    padding-right: 1rem ;
    padding-left: 1rem ;
  }
  .px-md-4 {
    padding-right: 1.5rem ;
    padding-left: 1.5rem ;
  }
  .px-md-5 {
    padding-right: 3rem ;
    padding-left: 3rem ;
  }
  .py-md-0 {
    padding-top: 0 ;
    padding-bottom: 0 ;
  }
  .py-md-1 {
    padding-top: 0.25rem ;
    padding-bottom: 0.25rem ;
  }
  .py-md-2 {
    padding-top: 0.5rem ;
    padding-bottom: 0.5rem ;
  }
  .py-md-3 {
    padding-top: 1rem ;
    padding-bottom: 1rem ;
  }
  .py-md-4 {
    padding-top: 1.5rem ;
    padding-bottom: 1.5rem ;
  }
  .py-md-5 {
    padding-top: 3rem ;
    padding-bottom: 3rem ;
  }
  .pt-md-0 {
    padding-top: 0 ;
  }
  .pt-md-1 {
    padding-top: 0.25rem ;
  }
  .pt-md-2 {
    padding-top: 0.5rem ;
  }
  .pt-md-3 {
    padding-top: 1rem ;
  }
  .pt-md-4 {
    padding-top: 1.5rem ;
  }
  .pt-md-5 {
    padding-top: 3rem ;
  }
  .pe-md-0 {
    padding-right: 0 ;
  }
  .pe-md-1 {
    padding-right: 0.25rem ;
  }
  .pe-md-2 {
    padding-right: 0.5rem ;
  }
  .pe-md-3 {
    padding-right: 1rem ;
  }
  .pe-md-4 {
    padding-right: 1.5rem ;
  }
  .pe-md-5 {
    padding-right: 3rem ;
  }
  .pb-md-0 {
    padding-bottom: 0 ;
  }
  .pb-md-1 {
    padding-bottom: 0.25rem ;
  }
  .pb-md-2 {
    padding-bottom: 0.5rem ;
  }
  .pb-md-3 {
    padding-bottom: 1rem ;
  }
  .pb-md-4 {
    padding-bottom: 1.5rem ;
  }
  .pb-md-5 {
    padding-bottom: 3rem ;
  }
  .ps-md-0 {
    padding-left: 0 ;
  }
  .ps-md-1 {
    padding-left: 0.25rem ;
  }
  .ps-md-2 {
    padding-left: 0.5rem ;
  }
  .ps-md-3 {
    padding-left: 1rem ;
  }
  .ps-md-4 {
    padding-left: 1.5rem ;
  }
  .ps-md-5 {
    padding-left: 3rem ;
  }
  .text-md-start {
    text-align: left ;
  }
  .text-md-end {
    text-align: right ;
  }
  .text-md-center {
    text-align: center ;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left ;
  }
  .float-lg-end {
    float: right ;
  }
  .float-lg-none {
    float: none ;
  }
  .d-lg-inline {
    display: inline ;
  }
  .d-lg-inline-block {
    display: inline-block ;
  }
  .d-lg-block {
    display: block ;
  }
  .d-lg-grid {
    display: grid ;
  }
  .d-lg-table {
    display: table ;
  }
  .d-lg-table-row {
    display: table-row ;
  }
  .d-lg-table-cell {
    display: table-cell ;
  }
  .d-lg-flex {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
  }
  .d-lg-inline-flex {
    display: -webkit-inline-box ;
    display: -ms-inline-flexbox ;
    display: inline-flex ;
  }
  .d-lg-none {
    display: none ;
  }
  .flex-lg-fill {
    -webkit-box-flex: 1 ;
    -ms-flex: 1 1 auto ;
    flex: 1 1 auto ;
  }
  .flex-lg-row {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: row ;
    flex-direction: row ;
  }
  .flex-lg-column {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: column ;
    flex-direction: column ;
  }
  .flex-lg-row-reverse {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: row-reverse ;
    flex-direction: row-reverse ;
  }
  .flex-lg-column-reverse {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: column-reverse ;
    flex-direction: column-reverse ;
  }
  .flex-lg-grow-0 {
    -webkit-box-flex: 0 ;
    -ms-flex-positive: 0 ;
    flex-grow: 0 ;
  }
  .flex-lg-grow-1 {
    -webkit-box-flex: 1 ;
    -ms-flex-positive: 1 ;
    flex-grow: 1 ;
  }
  .flex-lg-shrink-0 {
    -ms-flex-negative: 0 ;
    flex-shrink: 0 ;
  }
  .flex-lg-shrink-1 {
    -ms-flex-negative: 1 ;
    flex-shrink: 1 ;
  }
  .flex-lg-wrap {
    -ms-flex-wrap: wrap ;
    flex-wrap: wrap ;
  }
  .flex-lg-nowrap {
    -ms-flex-wrap: nowrap ;
    flex-wrap: nowrap ;
  }
  .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse ;
    flex-wrap: wrap-reverse ;
  }
  .gap-lg-0 {
    gap: 0 ;
  }
  .gap-lg-1 {
    gap: 0.25rem ;
  }
  .gap-lg-2 {
    gap: 0.5rem ;
  }
  .gap-lg-3 {
    gap: 1rem ;
  }
  .gap-lg-4 {
    gap: 1.5rem ;
  }
  .gap-lg-5 {
    gap: 3rem ;
  }
  .justify-content-lg-start {
    -webkit-box-pack: start ;
    -ms-flex-pack: start ;
    justify-content: flex-start ;
  }
  .justify-content-lg-end {
    -webkit-box-pack: end ;
    -ms-flex-pack: end ;
    justify-content: flex-end ;
  }
  .justify-content-lg-center {
    -webkit-box-pack: center ;
    -ms-flex-pack: center ;
    justify-content: center ;
  }
  .justify-content-lg-between {
    -webkit-box-pack: justify ;
    -ms-flex-pack: justify ;
    justify-content: space-between ;
  }
  .justify-content-lg-around {
    -ms-flex-pack: distribute ;
    justify-content: space-around ;
  }
  .justify-content-lg-evenly {
    -webkit-box-pack: space-evenly ;
    -ms-flex-pack: space-evenly ;
    justify-content: space-evenly ;
  }
  .align-items-lg-start {
    -webkit-box-align: start ;
    -ms-flex-align: start ;
    align-items: flex-start ;
  }
  .align-items-lg-end {
    -webkit-box-align: end ;
    -ms-flex-align: end ;
    align-items: flex-end ;
  }
  .align-items-lg-center {
    -webkit-box-align: center ;
    -ms-flex-align: center ;
    align-items: center ;
  }
  .align-items-lg-baseline {
    -webkit-box-align: baseline ;
    -ms-flex-align: baseline ;
    align-items: baseline ;
  }
  .align-items-lg-stretch {
    -webkit-box-align: stretch ;
    -ms-flex-align: stretch ;
    align-items: stretch ;
  }
  .align-content-lg-start {
    -ms-flex-line-pack: start ;
    align-content: flex-start ;
  }
  .align-content-lg-end {
    -ms-flex-line-pack: end ;
    align-content: flex-end ;
  }
  .align-content-lg-center {
    -ms-flex-line-pack: center ;
    align-content: center ;
  }
  .align-content-lg-between {
    -ms-flex-line-pack: justify ;
    align-content: space-between ;
  }
  .align-content-lg-around {
    -ms-flex-line-pack: distribute ;
    align-content: space-around ;
  }
  .align-content-lg-stretch {
    -ms-flex-line-pack: stretch ;
    align-content: stretch ;
  }
  .align-self-lg-auto {
    -ms-flex-item-align: auto ;
    align-self: auto ;
  }
  .align-self-lg-start {
    -ms-flex-item-align: start ;
    align-self: flex-start ;
  }
  .align-self-lg-end {
    -ms-flex-item-align: end ;
    align-self: flex-end ;
  }
  .align-self-lg-center {
    -ms-flex-item-align: center ;
    align-self: center ;
  }
  .align-self-lg-baseline {
    -ms-flex-item-align: baseline ;
    align-self: baseline ;
  }
  .align-self-lg-stretch {
    -ms-flex-item-align: stretch ;
    align-self: stretch ;
  }
  .order-lg-first {
    -webkit-box-ordinal-group: 0 ;
    -ms-flex-order: -1 ;
    order: -1 ;
  }
  .order-lg-0 {
    -webkit-box-ordinal-group: 1 ;
    -ms-flex-order: 0 ;
    order: 0 ;
  }
  .order-lg-1 {
    -webkit-box-ordinal-group: 2 ;
    -ms-flex-order: 1 ;
    order: 1 ;
  }
  .order-lg-2 {
    -webkit-box-ordinal-group: 3 ;
    -ms-flex-order: 2 ;
    order: 2 ;
  }
  .order-lg-3 {
    -webkit-box-ordinal-group: 4 ;
    -ms-flex-order: 3 ;
    order: 3 ;
  }
  .order-lg-4 {
    -webkit-box-ordinal-group: 5 ;
    -ms-flex-order: 4 ;
    order: 4 ;
  }
  .order-lg-5 {
    -webkit-box-ordinal-group: 6 ;
    -ms-flex-order: 5 ;
    order: 5 ;
  }
  .order-lg-last {
    -webkit-box-ordinal-group: 7 ;
    -ms-flex-order: 6 ;
    order: 6 ;
  }
  .m-lg-0 {
    margin: 0 ;
  }
  .m-lg-1 {
    margin: 0.25rem ;
  }
  .m-lg-2 {
    margin: 0.5rem ;
  }
  .m-lg-3 {
    margin: 1rem ;
  }
  .m-lg-4 {
    margin: 1.5rem ;
  }
  .m-lg-5 {
    margin: 3rem ;
  }
  .m-lg-auto {
    margin: auto ;
  }
  .mx-lg-0 {
    margin-right: 0 ;
    margin-left: 0 ;
  }
  .mx-lg-1 {
    margin-right: 0.25rem ;
    margin-left: 0.25rem ;
  }
  .mx-lg-2 {
    margin-right: 0.5rem ;
    margin-left: 0.5rem ;
  }
  .mx-lg-3 {
    margin-right: 1rem ;
    margin-left: 1rem ;
  }
  .mx-lg-4 {
    margin-right: 1.5rem ;
    margin-left: 1.5rem ;
  }
  .mx-lg-5 {
    margin-right: 3rem ;
    margin-left: 3rem ;
  }
  .mx-lg-auto {
    margin-right: auto ;
    margin-left: auto ;
  }
  .my-lg-0 {
    margin-top: 0 ;
    margin-bottom: 0 ;
  }
  .my-lg-1 {
    margin-top: 0.25rem ;
    margin-bottom: 0.25rem ;
  }
  .my-lg-2 {
    margin-top: 0.5rem ;
    margin-bottom: 0.5rem ;
  }
  .my-lg-3 {
    margin-top: 1rem ;
    margin-bottom: 1rem ;
  }
  .my-lg-4 {
    margin-top: 1.5rem ;
    margin-bottom: 1.5rem ;
  }
  .my-lg-5 {
    margin-top: 3rem ;
    margin-bottom: 3rem ;
  }
  .my-lg-auto {
    margin-top: auto ;
    margin-bottom: auto ;
  }
  .mt-lg-0 {
    margin-top: 0 ;
  }
  .mt-lg-1 {
    margin-top: 0.25rem ;
  }
  .mt-lg-2 {
    margin-top: 0.5rem ;
  }
  .mt-lg-3 {
    margin-top: 1rem ;
  }
  .mt-lg-4 {
    margin-top: 1.5rem ;
  }
  .mt-lg-5 {
    margin-top: 3rem ;
  }
  .mt-lg-auto {
    margin-top: auto ;
  }
  .me-lg-0 {
    margin-right: 0 ;
  }
  .me-lg-1 {
    margin-right: 0.25rem ;
  }
  .me-lg-2 {
    margin-right: 0.5rem ;
  }
  .me-lg-3 {
    margin-right: 1rem ;
  }
  .me-lg-4 {
    margin-right: 1.5rem ;
  }
  .me-lg-5 {
    margin-right: 3rem ;
  }
  .me-lg-auto {
    margin-right: auto ;
  }
  .mb-lg-0 {
    margin-bottom: 0 ;
  }
  .mb-lg-1 {
    margin-bottom: 0.25rem ;
  }
  .mb-lg-2 {
    margin-bottom: 0.5rem ;
  }
  .mb-lg-3 {
    margin-bottom: 1rem ;
  }
  .mb-lg-4 {
    margin-bottom: 1.5rem ;
  }
  .mb-lg-5 {
    margin-bottom: 3rem ;
  }
  .mb-lg-auto {
    margin-bottom: auto ;
  }
  .ms-lg-0 {
    margin-left: 0 ;
  }
  .ms-lg-1 {
    margin-left: 0.25rem ;
  }
  .ms-lg-2 {
    margin-left: 0.5rem ;
  }
  .ms-lg-3 {
    margin-left: 1rem ;
  }
  .ms-lg-4 {
    margin-left: 1.5rem ;
  }
  .ms-lg-5 {
    margin-left: 3rem ;
  }
  .ms-lg-auto {
    margin-left: auto ;
  }
  .m-lg-n1 {
    margin: -0.25rem ;
  }
  .m-lg-n2 {
    margin: -0.5rem ;
  }
  .m-lg-n3 {
    margin: -1rem ;
  }
  .m-lg-n4 {
    margin: -1.5rem ;
  }
  .m-lg-n5 {
    margin: -3rem ;
  }
  .mx-lg-n1 {
    margin-right: -0.25rem ;
    margin-left: -0.25rem ;
  }
  .mx-lg-n2 {
    margin-right: -0.5rem ;
    margin-left: -0.5rem ;
  }
  .mx-lg-n3 {
    margin-right: -1rem ;
    margin-left: -1rem ;
  }
  .mx-lg-n4 {
    margin-right: -1.5rem ;
    margin-left: -1.5rem ;
  }
  .mx-lg-n5 {
    margin-right: -3rem ;
    margin-left: -3rem ;
  }
  .my-lg-n1 {
    margin-top: -0.25rem ;
    margin-bottom: -0.25rem ;
  }
  .my-lg-n2 {
    margin-top: -0.5rem ;
    margin-bottom: -0.5rem ;
  }
  .my-lg-n3 {
    margin-top: -1rem ;
    margin-bottom: -1rem ;
  }
  .my-lg-n4 {
    margin-top: -1.5rem ;
    margin-bottom: -1.5rem ;
  }
  .my-lg-n5 {
    margin-top: -3rem ;
    margin-bottom: -3rem ;
  }
  .mt-lg-n1 {
    margin-top: -0.25rem ;
  }
  .mt-lg-n2 {
    margin-top: -0.5rem ;
  }
  .mt-lg-n3 {
    margin-top: -1rem ;
  }
  .mt-lg-n4 {
    margin-top: -1.5rem ;
  }
  .mt-lg-n5 {
    margin-top: -3rem ;
  }
  .me-lg-n1 {
    margin-right: -0.25rem ;
  }
  .me-lg-n2 {
    margin-right: -0.5rem ;
  }
  .me-lg-n3 {
    margin-right: -1rem ;
  }
  .me-lg-n4 {
    margin-right: -1.5rem ;
  }
  .me-lg-n5 {
    margin-right: -3rem ;
  }
  .mb-lg-n1 {
    margin-bottom: -0.25rem ;
  }
  .mb-lg-n2 {
    margin-bottom: -0.5rem ;
  }
  .mb-lg-n3 {
    margin-bottom: -1rem ;
  }
  .mb-lg-n4 {
    margin-bottom: -1.5rem ;
  }
  .mb-lg-n5 {
    margin-bottom: -3rem ;
  }
  .ms-lg-n1 {
    margin-left: -0.25rem ;
  }
  .ms-lg-n2 {
    margin-left: -0.5rem ;
  }
  .ms-lg-n3 {
    margin-left: -1rem ;
  }
  .ms-lg-n4 {
    margin-left: -1.5rem ;
  }
  .ms-lg-n5 {
    margin-left: -3rem ;
  }
  .p-lg-0 {
    padding: 0 ;
  }
  .p-lg-1 {
    padding: 0.25rem ;
  }
  .p-lg-2 {
    padding: 0.5rem ;
  }
  .p-lg-3 {
    padding: 1rem ;
  }
  .p-lg-4 {
    padding: 1.5rem ;
  }
  .p-lg-5 {
    padding: 3rem ;
  }
  .px-lg-0 {
    padding-right: 0 ;
    padding-left: 0 ;
  }
  .px-lg-1 {
    padding-right: 0.25rem ;
    padding-left: 0.25rem ;
  }
  .px-lg-2 {
    padding-right: 0.5rem ;
    padding-left: 0.5rem ;
  }
  .px-lg-3 {
    padding-right: 1rem ;
    padding-left: 1rem ;
  }
  .px-lg-4 {
    padding-right: 1.5rem ;
    padding-left: 1.5rem ;
  }
  .px-lg-5 {
    padding-right: 3rem ;
    padding-left: 3rem ;
  }
  .py-lg-0 {
    padding-top: 0 ;
    padding-bottom: 0 ;
  }
  .py-lg-1 {
    padding-top: 0.25rem ;
    padding-bottom: 0.25rem ;
  }
  .py-lg-2 {
    padding-top: 0.5rem ;
    padding-bottom: 0.5rem ;
  }
  .py-lg-3 {
    padding-top: 1rem ;
    padding-bottom: 1rem ;
  }
  .py-lg-4 {
    padding-top: 1.5rem ;
    padding-bottom: 1.5rem ;
  }
  .py-lg-5 {
    padding-top: 3rem ;
    padding-bottom: 3rem ;
  }
  .pt-lg-0 {
    padding-top: 0 ;
  }
  .pt-lg-1 {
    padding-top: 0.25rem ;
  }
  .pt-lg-2 {
    padding-top: 0.5rem ;
  }
  .pt-lg-3 {
    padding-top: 1rem ;
  }
  .pt-lg-4 {
    padding-top: 1.5rem ;
  }
  .pt-lg-5 {
    padding-top: 3rem ;
  }
  .pe-lg-0 {
    padding-right: 0 ;
  }
  .pe-lg-1 {
    padding-right: 0.25rem ;
  }
  .pe-lg-2 {
    padding-right: 0.5rem ;
  }
  .pe-lg-3 {
    padding-right: 1rem ;
  }
  .pe-lg-4 {
    padding-right: 1.5rem ;
  }
  .pe-lg-5 {
    padding-right: 3rem ;
  }
  .pb-lg-0 {
    padding-bottom: 0 ;
  }
  .pb-lg-1 {
    padding-bottom: 0.25rem ;
  }
  .pb-lg-2 {
    padding-bottom: 0.5rem ;
  }
  .pb-lg-3 {
    padding-bottom: 1rem ;
  }
  .pb-lg-4 {
    padding-bottom: 1.5rem ;
  }
  .pb-lg-5 {
    padding-bottom: 3rem ;
  }
  .ps-lg-0 {
    padding-left: 0 ;
  }
  .ps-lg-1 {
    padding-left: 0.25rem ;
  }
  .ps-lg-2 {
    padding-left: 0.5rem ;
  }
  .ps-lg-3 {
    padding-left: 1rem ;
  }
  .ps-lg-4 {
    padding-left: 1.5rem ;
  }
  .ps-lg-5 {
    padding-left: 3rem ;
  }
  .text-lg-start {
    text-align: left ;
  }
  .text-lg-end {
    text-align: right ;
  }
  .text-lg-center {
    text-align: center ;
  }
}
@media (min-width: 1200px) {
  .float-xl-start {
    float: left ;
  }
  .float-xl-end {
    float: right ;
  }
  .float-xl-none {
    float: none ;
  }
  .d-xl-inline {
    display: inline ;
  }
  .d-xl-inline-block {
    display: inline-block ;
  }
  .d-xl-block {
    display: block ;
  }
  .d-xl-grid {
    display: grid ;
  }
  .d-xl-table {
    display: table ;
  }
  .d-xl-table-row {
    display: table-row ;
  }
  .d-xl-table-cell {
    display: table-cell ;
  }
  .d-xl-flex {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
  }
  .d-xl-inline-flex {
    display: -webkit-inline-box ;
    display: -ms-inline-flexbox ;
    display: inline-flex ;
  }
  .d-xl-none {
    display: none ;
  }
  .flex-xl-fill {
    -webkit-box-flex: 1 ;
    -ms-flex: 1 1 auto ;
    flex: 1 1 auto ;
  }
  .flex-xl-row {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: row ;
    flex-direction: row ;
  }
  .flex-xl-column {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: column ;
    flex-direction: column ;
  }
  .flex-xl-row-reverse {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: row-reverse ;
    flex-direction: row-reverse ;
  }
  .flex-xl-column-reverse {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: column-reverse ;
    flex-direction: column-reverse ;
  }
  .flex-xl-grow-0 {
    -webkit-box-flex: 0 ;
    -ms-flex-positive: 0 ;
    flex-grow: 0 ;
  }
  .flex-xl-grow-1 {
    -webkit-box-flex: 1 ;
    -ms-flex-positive: 1 ;
    flex-grow: 1 ;
  }
  .flex-xl-shrink-0 {
    -ms-flex-negative: 0 ;
    flex-shrink: 0 ;
  }
  .flex-xl-shrink-1 {
    -ms-flex-negative: 1 ;
    flex-shrink: 1 ;
  }
  .flex-xl-wrap {
    -ms-flex-wrap: wrap ;
    flex-wrap: wrap ;
  }
  .flex-xl-nowrap {
    -ms-flex-wrap: nowrap ;
    flex-wrap: nowrap ;
  }
  .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse ;
    flex-wrap: wrap-reverse ;
  }
  .gap-xl-0 {
    gap: 0 ;
  }
  .gap-xl-1 {
    gap: 0.25rem ;
  }
  .gap-xl-2 {
    gap: 0.5rem ;
  }
  .gap-xl-3 {
    gap: 1rem ;
  }
  .gap-xl-4 {
    gap: 1.5rem ;
  }
  .gap-xl-5 {
    gap: 3rem ;
  }
  .justify-content-xl-start {
    -webkit-box-pack: start ;
    -ms-flex-pack: start ;
    justify-content: flex-start ;
  }
  .justify-content-xl-end {
    -webkit-box-pack: end ;
    -ms-flex-pack: end ;
    justify-content: flex-end ;
  }
  .justify-content-xl-center {
    -webkit-box-pack: center ;
    -ms-flex-pack: center ;
    justify-content: center ;
  }
  .justify-content-xl-between {
    -webkit-box-pack: justify ;
    -ms-flex-pack: justify ;
    justify-content: space-between ;
  }
  .justify-content-xl-around {
    -ms-flex-pack: distribute ;
    justify-content: space-around ;
  }
  .justify-content-xl-evenly {
    -webkit-box-pack: space-evenly ;
    -ms-flex-pack: space-evenly ;
    justify-content: space-evenly ;
  }
  .align-items-xl-start {
    -webkit-box-align: start ;
    -ms-flex-align: start ;
    align-items: flex-start ;
  }
  .align-items-xl-end {
    -webkit-box-align: end ;
    -ms-flex-align: end ;
    align-items: flex-end ;
  }
  .align-items-xl-center {
    -webkit-box-align: center ;
    -ms-flex-align: center ;
    align-items: center ;
  }
  .align-items-xl-baseline {
    -webkit-box-align: baseline ;
    -ms-flex-align: baseline ;
    align-items: baseline ;
  }
  .align-items-xl-stretch {
    -webkit-box-align: stretch ;
    -ms-flex-align: stretch ;
    align-items: stretch ;
  }
  .align-content-xl-start {
    -ms-flex-line-pack: start ;
    align-content: flex-start ;
  }
  .align-content-xl-end {
    -ms-flex-line-pack: end ;
    align-content: flex-end ;
  }
  .align-content-xl-center {
    -ms-flex-line-pack: center ;
    align-content: center ;
  }
  .align-content-xl-between {
    -ms-flex-line-pack: justify ;
    align-content: space-between ;
  }
  .align-content-xl-around {
    -ms-flex-line-pack: distribute ;
    align-content: space-around ;
  }
  .align-content-xl-stretch {
    -ms-flex-line-pack: stretch ;
    align-content: stretch ;
  }
  .align-self-xl-auto {
    -ms-flex-item-align: auto ;
    align-self: auto ;
  }
  .align-self-xl-start {
    -ms-flex-item-align: start ;
    align-self: flex-start ;
  }
  .align-self-xl-end {
    -ms-flex-item-align: end ;
    align-self: flex-end ;
  }
  .align-self-xl-center {
    -ms-flex-item-align: center ;
    align-self: center ;
  }
  .align-self-xl-baseline {
    -ms-flex-item-align: baseline ;
    align-self: baseline ;
  }
  .align-self-xl-stretch {
    -ms-flex-item-align: stretch ;
    align-self: stretch ;
  }
  .order-xl-first {
    -webkit-box-ordinal-group: 0 ;
    -ms-flex-order: -1 ;
    order: -1 ;
  }
  .order-xl-0 {
    -webkit-box-ordinal-group: 1 ;
    -ms-flex-order: 0 ;
    order: 0 ;
  }
  .order-xl-1 {
    -webkit-box-ordinal-group: 2 ;
    -ms-flex-order: 1 ;
    order: 1 ;
  }
  .order-xl-2 {
    -webkit-box-ordinal-group: 3 ;
    -ms-flex-order: 2 ;
    order: 2 ;
  }
  .order-xl-3 {
    -webkit-box-ordinal-group: 4 ;
    -ms-flex-order: 3 ;
    order: 3 ;
  }
  .order-xl-4 {
    -webkit-box-ordinal-group: 5 ;
    -ms-flex-order: 4 ;
    order: 4 ;
  }
  .order-xl-5 {
    -webkit-box-ordinal-group: 6 ;
    -ms-flex-order: 5 ;
    order: 5 ;
  }
  .order-xl-last {
    -webkit-box-ordinal-group: 7 ;
    -ms-flex-order: 6 ;
    order: 6 ;
  }
  .m-xl-0 {
    margin: 0 ;
  }
  .m-xl-1 {
    margin: 0.25rem ;
  }
  .m-xl-2 {
    margin: 0.5rem ;
  }
  .m-xl-3 {
    margin: 1rem ;
  }
  .m-xl-4 {
    margin: 1.5rem ;
  }
  .m-xl-5 {
    margin: 3rem ;
  }
  .m-xl-auto {
    margin: auto ;
  }
  .mx-xl-0 {
    margin-right: 0 ;
    margin-left: 0 ;
  }
  .mx-xl-1 {
    margin-right: 0.25rem ;
    margin-left: 0.25rem ;
  }
  .mx-xl-2 {
    margin-right: 0.5rem ;
    margin-left: 0.5rem ;
  }
  .mx-xl-3 {
    margin-right: 1rem ;
    margin-left: 1rem ;
  }
  .mx-xl-4 {
    margin-right: 1.5rem ;
    margin-left: 1.5rem ;
  }
  .mx-xl-5 {
    margin-right: 3rem ;
    margin-left: 3rem ;
  }
  .mx-xl-auto {
    margin-right: auto ;
    margin-left: auto ;
  }
  .my-xl-0 {
    margin-top: 0 ;
    margin-bottom: 0 ;
  }
  .my-xl-1 {
    margin-top: 0.25rem ;
    margin-bottom: 0.25rem ;
  }
  .my-xl-2 {
    margin-top: 0.5rem ;
    margin-bottom: 0.5rem ;
  }
  .my-xl-3 {
    margin-top: 1rem ;
    margin-bottom: 1rem ;
  }
  .my-xl-4 {
    margin-top: 1.5rem ;
    margin-bottom: 1.5rem ;
  }
  .my-xl-5 {
    margin-top: 3rem ;
    margin-bottom: 3rem ;
  }
  .my-xl-auto {
    margin-top: auto ;
    margin-bottom: auto ;
  }
  .mt-xl-0 {
    margin-top: 0 ;
  }
  .mt-xl-1 {
    margin-top: 0.25rem ;
  }
  .mt-xl-2 {
    margin-top: 0.5rem ;
  }
  .mt-xl-3 {
    margin-top: 1rem ;
  }
  .mt-xl-4 {
    margin-top: 1.5rem ;
  }
  .mt-xl-5 {
    margin-top: 3rem ;
  }
  .mt-xl-auto {
    margin-top: auto ;
  }
  .me-xl-0 {
    margin-right: 0 ;
  }
  .me-xl-1 {
    margin-right: 0.25rem ;
  }
  .me-xl-2 {
    margin-right: 0.5rem ;
  }
  .me-xl-3 {
    margin-right: 1rem ;
  }
  .me-xl-4 {
    margin-right: 1.5rem ;
  }
  .me-xl-5 {
    margin-right: 3rem ;
  }
  .me-xl-auto {
    margin-right: auto ;
  }
  .mb-xl-0 {
    margin-bottom: 0 ;
  }
  .mb-xl-1 {
    margin-bottom: 0.25rem ;
  }
  .mb-xl-2 {
    margin-bottom: 0.5rem ;
  }
  .mb-xl-3 {
    margin-bottom: 1rem ;
  }
  .mb-xl-4 {
    margin-bottom: 1.5rem ;
  }
  .mb-xl-5 {
    margin-bottom: 3rem ;
  }
  .mb-xl-auto {
    margin-bottom: auto ;
  }
  .ms-xl-0 {
    margin-left: 0 ;
  }
  .ms-xl-1 {
    margin-left: 0.25rem ;
  }
  .ms-xl-2 {
    margin-left: 0.5rem ;
  }
  .ms-xl-3 {
    margin-left: 1rem ;
  }
  .ms-xl-4 {
    margin-left: 1.5rem ;
  }
  .ms-xl-5 {
    margin-left: 3rem ;
  }
  .ms-xl-auto {
    margin-left: auto ;
  }
  .m-xl-n1 {
    margin: -0.25rem ;
  }
  .m-xl-n2 {
    margin: -0.5rem ;
  }
  .m-xl-n3 {
    margin: -1rem ;
  }
  .m-xl-n4 {
    margin: -1.5rem ;
  }
  .m-xl-n5 {
    margin: -3rem ;
  }
  .mx-xl-n1 {
    margin-right: -0.25rem ;
    margin-left: -0.25rem ;
  }
  .mx-xl-n2 {
    margin-right: -0.5rem ;
    margin-left: -0.5rem ;
  }
  .mx-xl-n3 {
    margin-right: -1rem ;
    margin-left: -1rem ;
  }
  .mx-xl-n4 {
    margin-right: -1.5rem ;
    margin-left: -1.5rem ;
  }
  .mx-xl-n5 {
    margin-right: -3rem ;
    margin-left: -3rem ;
  }
  .my-xl-n1 {
    margin-top: -0.25rem ;
    margin-bottom: -0.25rem ;
  }
  .my-xl-n2 {
    margin-top: -0.5rem ;
    margin-bottom: -0.5rem ;
  }
  .my-xl-n3 {
    margin-top: -1rem ;
    margin-bottom: -1rem ;
  }
  .my-xl-n4 {
    margin-top: -1.5rem ;
    margin-bottom: -1.5rem ;
  }
  .my-xl-n5 {
    margin-top: -3rem ;
    margin-bottom: -3rem ;
  }
  .mt-xl-n1 {
    margin-top: -0.25rem ;
  }
  .mt-xl-n2 {
    margin-top: -0.5rem ;
  }
  .mt-xl-n3 {
    margin-top: -1rem ;
  }
  .mt-xl-n4 {
    margin-top: -1.5rem ;
  }
  .mt-xl-n5 {
    margin-top: -3rem ;
  }
  .me-xl-n1 {
    margin-right: -0.25rem ;
  }
  .me-xl-n2 {
    margin-right: -0.5rem ;
  }
  .me-xl-n3 {
    margin-right: -1rem ;
  }
  .me-xl-n4 {
    margin-right: -1.5rem ;
  }
  .me-xl-n5 {
    margin-right: -3rem ;
  }
  .mb-xl-n1 {
    margin-bottom: -0.25rem ;
  }
  .mb-xl-n2 {
    margin-bottom: -0.5rem ;
  }
  .mb-xl-n3 {
    margin-bottom: -1rem ;
  }
  .mb-xl-n4 {
    margin-bottom: -1.5rem ;
  }
  .mb-xl-n5 {
    margin-bottom: -3rem ;
  }
  .ms-xl-n1 {
    margin-left: -0.25rem ;
  }
  .ms-xl-n2 {
    margin-left: -0.5rem ;
  }
  .ms-xl-n3 {
    margin-left: -1rem ;
  }
  .ms-xl-n4 {
    margin-left: -1.5rem ;
  }
  .ms-xl-n5 {
    margin-left: -3rem ;
  }
  .p-xl-0 {
    padding: 0 ;
  }
  .p-xl-1 {
    padding: 0.25rem ;
  }
  .p-xl-2 {
    padding: 0.5rem ;
  }
  .p-xl-3 {
    padding: 1rem ;
  }
  .p-xl-4 {
    padding: 1.5rem ;
  }
  .p-xl-5 {
    padding: 3rem ;
  }
  .px-xl-0 {
    padding-right: 0 ;
    padding-left: 0 ;
  }
  .px-xl-1 {
    padding-right: 0.25rem ;
    padding-left: 0.25rem ;
  }
  .px-xl-2 {
    padding-right: 0.5rem ;
    padding-left: 0.5rem ;
  }
  .px-xl-3 {
    padding-right: 1rem ;
    padding-left: 1rem ;
  }
  .px-xl-4 {
    padding-right: 1.5rem ;
    padding-left: 1.5rem ;
  }
  .px-xl-5 {
    padding-right: 3rem ;
    padding-left: 3rem ;
  }
  .py-xl-0 {
    padding-top: 0 ;
    padding-bottom: 0 ;
  }
  .py-xl-1 {
    padding-top: 0.25rem ;
    padding-bottom: 0.25rem ;
  }
  .py-xl-2 {
    padding-top: 0.5rem ;
    padding-bottom: 0.5rem ;
  }
  .py-xl-3 {
    padding-top: 1rem ;
    padding-bottom: 1rem ;
  }
  .py-xl-4 {
    padding-top: 1.5rem ;
    padding-bottom: 1.5rem ;
  }
  .py-xl-5 {
    padding-top: 3rem ;
    padding-bottom: 3rem ;
  }
  .pt-xl-0 {
    padding-top: 0 ;
  }
  .pt-xl-1 {
    padding-top: 0.25rem ;
  }
  .pt-xl-2 {
    padding-top: 0.5rem ;
  }
  .pt-xl-3 {
    padding-top: 1rem ;
  }
  .pt-xl-4 {
    padding-top: 1.5rem ;
  }
  .pt-xl-5 {
    padding-top: 3rem ;
  }
  .pe-xl-0 {
    padding-right: 0 ;
  }
  .pe-xl-1 {
    padding-right: 0.25rem ;
  }
  .pe-xl-2 {
    padding-right: 0.5rem ;
  }
  .pe-xl-3 {
    padding-right: 1rem ;
  }
  .pe-xl-4 {
    padding-right: 1.5rem ;
  }
  .pe-xl-5 {
    padding-right: 3rem ;
  }
  .pb-xl-0 {
    padding-bottom: 0 ;
  }
  .pb-xl-1 {
    padding-bottom: 0.25rem ;
  }
  .pb-xl-2 {
    padding-bottom: 0.5rem ;
  }
  .pb-xl-3 {
    padding-bottom: 1rem ;
  }
  .pb-xl-4 {
    padding-bottom: 1.5rem ;
  }
  .pb-xl-5 {
    padding-bottom: 3rem ;
  }
  .ps-xl-0 {
    padding-left: 0 ;
  }
  .ps-xl-1 {
    padding-left: 0.25rem ;
  }
  .ps-xl-2 {
    padding-left: 0.5rem ;
  }
  .ps-xl-3 {
    padding-left: 1rem ;
  }
  .ps-xl-4 {
    padding-left: 1.5rem ;
  }
  .ps-xl-5 {
    padding-left: 3rem ;
  }
  .text-xl-start {
    text-align: left ;
  }
  .text-xl-end {
    text-align: right ;
  }
  .text-xl-center {
    text-align: center ;
  }
}
@media (min-width: 1400px) {
  .float-xxl-start {
    float: left ;
  }
  .float-xxl-end {
    float: right ;
  }
  .float-xxl-none {
    float: none ;
  }
  .d-xxl-inline {
    display: inline ;
  }
  .d-xxl-inline-block {
    display: inline-block ;
  }
  .d-xxl-block {
    display: block ;
  }
  .d-xxl-grid {
    display: grid ;
  }
  .d-xxl-table {
    display: table ;
  }
  .d-xxl-table-row {
    display: table-row ;
  }
  .d-xxl-table-cell {
    display: table-cell ;
  }
  .d-xxl-flex {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
  }
  .d-xxl-inline-flex {
    display: -webkit-inline-box ;
    display: -ms-inline-flexbox ;
    display: inline-flex ;
  }
  .d-xxl-none {
    display: none ;
  }
  .flex-xxl-fill {
    -webkit-box-flex: 1 ;
    -ms-flex: 1 1 auto ;
    flex: 1 1 auto ;
  }
  .flex-xxl-row {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: row ;
    flex-direction: row ;
  }
  .flex-xxl-column {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: normal ;
    -ms-flex-direction: column ;
    flex-direction: column ;
  }
  .flex-xxl-row-reverse {
    -webkit-box-orient: horizontal ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: row-reverse ;
    flex-direction: row-reverse ;
  }
  .flex-xxl-column-reverse {
    -webkit-box-orient: vertical ;
    -webkit-box-direction: reverse ;
    -ms-flex-direction: column-reverse ;
    flex-direction: column-reverse ;
  }
  .flex-xxl-grow-0 {
    -webkit-box-flex: 0 ;
    -ms-flex-positive: 0 ;
    flex-grow: 0 ;
  }
  .flex-xxl-grow-1 {
    -webkit-box-flex: 1 ;
    -ms-flex-positive: 1 ;
    flex-grow: 1 ;
  }
  .flex-xxl-shrink-0 {
    -ms-flex-negative: 0 ;
    flex-shrink: 0 ;
  }
  .flex-xxl-shrink-1 {
    -ms-flex-negative: 1 ;
    flex-shrink: 1 ;
  }
  .flex-xxl-wrap {
    -ms-flex-wrap: wrap ;
    flex-wrap: wrap ;
  }
  .flex-xxl-nowrap {
    -ms-flex-wrap: nowrap ;
    flex-wrap: nowrap ;
  }
  .flex-xxl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse ;
    flex-wrap: wrap-reverse ;
  }
  .gap-xxl-0 {
    gap: 0 ;
  }
  .gap-xxl-1 {
    gap: 0.25rem ;
  }
  .gap-xxl-2 {
    gap: 0.5rem ;
  }
  .gap-xxl-3 {
    gap: 1rem ;
  }
  .gap-xxl-4 {
    gap: 1.5rem ;
  }
  .gap-xxl-5 {
    gap: 3rem ;
  }
  .justify-content-xxl-start {
    -webkit-box-pack: start ;
    -ms-flex-pack: start ;
    justify-content: flex-start ;
  }
  .justify-content-xxl-end {
    -webkit-box-pack: end ;
    -ms-flex-pack: end ;
    justify-content: flex-end ;
  }
  .justify-content-xxl-center {
    -webkit-box-pack: center ;
    -ms-flex-pack: center ;
    justify-content: center ;
  }
  .justify-content-xxl-between {
    -webkit-box-pack: justify ;
    -ms-flex-pack: justify ;
    justify-content: space-between ;
  }
  .justify-content-xxl-around {
    -ms-flex-pack: distribute ;
    justify-content: space-around ;
  }
  .justify-content-xxl-evenly {
    -webkit-box-pack: space-evenly ;
    -ms-flex-pack: space-evenly ;
    justify-content: space-evenly ;
  }
  .align-items-xxl-start {
    -webkit-box-align: start ;
    -ms-flex-align: start ;
    align-items: flex-start ;
  }
  .align-items-xxl-end {
    -webkit-box-align: end ;
    -ms-flex-align: end ;
    align-items: flex-end ;
  }
  .align-items-xxl-center {
    -webkit-box-align: center ;
    -ms-flex-align: center ;
    align-items: center ;
  }
  .align-items-xxl-baseline {
    -webkit-box-align: baseline ;
    -ms-flex-align: baseline ;
    align-items: baseline ;
  }
  .align-items-xxl-stretch {
    -webkit-box-align: stretch ;
    -ms-flex-align: stretch ;
    align-items: stretch ;
  }
  .align-content-xxl-start {
    -ms-flex-line-pack: start ;
    align-content: flex-start ;
  }
  .align-content-xxl-end {
    -ms-flex-line-pack: end ;
    align-content: flex-end ;
  }
  .align-content-xxl-center {
    -ms-flex-line-pack: center ;
    align-content: center ;
  }
  .align-content-xxl-between {
    -ms-flex-line-pack: justify ;
    align-content: space-between ;
  }
  .align-content-xxl-around {
    -ms-flex-line-pack: distribute ;
    align-content: space-around ;
  }
  .align-content-xxl-stretch {
    -ms-flex-line-pack: stretch ;
    align-content: stretch ;
  }
  .align-self-xxl-auto {
    -ms-flex-item-align: auto ;
    align-self: auto ;
  }
  .align-self-xxl-start {
    -ms-flex-item-align: start ;
    align-self: flex-start ;
  }
  .align-self-xxl-end {
    -ms-flex-item-align: end ;
    align-self: flex-end ;
  }
  .align-self-xxl-center {
    -ms-flex-item-align: center ;
    align-self: center ;
  }
  .align-self-xxl-baseline {
    -ms-flex-item-align: baseline ;
    align-self: baseline ;
  }
  .align-self-xxl-stretch {
    -ms-flex-item-align: stretch ;
    align-self: stretch ;
  }
  .order-xxl-first {
    -webkit-box-ordinal-group: 0 ;
    -ms-flex-order: -1 ;
    order: -1 ;
  }
  .order-xxl-0 {
    -webkit-box-ordinal-group: 1 ;
    -ms-flex-order: 0 ;
    order: 0 ;
  }
  .order-xxl-1 {
    -webkit-box-ordinal-group: 2 ;
    -ms-flex-order: 1 ;
    order: 1 ;
  }
  .order-xxl-2 {
    -webkit-box-ordinal-group: 3 ;
    -ms-flex-order: 2 ;
    order: 2 ;
  }
  .order-xxl-3 {
    -webkit-box-ordinal-group: 4 ;
    -ms-flex-order: 3 ;
    order: 3 ;
  }
  .order-xxl-4 {
    -webkit-box-ordinal-group: 5 ;
    -ms-flex-order: 4 ;
    order: 4 ;
  }
  .order-xxl-5 {
    -webkit-box-ordinal-group: 6 ;
    -ms-flex-order: 5 ;
    order: 5 ;
  }
  .order-xxl-last {
    -webkit-box-ordinal-group: 7 ;
    -ms-flex-order: 6 ;
    order: 6 ;
  }
  .m-xxl-0 {
    margin: 0 ;
  }
  .m-xxl-1 {
    margin: 0.25rem ;
  }
  .m-xxl-2 {
    margin: 0.5rem ;
  }
  .m-xxl-3 {
    margin: 1rem ;
  }
  .m-xxl-4 {
    margin: 1.5rem ;
  }
  .m-xxl-5 {
    margin: 3rem ;
  }
  .m-xxl-auto {
    margin: auto ;
  }
  .mx-xxl-0 {
    margin-right: 0 ;
    margin-left: 0 ;
  }
  .mx-xxl-1 {
    margin-right: 0.25rem ;
    margin-left: 0.25rem ;
  }
  .mx-xxl-2 {
    margin-right: 0.5rem ;
    margin-left: 0.5rem ;
  }
  .mx-xxl-3 {
    margin-right: 1rem ;
    margin-left: 1rem ;
  }
  .mx-xxl-4 {
    margin-right: 1.5rem ;
    margin-left: 1.5rem ;
  }
  .mx-xxl-5 {
    margin-right: 3rem ;
    margin-left: 3rem ;
  }
  .mx-xxl-auto {
    margin-right: auto ;
    margin-left: auto ;
  }
  .my-xxl-0 {
    margin-top: 0 ;
    margin-bottom: 0 ;
  }
  .my-xxl-1 {
    margin-top: 0.25rem ;
    margin-bottom: 0.25rem ;
  }
  .my-xxl-2 {
    margin-top: 0.5rem ;
    margin-bottom: 0.5rem ;
  }
  .my-xxl-3 {
    margin-top: 1rem ;
    margin-bottom: 1rem ;
  }
  .my-xxl-4 {
    margin-top: 1.5rem ;
    margin-bottom: 1.5rem ;
  }
  .my-xxl-5 {
    margin-top: 3rem ;
    margin-bottom: 3rem ;
  }
  .my-xxl-auto {
    margin-top: auto ;
    margin-bottom: auto ;
  }
  .mt-xxl-0 {
    margin-top: 0 ;
  }
  .mt-xxl-1 {
    margin-top: 0.25rem ;
  }
  .mt-xxl-2 {
    margin-top: 0.5rem ;
  }
  .mt-xxl-3 {
    margin-top: 1rem ;
  }
  .mt-xxl-4 {
    margin-top: 1.5rem ;
  }
  .mt-xxl-5 {
    margin-top: 3rem ;
  }
  .mt-xxl-auto {
    margin-top: auto ;
  }
  .me-xxl-0 {
    margin-right: 0 ;
  }
  .me-xxl-1 {
    margin-right: 0.25rem ;
  }
  .me-xxl-2 {
    margin-right: 0.5rem ;
  }
  .me-xxl-3 {
    margin-right: 1rem ;
  }
  .me-xxl-4 {
    margin-right: 1.5rem ;
  }
  .me-xxl-5 {
    margin-right: 3rem ;
  }
  .me-xxl-auto {
    margin-right: auto ;
  }
  .mb-xxl-0 {
    margin-bottom: 0 ;
  }
  .mb-xxl-1 {
    margin-bottom: 0.25rem ;
  }
  .mb-xxl-2 {
    margin-bottom: 0.5rem ;
  }
  .mb-xxl-3 {
    margin-bottom: 1rem ;
  }
  .mb-xxl-4 {
    margin-bottom: 1.5rem ;
  }
  .mb-xxl-5 {
    margin-bottom: 3rem ;
  }
  .mb-xxl-auto {
    margin-bottom: auto ;
  }
  .ms-xxl-0 {
    margin-left: 0 ;
  }
  .ms-xxl-1 {
    margin-left: 0.25rem ;
  }
  .ms-xxl-2 {
    margin-left: 0.5rem ;
  }
  .ms-xxl-3 {
    margin-left: 1rem ;
  }
  .ms-xxl-4 {
    margin-left: 1.5rem ;
  }
  .ms-xxl-5 {
    margin-left: 3rem ;
  }
  .ms-xxl-auto {
    margin-left: auto ;
  }
  .m-xxl-n1 {
    margin: -0.25rem ;
  }
  .m-xxl-n2 {
    margin: -0.5rem ;
  }
  .m-xxl-n3 {
    margin: -1rem ;
  }
  .m-xxl-n4 {
    margin: -1.5rem ;
  }
  .m-xxl-n5 {
    margin: -3rem ;
  }
  .mx-xxl-n1 {
    margin-right: -0.25rem ;
    margin-left: -0.25rem ;
  }
  .mx-xxl-n2 {
    margin-right: -0.5rem ;
    margin-left: -0.5rem ;
  }
  .mx-xxl-n3 {
    margin-right: -1rem ;
    margin-left: -1rem ;
  }
  .mx-xxl-n4 {
    margin-right: -1.5rem ;
    margin-left: -1.5rem ;
  }
  .mx-xxl-n5 {
    margin-right: -3rem ;
    margin-left: -3rem ;
  }
  .my-xxl-n1 {
    margin-top: -0.25rem ;
    margin-bottom: -0.25rem ;
  }
  .my-xxl-n2 {
    margin-top: -0.5rem ;
    margin-bottom: -0.5rem ;
  }
  .my-xxl-n3 {
    margin-top: -1rem ;
    margin-bottom: -1rem ;
  }
  .my-xxl-n4 {
    margin-top: -1.5rem ;
    margin-bottom: -1.5rem ;
  }
  .my-xxl-n5 {
    margin-top: -3rem ;
    margin-bottom: -3rem ;
  }
  .mt-xxl-n1 {
    margin-top: -0.25rem ;
  }
  .mt-xxl-n2 {
    margin-top: -0.5rem ;
  }
  .mt-xxl-n3 {
    margin-top: -1rem ;
  }
  .mt-xxl-n4 {
    margin-top: -1.5rem ;
  }
  .mt-xxl-n5 {
    margin-top: -3rem ;
  }
  .me-xxl-n1 {
    margin-right: -0.25rem ;
  }
  .me-xxl-n2 {
    margin-right: -0.5rem ;
  }
  .me-xxl-n3 {
    margin-right: -1rem ;
  }
  .me-xxl-n4 {
    margin-right: -1.5rem ;
  }
  .me-xxl-n5 {
    margin-right: -3rem ;
  }
  .mb-xxl-n1 {
    margin-bottom: -0.25rem ;
  }
  .mb-xxl-n2 {
    margin-bottom: -0.5rem ;
  }
  .mb-xxl-n3 {
    margin-bottom: -1rem ;
  }
  .mb-xxl-n4 {
    margin-bottom: -1.5rem ;
  }
  .mb-xxl-n5 {
    margin-bottom: -3rem ;
  }
  .ms-xxl-n1 {
    margin-left: -0.25rem ;
  }
  .ms-xxl-n2 {
    margin-left: -0.5rem ;
  }
  .ms-xxl-n3 {
    margin-left: -1rem ;
  }
  .ms-xxl-n4 {
    margin-left: -1.5rem ;
  }
  .ms-xxl-n5 {
    margin-left: -3rem ;
  }
  .p-xxl-0 {
    padding: 0 ;
  }
  .p-xxl-1 {
    padding: 0.25rem ;
  }
  .p-xxl-2 {
    padding: 0.5rem ;
  }
  .p-xxl-3 {
    padding: 1rem ;
  }
  .p-xxl-4 {
    padding: 1.5rem ;
  }
  .p-xxl-5 {
    padding: 3rem ;
  }
  .px-xxl-0 {
    padding-right: 0 ;
    padding-left: 0 ;
  }
  .px-xxl-1 {
    padding-right: 0.25rem ;
    padding-left: 0.25rem ;
  }
  .px-xxl-2 {
    padding-right: 0.5rem ;
    padding-left: 0.5rem ;
  }
  .px-xxl-3 {
    padding-right: 1rem ;
    padding-left: 1rem ;
  }
  .px-xxl-4 {
    padding-right: 1.5rem ;
    padding-left: 1.5rem ;
  }
  .px-xxl-5 {
    padding-right: 3rem ;
    padding-left: 3rem ;
  }
  .py-xxl-0 {
    padding-top: 0 ;
    padding-bottom: 0 ;
  }
  .py-xxl-1 {
    padding-top: 0.25rem ;
    padding-bottom: 0.25rem ;
  }
  .py-xxl-2 {
    padding-top: 0.5rem ;
    padding-bottom: 0.5rem ;
  }
  .py-xxl-3 {
    padding-top: 1rem ;
    padding-bottom: 1rem ;
  }
  .py-xxl-4 {
    padding-top: 1.5rem ;
    padding-bottom: 1.5rem ;
  }
  .py-xxl-5 {
    padding-top: 3rem ;
    padding-bottom: 3rem ;
  }
  .pt-xxl-0 {
    padding-top: 0 ;
  }
  .pt-xxl-1 {
    padding-top: 0.25rem ;
  }
  .pt-xxl-2 {
    padding-top: 0.5rem ;
  }
  .pt-xxl-3 {
    padding-top: 1rem ;
  }
  .pt-xxl-4 {
    padding-top: 1.5rem ;
  }
  .pt-xxl-5 {
    padding-top: 3rem ;
  }
  .pe-xxl-0 {
    padding-right: 0 ;
  }
  .pe-xxl-1 {
    padding-right: 0.25rem ;
  }
  .pe-xxl-2 {
    padding-right: 0.5rem ;
  }
  .pe-xxl-3 {
    padding-right: 1rem ;
  }
  .pe-xxl-4 {
    padding-right: 1.5rem ;
  }
  .pe-xxl-5 {
    padding-right: 3rem ;
  }
  .pb-xxl-0 {
    padding-bottom: 0 ;
  }
  .pb-xxl-1 {
    padding-bottom: 0.25rem ;
  }
  .pb-xxl-2 {
    padding-bottom: 0.5rem ;
  }
  .pb-xxl-3 {
    padding-bottom: 1rem ;
  }
  .pb-xxl-4 {
    padding-bottom: 1.5rem ;
  }
  .pb-xxl-5 {
    padding-bottom: 3rem ;
  }
  .ps-xxl-0 {
    padding-left: 0 ;
  }
  .ps-xxl-1 {
    padding-left: 0.25rem ;
  }
  .ps-xxl-2 {
    padding-left: 0.5rem ;
  }
  .ps-xxl-3 {
    padding-left: 1rem ;
  }
  .ps-xxl-4 {
    padding-left: 1.5rem ;
  }
  .ps-xxl-5 {
    padding-left: 3rem ;
  }
  .text-xxl-start {
    text-align: left ;
  }
  .text-xxl-end {
    text-align: right ;
  }
  .text-xxl-center {
    text-align: center ;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.1875rem ;
  }
  .fs-2 {
    font-size: 1.75rem ;
  }
  .fs-3 {
    font-size: 1.53125rem ;
  }
  .fs-4 {
    font-size: 1.3125rem ;
  }
}
@media print {
  .d-print-inline {
    display: inline ;
  }
  .d-print-inline-block {
    display: inline-block ;
  }
  .d-print-block {
    display: block ;
  }
  .d-print-grid {
    display: grid ;
  }
  .d-print-table {
    display: table ;
  }
  .d-print-table-row {
    display: table-row ;
  }
  .d-print-table-cell {
    display: table-cell ;
  }
  .d-print-flex {
    display: -webkit-box ;
    display: -ms-flexbox ;
    display: flex ;
  }
  .d-print-inline-flex {
    display: -webkit-inline-box ;
    display: -ms-inline-flexbox ;
    display: inline-flex ;
  }
  .d-print-none {
    display: none ;
  }
}
html {
  position: relative;
  min-height: 100%;
}
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #495057;
  font-weight: 500;
  line-height: 1.2;
}
a {
  text-decoration: none ;
}
label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}
.blockquote {
  padding: 10px 20px;
  border-left: 4px solid #eaedf1;
}
.blockquote-reverse {
  border-left: 0;
  border-right: 4px solid #eaedf1;
  text-align: right;
}
@media (min-width: 1200px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1140px;
  }
}
.row > * {
  position: relative;
}
.bg-soft-primary {
  background-color: rgba(82, 92, 229, 0.25) ;
}
.bg-soft-secondary {
  background-color: rgba(116, 120, 141, 0.25) ;
}
.bg-soft-success {
  background-color: rgba(35, 197, 143, 0.25) ;
}
.bg-soft-info {
  background-color: rgba(91, 164, 229, 0.25) ;
}
.bg-soft-warning {
  background-color: rgba(238, 177, 72, 0.25) ;
}
.bg-soft-danger {
  background-color: rgba(241, 78, 78, 0.25) ;
}
.bg-soft-pink {
  background-color: rgba(232, 62, 140, 0.25) ;
}
.bg-soft-light {
  background-color: rgba(249, 250, 252, 0.25) ;
}
.bg-soft-dark {
  background-color: rgba(52, 58, 64, 0.25) ;
}
.badge[href]:focus,
.badge[href]:hover {
  color: #fff;
}
.badge.bg-primary[href]:focus,
.badge.bg-primary[href]:hover {
  background-color: #404be2 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-primary {
  color: #525ce5;
  background-color: rgba(82, 92, 229, 0.18);
}
.badge-soft-primary[href]:focus,
.badge-soft-primary[href]:hover {
  color: #525ce5;
  text-decoration: none;
  background-color: rgba(82, 92, 229, 0.4);
}
.badge.bg-secondary[href]:focus,
.badge.bg-secondary[href]:hover {
  background-color: #6b6e82 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-secondary {
  color: #74788d;
  background-color: rgba(116, 120, 141, 0.18);
}
.badge-soft-secondary[href]:focus,
.badge-soft-secondary[href]:hover {
  color: #74788d;
  text-decoration: none;
  background-color: rgba(116, 120, 141, 0.4);
}
.badge.bg-success[href]:focus,
.badge.bg-success[href]:hover {
  background-color: #20b482 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-success {
  color: #23c58f;
  background-color: rgba(35, 197, 143, 0.18);
}
.badge-soft-success[href]:focus,
.badge-soft-success[href]:hover {
  color: #23c58f;
  text-decoration: none;
  background-color: rgba(35, 197, 143, 0.4);
}
.badge.bg-info[href]:focus,
.badge.bg-info[href]:hover {
  background-color: #499ae2 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-info {
  color: #5ba4e5;
  background-color: rgba(91, 164, 229, 0.18);
}
.badge-soft-info[href]:focus,
.badge-soft-info[href]:hover {
  color: #5ba4e5;
  text-decoration: none;
  background-color: rgba(91, 164, 229, 0.4);
}
.badge.bg-warning[href]:focus,
.badge.bg-warning[href]:hover {
  background-color: #eca935 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-warning {
  color: #eeb148;
  background-color: rgba(238, 177, 72, 0.18);
}
.badge-soft-warning[href]:focus,
.badge-soft-warning[href]:hover {
  color: #eeb148;
  text-decoration: none;
  background-color: rgba(238, 177, 72, 0.4);
}
.badge.bg-danger[href]:focus,
.badge.bg-danger[href]:hover {
  background-color: #f03b3b ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-danger {
  color: #f14e4e;
  background-color: rgba(241, 78, 78, 0.18);
}
.badge-soft-danger[href]:focus,
.badge-soft-danger[href]:hover {
  color: #f14e4e;
  text-decoration: none;
  background-color: rgba(241, 78, 78, 0.4);
}
.badge.bg-pink[href]:focus,
.badge.bg-pink[href]:hover {
  background-color: #e62c81 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-pink {
  color: #e83e8c;
  background-color: rgba(232, 62, 140, 0.18);
}
.badge-soft-pink[href]:focus,
.badge-soft-pink[href]:hover {
  color: #e83e8c;
  text-decoration: none;
  background-color: rgba(232, 62, 140, 0.4);
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  background-color: #ebeff5 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-light {
  color: #f9fafc;
  background-color: rgba(249, 250, 252, 0.18);
}
.badge-soft-light[href]:focus,
.badge-soft-light[href]:hover {
  color: #f9fafc;
  text-decoration: none;
  background-color: rgba(249, 250, 252, 0.4);
}
.badge.bg-dark[href]:focus,
.badge.bg-dark[href]:hover {
  background-color: #2b3035 ;
}
.badge.bg-light {
  color: #495057;
}
.badge.bg-light[href]:focus,
.badge.bg-light[href]:hover {
  color: #495057;
}
.badge-soft-dark {
  color: #343a40;
  background-color: rgba(52, 58, 64, 0.18);
}
.badge-soft-dark[href]:focus,
.badge-soft-dark[href]:hover {
  color: #343a40;
  text-decoration: none;
  background-color: rgba(52, 58, 64, 0.4);
}
.rounded-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
}
.badge.bg-dark {
  color: #f9fafc;
}
a,
button {
  outline: 0 ;
}
.btn-light {
  border: 1px solid #f2f4f9;
}
.btn-rounded {
  border-radius: 30px;
}
.btn-dark,
.btn-secondary {
  color: #edf1f5 ;
}
.btn-outline-light {
  color: #212529;
}
.breadcrumb-item > a {
  color: #fff;
}
.breadcrumb-item + .breadcrumb-item::before {
  color: #fff;
  content: "\f105" ;
  font-family: "Font Awesome 5 Free";
  font-weight: 700;
}
[dir="rtl"] .breadcrumb-item + .breadcrumb-item {
  padding-right: 0.5rem;
}
[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
  content: "\f104";
}
.card {
  margin-bottom: 24px;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
}
.card-drop {
  font-size: 20px;
  line-height: 0;
  color: inherit;
}
.header-title {
  font-size: 16px;
  margin: 0 0 7px 0;
  font-weight: 500;
}
.card-title-desc {
  color: #74788d;
  margin-bottom: 24px;
}
.dropdown-menu {
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  -webkit-animation-name: DropDownSlide;
  animation-name: DropDownSlide;
  -webkit-animation-duration: 0.3s;
  animation-duration: 0.3s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  margin: 0;
  position: absolute;
  z-index: 1000;
}
.dropdown-menu.show {
  top: 100% ;
}
.dropdown-menu[style] {
  right: auto ;
  left: 0;
}
.dropdown-menu-end[style] {
  right: 0 ;
  left: auto ;
}
.dropdown-menu[data-popper-placement^="left"],
.dropdown-menu[data-popper-placement^="right"],
.dropdown-menu[data-popper-placement^="top"] {
  top: auto ;
  -webkit-animation: none ;
  animation: none ;
}
@-webkit-keyframes DropDownSlide {
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  0% {
    -webkit-transform: translateY(10px);
    transform: translateY(10px);
  }
}
@keyframes DropDownSlide {
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  0% {
    -webkit-transform: translateY(10px);
    transform: translateY(10px);
  }
}
@media (min-width: 600px) {
  .dropdown-menu-lg {
    width: 320px;
  }
  .dropdown-menu-md {
    width: 240px;
  }
}
.dropdown-divider {
  border-top-color: #edf1f5;
}
.dropdown-mega {
  position: static ;
}
.dropdown-megamenu[style] {
  padding: 20px;
  left: 20px ;
  right: 20px ;
}
.dropdown-mega-menu-xl {
  width: 40rem;
}
.dropdown-mega-menu-lg {
  width: 26rem;
}
.nav-pills > li > a,
.nav-tabs > li > a {
  color: #495057;
  font-weight: 500;
}
.nav-pills > a {
  color: #495057;
  font-weight: 500;
}
.nav-tabs-custom {
  border-bottom: 2px solid #eaedf1;
}
.nav-tabs-custom .nav-item {
  position: relative;
  color: #343a40;
}
.nav-tabs-custom .nav-item .nav-link {
  border: none;
}
.nav-tabs-custom .nav-item .nav-link::after {
  content: "";
  background: #525ce5;
  height: 2px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: -1px;
  -webkit-transition: all 250ms ease 0s;
  transition: all 250ms ease 0s;
  -webkit-transform: scale(0);
  transform: scale(0);
}
.nav-tabs-custom .nav-item .nav-link.active {
  color: #525ce5;
}
.nav-tabs-custom .nav-item .nav-link.active:after {
  -webkit-transform: scale(1);
  transform: scale(1);
}
.vertical-nav .nav .nav-link {
  padding: 24px 16px;
  text-align: center;
  margin-bottom: 8px;
}
.vertical-nav .nav .nav-link .nav-icon {
  font-size: 24px;
}
.table th {
  font-weight: 600;
}
.table .table-light {
  color: #495057;
  border-color: #edf1f5;
  background-color: #f8f9fa;
}
.table > :not(caption) > * > * {
  border-bottom-width: 0;
  border-top-width: 1px;
}
.table > :not(:last-child) > :last-child > * {
  border-bottom-color: #edf1f5;
}
.table-dark > :not(:last-child) > :last-child > * {
  border-bottom-color: #43494e;
}
.table-nowrap td,
.table-nowrap th {
  white-space: nowrap;
}
.table-centered td,
.table-centered th {
  vertical-align: middle ;
}
.table-card-list {
  border-collapse: separate;
  border-spacing: 0 12px;
}
.table-card-list tr {
  background-color: #fff;
}
.pagination-rounded .page-link {
  border-radius: 30px ;
  margin: 0 3px ;
  border: none;
  width: 32px;
  height: 32px;
  padding: 0;
  text-align: center;
  line-height: 32px;
}
.progress-sm {
  height: 5px;
}
.progress-md {
  height: 8px;
}
.progress-lg {
  height: 12px;
}
.progress-xl {
  height: 16px;
}
.animated-progess {
  position: relative;
}
.animated-progess .progress-bar {
  position: relative;
  border-radius: 6px;
  -webkit-animation: animate-positive 2s;
  animation: animate-positive 2s;
}
@-webkit-keyframes animate-positive {
  0% {
    width: 0;
  }
}
@keyframes animate-positive {
  0% {
    width: 0;
  }
}
/*# sourceMappingURL=bootstrap.min.css.map */
