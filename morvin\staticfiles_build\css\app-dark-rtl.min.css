/*
Template Name: <PERSON><PERSON> -  <PERSON><PERSON> & Dashboard Template
Author: Themesdesign
Version: 1.0.0
Contact: <EMAIL>
File: Main Css File
*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap");
#page-topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background-color: #1f293f; }

.navbar-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  -webkit-box-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto;
  height: 70px;
  padding: 0 0 0 calc(24px / 2);
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); }
  .navbar-header .dropdown.show .header-item {
    background-color: #303d59; }

.navbar-brand-box {
  padding: 0 1.5rem;
  text-align: center;
  width: 260px; }

.logo {
  line-height: 70px; }
  .logo .logo-sm {
    display: none; }

.logo-dark {
  display: none; }

.logo-light {
  display: block; }

.fullscreen-enable [data-toggle="fullscreen"] .mdi-fullscreen::before {
  content: '\F0294'; }

.page-content-wrapper {
  margin-top: -90px; }

.page-title-box {
  background: url(../images/title-img.png);
  background-position: center;
  background-color: #525ce5;
  margin: 0 -24px 23px -24px;
  padding: 24px 24px 92px 24px;
  color: #fff;
  background-size: cover; }

/* Search */
.search-wrap {
  background-color: #26324d;
  color: #eff2f7;
  z-index: 9997;
  position: absolute;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  left: 0;
  height: 70px;
  padding: 0 15px;
  -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0);
  -webkit-transition: .3s;
  transition: .3s; }
  .search-wrap form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%; }
  .search-wrap .search-bar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%; }
  .search-wrap .search-input {
    -webkit-box-flex: 1;
        -ms-flex: 1 1;
            flex: 1 1;
    border: none;
    outline: none;
    -webkit-box-shadow: none;
            box-shadow: none;
    background-color: transparent; }
  .search-wrap .close-search {
    width: 36px;
    height: 64px;
    line-height: 64px;
    text-align: center;
    color: inherit;
    font-size: 24px; }
    .search-wrap .close-search:hover {
      color: #f14e4e; }

.search-wrap.open {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0); }

.megamenu-list li {
  position: relative;
  padding: 5px 0px; }
  .megamenu-list li a {
    color: #a7aebd; }

@media (max-width: 992px) {
  #page-topbar {
    right: 0; }
  .navbar-brand-box {
    width: auto; }
  .logo span.logo-lg {
    display: none; }
  .logo span.logo-sm {
    display: inline-block; } }

.page-content {
  padding: calc(70px) calc(24px / 2) 60px calc(24px / 2); }

.header-item {
  height: 70px;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  color: #919bae;
  border: 0;
  border-radius: 0px; }
  .header-item:hover {
    color: #919bae; }

.header-profile-user {
  height: 36px;
  width: 36px;
  background-color: #2f3a50;
  padding: 3px; }

.noti-icon i {
  font-size: 24px;
  color: #919bae; }

.noti-icon .badge {
  position: absolute;
  top: 20px;
  left: 6px; }

.notification-item .media {
  padding: 0.75rem 1rem; }
  .notification-item .media:hover {
    background-color: #2f3a50; }

.dropdown-icon-item {
  display: block;
  border-radius: 3px;
  line-height: 34px;
  text-align: center;
  padding: 15px 0 9px;
  display: block;
  border: 1px solid transparent;
  color: #c3cbe4; }
  .dropdown-icon-item img {
    height: 24px; }
  .dropdown-icon-item span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
  .dropdown-icon-item:hover {
    border-color: #1f293f; }

.fullscreen-enable [data-toggle="fullscreen"] .bx-fullscreen::before {
  content: "\ea3f"; }

body[data-topbar="dark"] #page-topbar {
  background-color: #141b2d; }

body[data-topbar="dark"] .navbar-header .dropdown.show .header-item {
  background-color: rgba(255, 255, 255, 0.05); }

body[data-topbar="dark"] .navbar-header .waves-effect .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

body[data-topbar="dark"] .header-item {
  color: #e9ecef; }
  body[data-topbar="dark"] .header-item:hover {
    color: #e9ecef; }

body[data-topbar="dark"] .header-profile-user {
  background-color: rgba(255, 255, 255, 0.25); }

body[data-topbar="dark"] .noti-icon i {
  color: #e9ecef; }

body[data-topbar="dark"] .title-tooltip li i {
  color: #e9ecef; }

body[data-topbar="dark"] .app-search .form-control {
  background-color: rgba(43, 50, 68, 0.07);
  color: #fff; }

body[data-topbar="dark"] .app-search span,
body[data-topbar="dark"] .app-search input.form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

body[data-sidebar="dark"] .navbar-brand-box {
  background: #1f293f; }

body[data-sidebar="dark"] .logo-dark {
  display: none; }

body[data-sidebar="dark"] .logo-light {
  display: block; }

@media (max-width: 600px) {
  .navbar-header .dropdown {
    position: static; }
    .navbar-header .dropdown .dropdown-menu {
      right: 10px !important;
      left: 10px !important; } }

@media (max-width: 380px) {
  .navbar-brand-box {
    display: none; } }

body[data-layout="horizontal"] .navbar-brand-box {
  width: auto; }

body[data-layout="horizontal"] .page-content {
  margin-top: 70px;
  padding: calc(36px + 24px) calc(24px / 2) 60px calc(24px / 2); }

@media (max-width: 992px) {
  body[data-layout="horizontal"] .page-content {
    margin-top: 10px; } }

.page-title-box .breadcrumb {
  background-color: transparent;
  padding: 0; }

.page-title-box h4 {
  color: #fff;
  text-transform: uppercase;
  font-weight: 500;
  font-size: 16px !important; }

.topbar-social-icon {
  padding: calc(38px / 2) 0; }

.title-tooltip li i {
  font-size: 20px;
  margin-right: 10px;
  color: #919bae; }

.footer {
  bottom: 0;
  padding: 20px calc(24px / 2);
  position: absolute;
  left: 0;
  border-top: 1px solid #1f293f;
  color: #d6dae5;
  right: 260px;
  height: 60px;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  background-color: #1f293f; }

@media (max-width: 992px) {
  .footer {
    right: 0; } }

.vertical-collpsed .footer {
  right: 70px; }

body[data-layout="horizontal"] .footer {
  right: 0 !important; }

.right-bar {
  background-color: #1f293f;
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  display: block;
  position: fixed;
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  width: 280px;
  z-index: 9999;
  float: left !important;
  left: -290px;
  top: 0;
  bottom: 0; }
  .right-bar .right-bar-toggle {
    background-color: white;
    height: 24px;
    width: 24px;
    line-height: 24px;
    color: #1f293f;
    text-align: center;
    border-radius: 50%; }
    .right-bar .right-bar-toggle:hover {
      background-color: white; }

.rightbar-overlay {
  background-color: rgba(239, 242, 247, 0.55);
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
  display: none;
  z-index: 9998;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out; }

.right-bar-enabled .right-bar {
  left: 0; }

.right-bar-enabled .rightbar-overlay {
  display: block; }

@media (max-width: 767.98px) {
  .right-bar {
    overflow: auto; }
    .right-bar .slimscroll-menu {
      height: auto !important; } }

.metismenu {
  margin: 0; }
  .metismenu li {
    display: block;
    width: 100%; }
  .metismenu .mm-collapse {
    display: none; }
    .metismenu .mm-collapse:not(.mm-show) {
      display: none; }
    .metismenu .mm-collapse.mm-show {
      display: block; }
  .metismenu .mm-collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-timing-function: ease;
            transition-timing-function: ease;
    -webkit-transition-duration: .35s;
            transition-duration: .35s;
    -webkit-transition-property: height, visibility;
    transition-property: height, visibility; }

.vertical-menu {
  width: 260px;
  z-index: 1001;
  background: #1f293f;
  bottom: 0;
  margin-top: 0;
  position: fixed;
  top: 70px;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); }

.user-sidebar {
  position: relative;
  text-align: center;
  background: url(../images/user-img.png);
  background-color: #525ce5;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  padding: 20px 0; }
  .user-sidebar .user-img {
    position: relative; }
    .user-sidebar .user-img img {
      width: 60px;
      height: 60px;
      border: 3px solid #23c58f;
      padding: 5px; }
    .user-sidebar .user-img .avatar-online {
      position: absolute;
      bottom: 4px;
      width: 10px;
      height: 10px;
      z-index: 1;
      border: 2px solid transparent;
      border-radius: 50%;
      margin-right: -15px; }

.main-content {
  margin-right: 260px;
  overflow: hidden; }
  .main-content .content {
    padding: 0 15px 10px 15px;
    margin-top: 70px; }

#sidebar-menu {
  padding: 0px 0 30px 0; }
  #sidebar-menu .mm-active > .has-arrow:after {
    -webkit-transform: rotate(-90deg);
            transform: rotate(-90deg); }
  #sidebar-menu .has-arrow:after {
    content: "\F0142";
    font-family: 'Material Design Icons';
    display: block;
    float: left;
    -webkit-transition: -webkit-transform .2s;
    transition: -webkit-transform .2s;
    transition: transform .2s;
    transition: transform .2s, -webkit-transform .2s;
    font-size: 1rem; }
  #sidebar-menu ul li a {
    display: block;
    padding: .625rem 1.2rem;
    color: #a3acc1;
    position: relative;
    font-size: 14.5px;
    -webkit-transition: all .4s;
    transition: all .4s;
    margin: 0px 17px;
    border-radius: 3px; }
    #sidebar-menu ul li a i {
      display: inline-block;
      min-width: 1.75rem;
      padding-bottom: .125em;
      font-size: 16px;
      line-height: 1.40625rem;
      vertical-align: middle;
      color: #a3acc1;
      -webkit-transition: all .4s;
      transition: all .4s; }
    #sidebar-menu ul li a:hover {
      color: #ffffff; }
      #sidebar-menu ul li a:hover i {
        color: #ffffff; }
  #sidebar-menu ul li .badge {
    margin-top: 5px; }
  #sidebar-menu ul li ul.sub-menu {
    padding: 0; }
    #sidebar-menu ul li ul.sub-menu li a {
      padding: .4rem 2.8rem .4rem 1.5rem;
      font-size: 14px;
      color: #a3acc1;
      background-color: transparent !important; }
      #sidebar-menu ul li ul.sub-menu li a:before {
        content: "\F09DF";
        font-family: 'Material Design Icons';
        font-size: 20px;
        line-height: 10px;
        padding-left: 2px;
        vertical-align: middle;
        display: inline-block; }
    #sidebar-menu ul li ul.sub-menu li ul.sub-menu {
      padding: 0; }
      #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {
        padding: .4rem 4rem .4rem 1.5rem;
        font-size: 14px; }

.menu-title {
  padding: 12px 20px !important;
  letter-spacing: .05em;
  pointer-events: none;
  cursor: default;
  font-size: 11px;
  text-transform: uppercase;
  color: #a3acc1;
  font-weight: 600; }

.mm-active {
  color: #ffffff !important; }
  .mm-active > a {
    color: #ffffff !important;
    background-color: #2b364e !important; }
    .mm-active > a i {
      color: #ffffff !important; }
  .mm-active > i {
    color: #ffffff !important; }
  .mm-active .active {
    color: #ffffff !important;
    background-color: #2b364e !important; }
    .mm-active .active i {
      color: #ffffff !important; }

@media (max-width: 992px) {
  .vertical-menu {
    display: none; }
  .main-content {
    margin-right: 0 !important; }
  body.sidebar-enable .vertical-menu {
    display: block; } }

.vertical-collpsed .user-sidebar {
  display: none; }

.vertical-collpsed .main-content {
  margin-right: 70px; }

.vertical-collpsed .navbar-brand-box {
  width: 70px !important; }

.vertical-collpsed .logo span.logo-lg {
  display: none; }

.vertical-collpsed .logo span.logo-sm {
  display: block; }

.vertical-collpsed .vertical-menu {
  position: absolute;
  width: 70px !important;
  z-index: 5; }
  .vertical-collpsed .vertical-menu .simplebar-mask,
  .vertical-collpsed .vertical-menu .simplebar-content-wrapper {
    overflow: visible !important; }
  .vertical-collpsed .vertical-menu .simplebar-scrollbar {
    display: none !important; }
  .vertical-collpsed .vertical-menu .simplebar-offset {
    bottom: 0 !important; }
  .vertical-collpsed .vertical-menu #sidebar-menu .menu-title,
  .vertical-collpsed .vertical-menu #sidebar-menu .badge,
  .vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {
    display: none !important; }
  .vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {
    height: inherit !important; }
  .vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {
    display: none; }
  .vertical-collpsed .vertical-menu #sidebar-menu > ul > li {
    position: relative;
    white-space: nowrap; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {
      padding: 15px 20px;
      min-height: 55px;
      -webkit-transition: none;
      transition: none;
      margin: 0; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {
        color: #ffffff; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {
        font-size: 1.15rem;
        margin-right: 4px; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {
        display: none;
        padding-right: 25px; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {
      position: relative;
      width: calc(190px + 70px);
      background-color: #2b364e;
      -webkit-transition: none;
      transition: none; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {
        display: inline; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {
      display: block;
      right: 70px;
      position: absolute;
      width: 190px;
      height: auto !important;
      -webkit-box-shadow: -3px 5px 10px 0 rgba(54, 61, 71, 0.1);
              box-shadow: -3px 5px 10px 0 rgba(54, 61, 71, 0.1); }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {
        -webkit-box-shadow: -3px 5px 10px 0 rgba(54, 61, 71, 0.1);
                box-shadow: -3px 5px 10px 0 rgba(54, 61, 71, 0.1); }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {
        -webkit-box-shadow: none;
                box-shadow: none;
        padding: 8px 20px;
        position: relative;
        width: 190px;
        z-index: 6;
        color: #a3acc1;
        margin: 0; }
        .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {
          color: #ffffff; }
  .vertical-collpsed .vertical-menu #sidebar-menu > ul ul {
    padding: 5px 0;
    z-index: 9999;
    display: none;
    background-color: #1f293f; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {
      display: block;
      right: 190px;
      height: auto !important;
      margin-top: -36px;
      position: absolute;
      width: 190px; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {
      position: absolute;
      left: 20px;
      top: 12px;
      -webkit-transform: rotate(-270deg);
              transform: rotate(-270deg); }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {
      color: #303d59; }

body[data-sidebar="dark"] .user-sidebar {
  background: none; }

body[data-sidebar="dark"] .vertical-menu {
  background: #1f293f; }

body[data-sidebar="dark"] #sidebar-menu ul li a {
  color: #8590a5; }
  body[data-sidebar="dark"] #sidebar-menu ul li a i {
    color: #8590a5; }
  body[data-sidebar="dark"] #sidebar-menu ul li a:hover {
    color: #ffffff; }
    body[data-sidebar="dark"] #sidebar-menu ul li a:hover i {
      color: #ffffff; }

body[data-sidebar="dark"] #sidebar-menu ul li ul.sub-menu li a {
  color: #8590a5; }
  body[data-sidebar="dark"] #sidebar-menu ul li ul.sub-menu li a:hover {
    color: #ffffff; }

body[data-sidebar="dark"].vertical-collpsed {
  min-height: 1400px; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {
    background: #222d46;
    color: #ffffff; }
    body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {
      color: #ffffff; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {
    color: #8590a5; }
    body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {
      color: #ffffff; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {
    background-color: #212b42; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {
    color: #ffffff !important; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {
    color: #ffffff !important; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {
    color: #ffffff !important; }

body[data-sidebar="dark"] .mm-active {
  color: #ffffff !important; }
  body[data-sidebar="dark"] .mm-active > a {
    color: #ffffff !important;
    background-color: #2b364e !important; }
    body[data-sidebar="dark"] .mm-active > a i {
      color: #ffffff !important; }
  body[data-sidebar="dark"] .mm-active > i {
    color: #ffffff !important; }
  body[data-sidebar="dark"] .mm-active .active {
    color: #ffffff !important;
    background-color: #2b364e !important; }
    body[data-sidebar="dark"] .mm-active .active i {
      color: #ffffff !important; }

body[data-sidebar="dark"] .menu-title {
  color: #8590a5; }

body[data-layout="horizontal"] .main-content {
  margin-right: 0 !important; }

body[data-sidebar-size="small"] .navbar-brand-box {
  width: 160px; }

body[data-sidebar-size="small"] .vertical-menu {
  width: 160px;
  text-align: center; }
  body[data-sidebar-size="small"] .vertical-menu .has-arrow:after,
  body[data-sidebar-size="small"] .vertical-menu .badge {
    display: none !important; }

body[data-sidebar-size="small"] .main-content {
  margin-right: 160px; }

body[data-sidebar-size="small"] .footer {
  right: 160px; }

body[data-sidebar-size="small"] #sidebar-menu ul li a i {
  display: block; }

body[data-sidebar-size="small"] #sidebar-menu ul li ul.sub-menu li a {
  padding-right: 1.5rem; }
  body[data-sidebar-size="small"] #sidebar-menu ul li ul.sub-menu li a:before {
    display: none; }

body[data-sidebar-size="small"] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {
  padding-right: 1.5rem; }

body[data-sidebar-size="small"].vertical-collpsed .main-content {
  margin-right: 70px; }

body[data-sidebar-size="small"].vertical-collpsed .vertical-menu #sidebar-menu {
  text-align: right; }
  body[data-sidebar-size="small"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {
    display: inline-block; }

body[data-sidebar-size="small"].vertical-collpsed .footer {
  right: 70px; }

[dir="rtl"]
#sidebar-menu .has-arrow:after {
  content: "\F0141";
  -webkit-transition: -webkit-transform .2s;
  transition: -webkit-transform .2s;
  transition: transform .2s;
  transition: transform .2s, -webkit-transform .2s; }

[dir="rtl"]
#sidebar-menu .mm-active > .has-arrow:after {
  -webkit-transform: rotate(-90deg);
          transform: rotate(-90deg); }

.topnav {
  background: #1f293f;
  padding: 0 calc(24px / 2);
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  margin-top: 70px;
  position: fixed;
  right: 0;
  left: 0;
  z-index: 100; }
  .topnav .topnav-menu {
    margin: 0;
    padding: 0; }
  .topnav .navbar-nav .nav-link {
    font-size: 15px;
    position: relative;
    padding: 1.2rem 1.5rem;
    color: #a3acc1; }
    .topnav .navbar-nav .nav-link i {
      font-size: 15px;
      top: 2px;
      position: relative; }
    .topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {
      color: #ffffff;
      background-color: transparent; }
  .topnav .navbar-nav .dropdown-item {
    color: #a3acc1; }
    .topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {
      color: #ffffff;
      background: transparent; }
  .topnav .navbar-nav .nav-item .nav-link.active {
    color: #ffffff; }
  .topnav .navbar-nav .dropdown.active > a {
    color: #ffffff;
    background-color: transparent; }

@media (min-width: 1200px) {
  body[data-layout="horizontal"] .container-fluid,
  body[data-layout="horizontal"] .navbar-header {
    max-width: 85%; } }

@media (min-width: 992px) {
  .topnav .navbar-nav .nav-item:first-of-type .nav-link {
    padding-right: 0; }
  .topnav .dropdown-item {
    padding: .5rem 1.5rem;
    min-width: 180px; }
  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {
    right: 0px;
    left: auto; }
  .topnav .dropdown .dropdown-menu {
    margin-top: 0;
    border-radius: 0 0 0.25rem 0.25rem; }
    .topnav .dropdown .dropdown-menu .arrow-down::after {
      left: 15px;
      -webkit-transform: rotate(135deg) translateY(-50%);
              transform: rotate(135deg) translateY(-50%);
      position: absolute; }
    .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {
      position: absolute;
      top: 0 !important;
      right: 100%;
      display: none; }
  .topnav .dropdown:hover > .dropdown-menu {
    display: block; }
  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {
    display: block; }
  .navbar-toggle {
    display: none; } }

.arrow-down {
  display: inline-block; }
  .arrow-down:after {
    border-color: initial;
    border-style: solid;
    border-width: 0 1px 1px 0;
    content: "";
    height: .4em;
    display: inline-block;
    left: 5px;
    top: 50%;
    margin-right: 10px;
    -webkit-transform: rotate(45deg) translateY(-50%);
            transform: rotate(45deg) translateY(-50%);
    -webkit-transform-origin: top;
            transform-origin: top;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
    width: .4em; }

@media (max-width: 1199.98px) {
  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {
    left: 100%;
    right: auto; } }

@media (max-width: 991.98px) {
  .navbar-brand-box .logo-dark {
    display: none; }
    .navbar-brand-box .logo-dark span.logo-sm {
      display: none; }
  .navbar-brand-box .logo-light {
    display: block; }
  .topnav {
    max-height: 360px;
    overflow-y: auto;
    padding: 0; }
    .topnav .navbar-nav .nav-link {
      padding: 0.75rem 1.1rem; }
    .topnav .dropdown .dropdown-menu {
      background-color: transparent;
      border: none;
      -webkit-box-shadow: none;
              box-shadow: none;
      padding-right: 20px; }
      .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {
        width: auto; }
        .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {
          margin: 0px; }
    .topnav .dropdown .dropdown-item {
      position: relative;
      background-color: transparent; }
      .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {
        color: #ffffff; }
    .topnav .arrow-down::after {
      left: 15px;
      position: absolute; } }

@media (min-width: 992px) {
  body[data-layout="horizontal"][data-topbar="light"] .navbar-brand-box .logo-dark {
    display: none; }
  body[data-layout="horizontal"][data-topbar="light"] .navbar-brand-box .logo-light {
    display: block; }
  body[data-layout="horizontal"][data-topbar="light"] .topnav {
    background-color: #141b2d; }
    body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav .nav-link {
      color: rgba(255, 255, 255, 0.5); }
      body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav .nav-link:focus, body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav .nav-link:hover {
        color: rgba(255, 255, 255, 0.9); }
    body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav > .dropdown.active > a {
      color: rgba(255, 255, 255, 0.9) !important; } }

body[data-layout="horizontal"] .logo-dark {
  display: none; }

body[data-layout="horizontal"] .logo-light {
  display: block; }

body[data-topbar="colored"] #page-topbar {
  background-color: #757dea; }

body[data-topbar="colored"] .navbar-header .dropdown .show.header-item {
  background-color: rgba(255, 255, 255, 0.05); }

body[data-topbar="colored"] .navbar-header .waves-effect .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

body[data-topbar="colored"] .title-tooltip li i {
  color: rgba(255, 255, 255, 0.8); }

body[data-topbar="colored"] .header-item {
  color: rgba(255, 255, 255, 0.5); }
  body[data-topbar="colored"] .header-item:hover {
    color: #fff; }

body[data-topbar="colored"] .header-profile-user {
  background-color: rgba(255, 255, 255, 0.25); }

body[data-topbar="colored"] .noti-icon i {
  color: rgba(255, 255, 255, 0.5); }

body[data-topbar="colored"] .logo-dark {
  display: none; }

body[data-topbar="colored"] .logo-light {
  display: block; }

body[data-topbar="colored"] .app-search .form-control {
  background-color: rgba(43, 50, 68, 0.07);
  color: #fff; }

body[data-topbar="colored"] .app-search span,
body[data-topbar="colored"] .app-search input.form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

body[data-layout-size="boxed"] {
  background-color: #272f40; }
  body[data-layout-size="boxed"] #layout-wrapper {
    background-color: #141b2d;
    max-width: 1400px;
    margin: 0 auto;
    -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); }
  body[data-layout-size="boxed"] #page-topbar {
    max-width: 1400px;
    margin: 0 auto; }
  body[data-layout-size="boxed"] .footer {
    margin: 0 auto;
    max-width: calc(1400px - 260px); }
  body[data-layout-size="boxed"].vertical-collpsed .footer {
    max-width: calc(1400px - 70px); }

body[data-layout="horizontal"][data-layout-size="boxed"] #page-topbar, body[data-layout="horizontal"][data-layout-size="boxed"] #layout-wrapper, body[data-layout="horizontal"][data-layout-size="boxed"] .footer {
  max-width: 100%; }

body[data-layout="horizontal"][data-layout-size="boxed"] .container-fluid, body[data-layout="horizontal"][data-layout-size="boxed"] .navbar-header {
  max-width: 1400px; }

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent; }

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-right: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  -webkit-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0) translate(0, 0);
  transform: scale(0) translate(0, 0);
  pointer-events: none; }

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2); }

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important; }

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1; }

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em; }

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em; }

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom; }

.waves-input-wrapper.waves-button {
  padding: 0; }

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  right: 0;
  z-index: 1; }

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%; }

.waves-float {
  -webkit-mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  transition: all 300ms; }

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }

.waves-block {
  display: block; }

.waves-effect.waves-light .waves-ripple {
  background-color: rgba(255, 255, 255, 0.4); }

.waves-effect.waves-primary .waves-ripple {
  background-color: rgba(82, 92, 229, 0.4); }

.waves-effect.waves-success .waves-ripple {
  background-color: rgba(35, 197, 143, 0.4); }

.waves-effect.waves-info .waves-ripple {
  background-color: rgba(91, 164, 229, 0.4); }

.waves-effect.waves-warning .waves-ripple {
  background-color: rgba(238, 177, 72, 0.4); }

.waves-effect.waves-danger .waves-ripple {
  background-color: rgba(241, 78, 78, 0.4); }

.avatar-xs {
  height: 2rem;
  width: 2rem; }

.avatar-sm {
  height: 2.5rem;
  width: 2.5rem; }

.avatar-md {
  height: 4.5rem;
  width: 4.5rem; }

.avatar-lg {
  height: 6rem;
  width: 6rem; }

.avatar-xl {
  height: 7.5rem;
  width: 7.5rem; }

.mini-stat-icon {
  width: 46px;
  height: 46px; }

.avatar-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%; }

.custom-accordion .card + .card {
  margin-top: 0.5rem; }

.custom-accordion a.collapsed i.accor-plus-icon:before {
  content: "\F0415"; }

.custom-accordion .card-header {
  border-radius: 7px; }

.custom-accordion-arrow .card {
  border: 1px solid #2f3a50;
  -webkit-box-shadow: none;
          box-shadow: none; }

.custom-accordion-arrow .card-header {
  padding-right: 45px;
  position: relative; }
  .custom-accordion-arrow .card-header .accor-arrow-icon {
    position: absolute;
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    background-color: #525ce5;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    right: 10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%); }

.custom-accordion-arrow a.collapsed i.accor-arrow-icon:before {
  content: "\F0142"; }

.font-size-10 {
  font-size: 10px !important; }

.font-size-11 {
  font-size: 11px !important; }

.font-size-12 {
  font-size: 12px !important; }

.font-size-13 {
  font-size: 13px !important; }

.font-size-14 {
  font-size: 14px !important; }

.font-size-15 {
  font-size: 15px !important; }

.font-size-16 {
  font-size: 16px !important; }

.font-size-17 {
  font-size: 17px !important; }

.font-size-18 {
  font-size: 18px !important; }

.font-size-20 {
  font-size: 20px !important; }

.font-size-22 {
  font-size: 22px !important; }

.font-size-24 {
  font-size: 24px !important; }

.media {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start; }

.media-body {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1; }

.social-list-item {
  height: 2rem;
  width: 2rem;
  line-height: calc(2rem - 2px);
  display: block;
  border: 1px solid #b3b7c1;
  border-radius: 50%;
  color: #b3b7c1;
  text-align: center;
  -webkit-transition: all 0.4s;
  transition: all 0.4s; }
  .social-list-item:hover {
    color: #c3cbe4;
    background-color: #1f293f; }

.w-xs {
  min-width: 80px; }

.w-sm {
  min-width: 95px; }

.w-md {
  min-width: 110px; }

.w-lg {
  min-width: 140px; }

.w-xl {
  min-width: 160px; }

.bg-overlay {
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  opacity: 0.7;
  background-color: #000; }

.flex-1 {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1; }

.alert-dismissible .btn-close {
  font-size: 10px;
  padding: 1.05rem 1.25rem;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat; }

#preloader {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-color: #1f293f;
  z-index: 9999; }

#status {
  width: 40px;
  height: 40px;
  position: absolute;
  right: 50%;
  top: 50%;
  margin: -20px -20px 0 0; }

.spinner-chase {
  margin: 0 auto;
  width: 40px;
  height: 40px;
  position: relative;
  -webkit-animation: spinner-chase 2.5s infinite linear both;
          animation: spinner-chase 2.5s infinite linear both; }

.chase-dot {
  width: 100%;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  -webkit-animation: chase-dot 2.0s infinite ease-in-out both;
          animation: chase-dot 2.0s infinite ease-in-out both; }
  .chase-dot:before {
    content: '';
    display: block;
    width: 25%;
    height: 25%;
    background-color: #525ce5;
    border-radius: 100%;
    -webkit-animation: chase-dot-before 2.0s infinite ease-in-out both;
            animation: chase-dot-before 2.0s infinite ease-in-out both; }
  .chase-dot:nth-child(1) {
    -webkit-animation-delay: -1.1s;
            animation-delay: -1.1s; }
    .chase-dot:nth-child(1):before {
      -webkit-animation-delay: -1.1s;
              animation-delay: -1.1s; }
  .chase-dot:nth-child(2) {
    -webkit-animation-delay: -1.0s;
            animation-delay: -1.0s; }
    .chase-dot:nth-child(2):before {
      -webkit-animation-delay: -1.0s;
              animation-delay: -1.0s; }
  .chase-dot:nth-child(3) {
    -webkit-animation-delay: -0.9s;
            animation-delay: -0.9s; }
    .chase-dot:nth-child(3):before {
      -webkit-animation-delay: -0.9s;
              animation-delay: -0.9s; }
  .chase-dot:nth-child(4) {
    -webkit-animation-delay: -0.8s;
            animation-delay: -0.8s; }
    .chase-dot:nth-child(4):before {
      -webkit-animation-delay: -0.8s;
              animation-delay: -0.8s; }
  .chase-dot:nth-child(5) {
    -webkit-animation-delay: -0.7s;
            animation-delay: -0.7s; }
    .chase-dot:nth-child(5):before {
      -webkit-animation-delay: -0.7s;
              animation-delay: -0.7s; }
  .chase-dot:nth-child(6) {
    -webkit-animation-delay: -0.6s;
            animation-delay: -0.6s; }
    .chase-dot:nth-child(6):before {
      -webkit-animation-delay: -0.6s;
              animation-delay: -0.6s; }

@-webkit-keyframes spinner-chase {
  100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg); } }

@keyframes spinner-chase {
  100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg); } }

@-webkit-keyframes chase-dot {
  80%, 100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg); } }

@keyframes chase-dot {
  80%, 100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg); } }

@-webkit-keyframes chase-dot-before {
  50% {
    -webkit-transform: scale(0.4);
            transform: scale(0.4); }
  100%, 0% {
    -webkit-transform: scale(1);
            transform: scale(1); } }

@keyframes chase-dot-before {
  50% {
    -webkit-transform: scale(0.4);
            transform: scale(0.4); }
  100%, 0% {
    -webkit-transform: scale(1);
            transform: scale(1); } }

.form-check-right {
  padding-right: 0;
  display: inline-block;
  padding-left: 1.5em; }
  .form-check-right .form-check-input {
    float: left;
    margin-right: 0;
    margin-left: -1.5em; }
  .form-check-right .form-check-label {
    display: block; }

.form-check {
  position: relative;
  text-align: right; }

.form-check-label {
  cursor: pointer;
  margin-bottom: 0; }

.dash-summary {
  border-top: 1px solid #2f3a50; }

.dash-main-border {
  border-bottom: 1px solid #2f3a50; }

.dash-info-widget {
  background: #253048; }

.dash-goal {
  border-right: 1px solid #2f3a50; }

@media (max-width: 768px) {
  .dash-goal {
    border-right: none; } }

.carousel-indicators {
  bottom: -20px; }
  .carousel-indicators button {
    background-color: #525ce5 !important;
    width: 10px !important;
    height: 10px !important;
    border-radius: 50% !important;
    margin: 5px;
    opacity: 0.5; }

.mini-stats-wid .mini-stat-icon {
  overflow: hidden;
  position: relative; }
  .mini-stats-wid .mini-stat-icon:before, .mini-stats-wid .mini-stat-icon:after {
    content: "";
    position: absolute;
    width: 8px;
    height: 54px;
    background-color: rgba(255, 255, 255, 0.1);
    right: 16px;
    -webkit-transform: rotate(-32deg);
            transform: rotate(-32deg);
    top: -5px;
    -webkit-transition: all 0.4s;
    transition: all 0.4s; }
  .mini-stats-wid .mini-stat-icon::after {
    right: -12px;
    width: 12px;
    -webkit-transition: all 0.2s;
    transition: all 0.2s; }

.mini-stats-wid:hover .mini-stat-icon::after {
  right: 60px; }

.inbox-wid .inbox-list-item a {
  color: #a7aebd;
  display: block;
  padding: 11px 0px;
  border-bottom: 1px solid #2f3a50; }

.inbox-wid .inbox-list-item:first-child a {
  padding-top: 0px; }

.inbox-wid .inbox-list-item:last-child a {
  border-bottom: 0px; }

.activity-border:before {
  content: "";
  position: absolute;
  height: 38px;
  border-right: 2px dashed #2f3a50;
  top: 40px;
  right: 0px; }

.activity-wid {
  margin-right: 16px; }
  .activity-wid .activity-list {
    position: relative;
    padding: 0 30px 33px 0; }
    .activity-wid .activity-list .activity-icon {
      position: absolute;
      right: -20px;
      top: 0px;
      z-index: 2; }
    .activity-wid .activity-list:last-child {
      padding-bottom: 0px; }

.button-items {
  margin-right: -8px;
  margin-bottom: -12px; }
  .button-items .btn {
    margin-bottom: 12px;
    margin-right: 8px; }

.mfp-popup-form {
  max-width: 1140px; }

.bs-example-modal {
  position: relative;
  top: auto;
  left: auto;
  bottom: auto;
  right: auto;
  z-index: 1;
  display: block; }

.icon-demo-content {
  text-align: center;
  color: #b3b7c1; }
  .icon-demo-content i {
    display: block;
    font-size: 24px;
    color: #c3cbe4;
    width: 48px;
    height: 48px;
    line-height: 46px;
    margin: 0px auto;
    margin-bottom: 16px;
    border-radius: 4px;
    border: 1px solid #2f3a50;
    -webkit-transition: all 0.4s;
    transition: all 0.4s; }
  .icon-demo-content .col-lg-4 {
    margin-top: 24px; }
    .icon-demo-content .col-lg-4:hover i {
      background-color: #525ce5;
      color: #fff; }

.grid-structure .grid-container {
  background-color: #303d59;
  margin-top: 10px;
  font-size: .8rem;
  font-weight: 500;
  padding: 10px 20px; }

.card-radio {
  background-color: #1f293f;
  border: 2px solid #2f3a50;
  border-radius: 0.25rem;
  padding: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }
  .card-radio:hover {
    cursor: pointer; }

.card-radio-label {
  display: block; }

.card-radio-input {
  display: none; }
  .card-radio-input:checked + .card-radio {
    border-color: #525ce5 !important; }

.navs-carousel .owl-nav {
  margin-top: 16px; }
  .navs-carousel .owl-nav button {
    width: 30px;
    height: 30px;
    line-height: 28px !important;
    font-size: 20px !important;
    border-radius: 50% !important;
    background-color: rgba(82, 92, 229, 0.25) !important;
    color: #525ce5 !important;
    margin: 4px 8px !important; }

@media print {
  .vertical-menu,
  .right-bar,
  .page-title-box,
  .navbar-header,
  .footer {
    display: none !important; }
  .card-body,
  .main-content,
  .right-bar,
  .page-content,
  body {
    padding: 0;
    margin: 0; }
  .card {
    border: 0; } }

[data-simplebar] {
  position: relative;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -ms-flex-line-pack: start;
      align-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start; }

.simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit; }

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0; }

.simplebar-offset {
  direction: inherit !important;
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  right: 0 !important;
  bottom: 0;
  left: 0 !important;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch; }

.simplebar-content-wrapper {
  direction: inherit;
  -webkit-box-sizing: border-box !important;
          box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%;
  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  visibility: visible;
  overflow: auto;
  /* Scroll on this element otherwise element can't have a padding applied properly */
  max-width: 100%;
  /* Not required for horizontal scroll to trigger */
  max-height: 100%;
  /* Needed for vertical scroll to trigger */
  scrollbar-width: none;
  padding: 0px !important; }

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none; }

.simplebar-content:before,
.simplebar-content:after {
  content: ' ';
  display: table; }

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none; }

.simplebar-height-auto-observer-wrapper {
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: right;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  -webkit-box-flex: inherit;
      -ms-flex-positive: inherit;
          flex-grow: inherit;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -ms-flex-preferred-size: 0;
      flex-basis: 0; }

.simplebar-height-auto-observer {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  right: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1; }

.simplebar-track {
  z-index: 1;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden; }

[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none; }

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all; }

.simplebar-scrollbar {
  position: absolute;
  left: 2px;
  width: 6px;
  min-height: 10px; }

.simplebar-scrollbar:before {
  position: absolute;
  content: '';
  background: #a2adb7;
  border-radius: 7px;
  right: 0;
  left: 0;
  opacity: 0;
  -webkit-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear; }

.simplebar-scrollbar.simplebar-visible:before {
  /* When hovered, remove all transitions from drag handle */
  opacity: 0.5;
  -webkit-transition: opacity 0s linear;
  transition: opacity 0s linear; }

.simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px; }

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px; }

.simplebar-track.simplebar-horizontal {
  right: 0;
  height: 11px; }

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  height: 100%;
  right: 2px;
  left: 2px; }

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  left: auto;
  right: 0;
  top: 2px;
  height: 7px;
  min-height: 0;
  min-width: 10px;
  width: auto; }

/* Rtl support */
[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {
  left: auto;
  right: 0; }

.hs-dummy-scrollbar-size {
  direction: ltr;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll; }

.simplebar-hide-scrollbar {
  position: fixed;
  right: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none; }

.custom-scroll {
  height: 100%; }

.fc-toolbar h2 {
  font-size: 16px;
  line-height: 30px;
  text-transform: uppercase; }

.fc th.fc-widget-header {
  background: #2f3a50;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 0;
  text-transform: uppercase;
  font-weight: 600; }

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
  border-color: #2f3a50; }

.fc-unthemed td.fc-today {
  background: #26324d; }

.fc-button {
  background: #1f293f;
  border-color: #2f3a50;
  color: #d6dae5;
  text-transform: capitalize;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 6px 12px !important;
  height: auto !important; }

.fc-state-down,
.fc-state-active,
.fc-state-disabled {
  background-color: #525ce5;
  color: #fff;
  text-shadow: none; }

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 0.8125rem;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center; }

#external-events .external-event {
  text-align: right !important;
  padding: 8px 16px; }

.fc-event, .fc-event-dot {
  background-color: #525ce5; }

.fc-event .fc-content {
  color: #fff; }

.fc .table-bordered td, .fc .table-bordered th {
  border-color: #2f3a50; }

@media (max-width: 575.98px) {
  .fc .fc-toolbar {
    display: block; } }

.fc .fc-toolbar h2 {
  font-size: 16px;
  line-height: 30px;
  text-transform: uppercase; }

@media (max-width: 767.98px) {
  .fc .fc-toolbar .fc-left,
  .fc .fc-toolbar .fc-right,
  .fc .fc-toolbar .fc-center {
    float: none;
    display: block;
    text-align: center;
    clear: both;
    margin: 10px 0; }
  .fc .fc-toolbar > * > * {
    float: none; }
  .fc .fc-toolbar .fc-today-button {
    display: none; } }

.fc .fc-toolbar .btn {
  text-transform: capitalize; }

.fc-bootstrap .fc-today.alert-info {
  background-color: #2f3a50; }

.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {
  background-color: #000 !important; }

[dir="rtl"] .fc-header-toolbar {
  direction: rtl !important; }

[dir="rtl"] .fc-toolbar > * > :not(:first-child) {
  margin-right: .75em; }

.sp-container {
  background-color: #1f293f;
  z-index: 1000; }
  .sp-container button {
    padding: .25rem .5rem;
    font-size: .71094rem;
    border-radius: .2rem;
    font-weight: 400;
    color: #eff2f7; }
    .sp-container button.sp-palette-toggle {
      background-color: #2f3a50; }
    .sp-container button.sp-choose {
      background-color: #23c58f;
      margin-right: 5px;
      margin-left: 0; }

.sp-palette-container {
  border-left: 1px solid #2f3a50; }

.sp-input {
  background-color: #283247;
  border-color: #313a4e !important;
  color: #b3b7c1; }
  .sp-input:focus {
    outline: none; }

[dir="rtl"] .sp-alpha {
  direction: ltr; }

[dir="rtl"] .sp-original-input-container .sp-add-on {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 4px !important; }

[dir="rtl"] input.spectrum.with-add-on {
  border: 1px solid #313a4e;
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem; }

#session-timeout-dialog .close {
  display: none; }

#session-timeout-dialog .countdown-holder {
  color: #f14e4e;
  font-weight: 500; }

#session-timeout-dialog .btn-default {
  background-color: #fff;
  color: #f14e4e;
  -webkit-box-shadow: none;
          box-shadow: none; }

.irs {
  font-family: var(--bs-font-sans-serif); }

.irs--round .irs-bar,
.irs--round .irs-to,
.irs--round .irs-from,
.irs--round .irs-single {
  background: #525ce5 !important;
  font-size: 11px; }

.irs--round .irs-to:before,
.irs--round .irs-from:before,
.irs--round .irs-single:before {
  display: none; }

.irs--round .irs-line {
  background: #2f3a50;
  border-color: #2f3a50; }

.irs--round .irs-grid-text {
  font-size: 11px;
  color: #b3b7c1; }

.irs--round .irs-min,
.irs--round .irs-max {
  color: #b3b7c1;
  background: #2f3a50;
  font-size: 11px; }

.irs--round .irs-handle {
  border: 2px solid #525ce5;
  width: 10px;
  height: 16px;
  top: 29px;
  background-color: #1f293f !important; }

.swal2-container .swal2-title {
  font-size: 24px;
  font-weight: 500; }

.swal2-content {
  font-size: 16px; }

.swal2-icon.swal2-question {
  border-color: #5ba4e5;
  color: #5ba4e5; }

.swal2-icon.swal2-success [class^=swal2-success-line] {
  background-color: #23c58f; }

.swal2-icon.swal2-success .swal2-success-ring {
  border-color: rgba(35, 197, 143, 0.3); }

.swal2-icon.swal2-warning {
  border-color: #eeb148;
  color: #eeb148; }

.swal2-styled:focus {
  -webkit-box-shadow: none;
          box-shadow: none; }

.swal2-progress-steps .swal2-progress-step {
  background: #525ce5; }
  .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
    background: #525ce5; }
    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {
      background: rgba(82, 92, 229, 0.3); }

.swal2-progress-steps .swal2-progress-step-line {
  background: #525ce5; }

.swal2-loader {
  border-color: #525ce5 transparent #525ce5 transparent; }

.symbol {
  border-color: #1f293f; }

.rating-symbol-background, .rating-symbol-foreground {
  font-size: 24px; }

.rating-symbol-foreground {
  top: 0px; }

.rating-star > span {
  display: inline-block;
  vertical-align: middle; }
  .rating-star > span.badge {
    margin-right: 4px; }

.error {
  color: #f14e4e; }

.parsley-error {
  border-color: #f14e4e; }

.parsley-errors-list {
  display: none;
  margin: 0;
  padding: 0; }
  .parsley-errors-list.filled {
    display: block; }
  .parsley-errors-list > li {
    font-size: 12px;
    list-style: none;
    color: #f14e4e;
    margin-top: 5px; }

.select2-container {
  display: block; }
  .select2-container .select2-selection--single {
    background-color: #283247;
    border: 1px solid #313a4e;
    height: 38px; }
    .select2-container .select2-selection--single:focus {
      outline: none; }
    .select2-container .select2-selection--single .select2-selection__rendered {
      line-height: 36px;
      padding-right: 12px;
      color: #b3b7c1; }
    .select2-container .select2-selection--single .select2-selection__arrow {
      height: 34px;
      width: 34px;
      left: 3px; }
      .select2-container .select2-selection--single .select2-selection__arrow b {
        border-color: #b3b7c1 transparent transparent transparent;
        border-width: 6px 6px 0 6px; }
    .select2-container .select2-selection--single .select2-selection__placeholder {
      color: #a7aebd; }

.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #b3b7c1 transparent !important;
  border-width: 0 6px 6px 6px !important; }

.select2-container--default .select2-search--dropdown {
  padding: 10px;
  background-color: #1f293f; }
  .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #313a4e;
    background-color: #283247;
    color: #c3cbe4;
    outline: none; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #525ce5; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #2f3a50;
  color: #e9ecef; }
  .select2-container--default .select2-results__option[aria-selected=true]:hover {
    background-color: #525ce5;
    color: #fff; }

.select2-results__option {
  padding: 6px 12px; }

.select2-dropdown {
  border: 1px solid #26324d;
  background-color: #1f293f;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); }

.select2-search input {
  border: 1px solid #2f3a50; }

.select2-container .select2-selection--multiple {
  min-height: 38px;
  background-color: #283247;
  border: 1px solid #313a4e !important; }
  .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 2px 10px; }
  .select2-container .select2-selection--multiple .select2-search__field {
    border: 0;
    color: #b3b7c1; }
    .select2-container .select2-selection--multiple .select2-search__field::-webkit-input-placeholder {
      color: #b3b7c1; }
    .select2-container .select2-selection--multiple .select2-search__field::-moz-placeholder {
      color: #b3b7c1; }
    .select2-container .select2-selection--multiple .select2-search__field:-ms-input-placeholder {
      color: #b3b7c1; }
    .select2-container .select2-selection--multiple .select2-search__field::-ms-input-placeholder {
      color: #b3b7c1; }
    .select2-container .select2-selection--multiple .select2-search__field::placeholder {
      color: #b3b7c1; }
  .select2-container .select2-selection--multiple .select2-selection__choice {
    background-color: #1f293f;
    border: 1px solid #2f3a50;
    border-radius: 1px;
    padding: 0 7px; }

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #a7aebd; }

.select2-container--default .select2-results__group {
  font-weight: 600; }

.select2-result-repository__avatar {
  float: right;
  width: 60px;
  margin-left: 10px; }
  .select2-result-repository__avatar img {
    width: 100%;
    height: auto;
    border-radius: 2px; }

.select2-result-repository__statistics {
  margin-top: 7px; }

.select2-result-repository__forks,
.select2-result-repository__stargazers,
.select2-result-repository__watchers {
  display: inline-block;
  font-size: 11px;
  margin-left: 1em;
  color: #b3b7c1; }
  .select2-result-repository__forks .fa,
  .select2-result-repository__stargazers .fa,
  .select2-result-repository__watchers .fa {
    margin-left: 4px; }
    .select2-result-repository__forks .fa.fa-flash::before,
    .select2-result-repository__stargazers .fa.fa-flash::before,
    .select2-result-repository__watchers .fa.fa-flash::before {
      content: "\f0e7";
      font-family: 'Font Awesome 5 Free'; }

.select2-results__option--highlighted .select2-result-repository__forks,
.select2-results__option--highlighted .select2-result-repository__stargazers,
.select2-results__option--highlighted .select2-result-repository__watchers {
  color: rgba(255, 255, 255, 0.8); }

.select2-result-repository__meta {
  overflow: hidden; }

.img-flag {
  margin-left: 7px;
  height: 15px;
  width: 18px; }

/* CSS Switch */
input[switch] {
  display: none; }
  input[switch] + label {
    font-size: 1em;
    line-height: 1;
    width: 56px;
    height: 24px;
    background-color: #a7aebd;
    background-image: none;
    border-radius: 2rem;
    padding: 0.16667rem;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    position: relative;
    font-weight: 500;
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out; }
    input[switch] + label:before {
      color: #eff2f7;
      content: attr(data-off-label);
      display: block;
      font-family: inherit;
      font-weight: 500;
      font-size: 12px;
      line-height: 21px;
      position: absolute;
      left: 1px;
      margin: 3px;
      top: -2px;
      text-align: center;
      min-width: 1.66667rem;
      overflow: hidden;
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out; }
    input[switch] + label:after {
      content: '';
      position: absolute;
      right: 3px;
      background-color: #1f293f;
      -webkit-box-shadow: none;
              box-shadow: none;
      border-radius: 2rem;
      height: 20px;
      width: 20px;
      top: 2px;
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out; }
  input[switch]:checked + label {
    background-color: #525ce5; }

input[switch]:checked + label {
  background-color: #525ce5; }
  input[switch]:checked + label:before {
    color: #fff;
    content: attr(data-on-label);
    left: auto;
    right: 3px; }
  input[switch]:checked + label:after {
    right: 33px;
    background-color: #1f293f; }

input[switch="bool"] + label {
  background-color: #f14e4e; }

input[switch="bool"] + label:before, input[switch="bool"]:checked + label:before,
input[switch="default"]:checked + label:before {
  color: #fff; }

input[switch="bool"]:checked + label {
  background-color: #23c58f; }

input[switch="default"]:checked + label {
  background-color: #a2a2a2; }

input[switch="primary"]:checked + label {
  background-color: #525ce5; }

input[switch="success"]:checked + label {
  background-color: #23c58f; }

input[switch="info"]:checked + label {
  background-color: #5ba4e5; }

input[switch="warning"]:checked + label {
  background-color: #eeb148; }

input[switch="danger"]:checked + label {
  background-color: #f14e4e; }

input[switch="dark"]:checked + label {
  background-color: #eff2f7; }

.square-switch {
  margin-left: 7px; }
  .square-switch input[switch] + label, .square-switch input[switch] + label:after {
    border-radius: 4px; }

.datepicker {
  border: 1px solid #303d59;
  padding: 8px;
  z-index: 999 !important; }
  .datepicker table tr th {
    font-weight: 500; }
  .datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {
    background-color: #525ce5 !important;
    background-image: none;
    -webkit-box-shadow: none;
            box-shadow: none;
    color: #fff !important; }
  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,
  .datepicker table tr td span.focused,
  .datepicker table tr td span:hover {
    background: #1f293f; }
  .datepicker table tr td.new, .datepicker table tr td.old,
  .datepicker table tr td span.new,
  .datepicker table tr td span.old {
    color: #b3b7c1;
    opacity: 0.6; }
  .datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {
    background-color: #2f3a50; }

.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {
  padding: 7px; }

.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }

.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }

.bootstrap-touchspin .input-group-btn-vertical {
  left: 0 !important;
  right: 100% !important; }

.bootstrap-touchspin .bootstrap-touchspin-up {
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important; }

.bootstrap-touchspin .bootstrap-touchspin-down {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 4px !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important; }

.table-bordered {
  border: 1px solid #2f3a50; }

div.dataTables_wrapper div.dataTables_filter {
  text-align: left; }
  div.dataTables_wrapper div.dataTables_filter input {
    margin-right: 0.5em;
    margin-left: 0; }

.tox-tinymce {
  border: 2px solid #2f3a50 !important; }

.tox .tox-statusbar {
  border-top: 1px solid #2f3a50 !important; }

.tox .tox-menubar,
.tox .tox-edit-area__iframe,
.tox .tox-statusbar {
  background-color: #1f293f !important;
  background: none !important; }

.tox .tox-mbtn {
  color: #d6dae5 !important; }
  .tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {
    background-color: #2f3a50 !important; }

.tox .tox-tbtn:hover {
  background-color: #2f3a50 !important; }

.tox .tox-toolbar__primary {
  border-color: #2f3a50 !important; }

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
  background: #2f3a50 !important; }

.tox .tox-tbtn {
  color: #d6dae5 !important; }
  .tox .tox-tbtn svg {
    fill: #d6dae5 !important; }

.tox .tox-edit-area__iframe {
  background-color: #1f293f !important; }

.tox .tox-statusbar a,
.tox .tox-statusbar__path-item,
.tox .tox-statusbar__wordcount {
  color: #d6dae5 !important; }

.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {
  border-left: 1px solid #262e40 !important; }

.tox-tinymce-aux {
  z-index: 1000 !important; }

/* Dropzone */
.dropzone {
  min-height: 230px;
  border: 2px dashed #a7aebd;
  background: #1f293f;
  border-radius: 6px; }
  .dropzone .dz-message {
    font-size: 24px;
    width: 100%; }

.twitter-bs-wizard .twitter-bs-wizard-nav {
  position: relative; }
  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {
    content: "";
    width: 189px;
    height: 2px;
    background: rgba(82, 92, 229, 0.2);
    position: absolute;
    top: 26px;
    margin-right: 100px; }
  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {
    display: inline-block;
    border-radius: 30px;
    padding: 4px 0px;
    width: 200px;
    line-height: 34px;
    color: #525ce5;
    text-align: center;
    position: relative;
    background-color: rgba(82, 92, 229, 0.2); }
    @media (max-width: 991.98px) {
      .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {
        display: block;
        margin: 0 auto 8px !important;
        width: 170px; } }
  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {
    display: block;
    margin-top: 8px;
    font-weight: 600; }
    @media (max-width: 575.98px) {
      .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {
        display: none; } }
  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {
    background-color: transparent;
    color: #d6dae5; }
    .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {
      background-color: #525ce5;
      color: #fff; }

.twitter-bs-wizard .twitter-bs-wizard-pager-link {
  padding-top: 24px;
  padding-right: 0;
  list-style: none;
  margin-bottom: 0; }
  .twitter-bs-wizard .twitter-bs-wizard-pager-link li {
    display: inline-block; }
    .twitter-bs-wizard .twitter-bs-wizard-pager-link li a {
      display: inline-block;
      padding: .47rem .75rem;
      background-color: #525ce5;
      color: #fff;
      border-radius: .25rem; }
    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {
      cursor: not-allowed;
      background-color: #757dea; }
    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {
      float: left; }

.twitter-bs-wizard-tab-content {
  padding-top: 24px;
  min-height: 262px; }

@media (max-width: 1024px) {
  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {
    background: transparent !important; } }

.table-rep-plugin .btn-toolbar {
  display: block; }

.table-rep-plugin .table-responsive {
  border: none !important; }

.table-rep-plugin .btn-group .btn-default {
  background-color: #c3cbe4;
  color: #2f3a50;
  border: 1px solid #c3cbe4; }
  .table-rep-plugin .btn-group .btn-default.btn-primary {
    background-color: #525ce5;
    border-color: #525ce5;
    color: #fff;
    -webkit-box-shadow: 0 0 0 2px rgba(82, 92, 229, 0.5);
            box-shadow: 0 0 0 2px rgba(82, 92, 229, 0.5); }

.table-rep-plugin .btn-group.pull-right {
  float: left; }
  .table-rep-plugin .btn-group.pull-right .dropdown-menu {
    left: 0;
    -webkit-transform: none !important;
            transform: none !important;
    top: 100% !important; }

.table-rep-plugin tbody th {
  font-size: 14px;
  font-weight: normal; }

.table-rep-plugin .checkbox-row {
  padding-right: 40px;
  color: #a7aebd !important; }
  .table-rep-plugin .checkbox-row:hover {
    background-color: #222d46 !important; }
  .table-rep-plugin .checkbox-row label {
    display: inline-block;
    padding-right: 5px;
    position: relative; }
    .table-rep-plugin .checkbox-row label::before {
      -o-transition: 0.3s ease-in-out;
      -webkit-transition: 0.3s ease-in-out;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #2f3a50;
      content: "";
      display: inline-block;
      height: 17px;
      right: 0;
      margin-right: -20px;
      position: absolute;
      transition: 0.3s ease-in-out;
      width: 17px;
      outline: none !important; }
    .table-rep-plugin .checkbox-row label::after {
      color: #1f293f;
      display: inline-block;
      font-size: 11px;
      height: 16px;
      right: 0;
      margin-right: -20px;
      padding-right: 3px;
      padding-top: 1px;
      position: absolute;
      top: -1px;
      width: 16px; }
  .table-rep-plugin .checkbox-row input[type="checkbox"] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: none !important; }
    .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label {
      opacity: 0.65; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:focus + label::before {
    outline-offset: -2px;
    outline: none; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    content: "\f00c";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label::before {
    background-color: #303d59;
    cursor: not-allowed; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::before {
    background-color: #525ce5;
    border-color: #525ce5; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    color: #fff; }

.table-rep-plugin .fixed-solution .sticky-table-header {
  top: 70px !important;
  background-color: #525ce5; }
  .table-rep-plugin .fixed-solution .sticky-table-header table {
    color: #fff; }

.table-rep-plugin table.focus-on tbody tr.focused th,
.table-rep-plugin table.focus-on tbody tr.focused td,
.table-rep-plugin .sticky-table-header {
  background: #525ce5;
  border-color: #525ce5;
  color: #fff; }
  .table-rep-plugin table.focus-on tbody tr.focused th table,
  .table-rep-plugin table.focus-on tbody tr.focused td table,
  .table-rep-plugin .sticky-table-header table {
    color: #fff; }

@media (min-width: 992px) {
  body[data-layout="horizontal"] .fixed-solution .sticky-table-header {
    top: 148px !important; } }

.table-striped > tbody > tr:nth-of-type(odd).focused {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }
  .table-striped > tbody > tr:nth-of-type(odd).focused td, .table-striped > tbody > tr:nth-of-type(odd).focused th {
    -webkit-box-shadow: none !important;
            box-shadow: none !important; }

.table-edits input, .table-edits select {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  border: 1px solid #313a4e;
  background-color: #283247;
  color: #b3b7c1;
  border-radius: 0.25rem; }
  .table-edits input:focus, .table-edits select:focus {
    outline: none;
    border-color: #353e52; }

.apex-charts {
  min-height: 10px !important; }
  .apex-charts text {
    font-family: var(--bs-font-sans-serif) !important;
    fill: #b3b7c1; }
  .apex-charts .apexcharts-canvas {
    margin: 0 auto; }

.apexcharts-tooltip-title,
.apexcharts-tooltip-text {
  font-family: var(--bs-font-sans-serif) !important; }

.apexcharts-legend-series {
  font-weight: 500; }

.apexcharts-gridline {
  pointer-events: none;
  stroke: #32394e; }

.apexcharts-legend-text {
  color: #c3cbe4 !important;
  font-family: var(--bs-font-sans-serif) !important;
  font-size: 13px !important; }

.apexcharts-pie-label {
  fill: #fff !important; }

.apexcharts-yaxis text,
.apexcharts-xaxis text {
  font-family: var(--bs-font-sans-serif) !important;
  fill: #b3b7c1; }

.ct-golden-section:before {
  float: none; }

.ct-chart {
  max-height: 300px; }
  .ct-chart .ct-label {
    fill: #b3b7c1;
    color: #b3b7c1;
    font-size: 12px;
    line-height: 1; }

.ct-chart.simple-pie-chart-chartist .ct-label {
  color: #fff;
  fill: #fff;
  font-size: 16px; }

.ct-grid {
  stroke: rgba(239, 242, 247, 0.1); }

.ct-chart .ct-series.ct-series-a .ct-bar,
.ct-chart .ct-series.ct-series-a .ct-line,
.ct-chart .ct-series.ct-series-a .ct-point,
.ct-chart .ct-series.ct-series-a .ct-slice-donut {
  stroke: #525ce5; }

.ct-chart .ct-series.ct-series-b .ct-bar,
.ct-chart .ct-series.ct-series-b .ct-line,
.ct-chart .ct-series.ct-series-b .ct-point,
.ct-chart .ct-series.ct-series-b .ct-slice-donut {
  stroke: #23c58f; }

.ct-chart .ct-series.ct-series-c .ct-bar,
.ct-chart .ct-series.ct-series-c .ct-line,
.ct-chart .ct-series.ct-series-c .ct-point,
.ct-chart .ct-series.ct-series-c .ct-slice-donut {
  stroke: #f14e4e; }

.ct-chart .ct-series.ct-series-d .ct-bar,
.ct-chart .ct-series.ct-series-d .ct-line,
.ct-chart .ct-series.ct-series-d .ct-point,
.ct-chart .ct-series.ct-series-d .ct-slice-donut {
  stroke: #5ba4e5; }

.ct-chart .ct-series.ct-series-e .ct-bar,
.ct-chart .ct-series.ct-series-e .ct-line,
.ct-chart .ct-series.ct-series-e .ct-point,
.ct-chart .ct-series.ct-series-e .ct-slice-donut {
  stroke: #23c58f; }

.ct-chart .ct-series.ct-series-f .ct-bar,
.ct-chart .ct-series.ct-series-f .ct-line,
.ct-chart .ct-series.ct-series-f .ct-point,
.ct-chart .ct-series.ct-series-f .ct-slice-donut {
  stroke: #eff2f7; }

.ct-chart .ct-series.ct-series-g .ct-bar,
.ct-chart .ct-series.ct-series-g .ct-line,
.ct-chart .ct-series.ct-series-g .ct-point,
.ct-chart .ct-series.ct-series-g .ct-slice-donut {
  stroke: #6f42c1; }

.ct-series-a .ct-area,
.ct-series-a .ct-slice-pie {
  fill: #525ce5; }

.ct-series-b .ct-area,
.ct-series-b .ct-slice-pie {
  fill: #23c58f; }

.ct-series-c .ct-area,
.ct-series-c .ct-slice-pie {
  fill: #eeb148; }

.ct-series-d .ct-area,
.ct-series-d .ct-slice-pie {
  fill: #23c58f; }

.ct-area {
  fill-opacity: .33; }

.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 10px;
  padding: 2px 10px;
  border-radius: 3px;
  background: #eff2f7;
  color: #2f3a50;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity .2s linear;
  transition: opacity .2s linear; }
  .chartist-tooltip.tooltip-show {
    opacity: 1; }

.ct-line {
  stroke-width: 3px; }

.ct-point {
  stroke-width: 7px; }

/* Flot chart */
.flotTip {
  padding: 8px 12px !important;
  background-color: #eff2f7 !important;
  border: 1px solid #eff2f7 !important;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  z-index: 100;
  color: #1f293f;
  opacity: 1;
  border-radius: 3px !important;
  font-size: 14px !important; }

.legend div {
  background-color: transparent !important; }

.legend tr {
  height: 30px; }

.legendLabel {
  padding-right: 5px;
  line-height: 10px;
  padding-left: 10px;
  font-size: 13px;
  font-weight: 500;
  color: #b3b7c1; }

.legendColorBox div {
  border-radius: 3px; }
  .legendColorBox div div {
    border-radius: 3px; }

.float-lable-box table {
  margin: 0 auto; }

@media (max-width: 575.98px) {
  .legendLabel {
    display: none; } }

.jqstooltip {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: auto !important;
  height: auto !important;
  background-color: #eff2f7 !important;
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  padding: 5px 10px !important;
  border-radius: 3px;
  border-color: #f8f9fa !important; }

.jqsfield {
  color: #1f293f !important;
  font-size: 12px !important;
  line-height: 18px !important;
  font-family: var(--bs-font-sans-serif) !important;
  font-weight: 500 !important; }

.gmaps, .gmaps-panaroma {
  height: 300px;
  background: #303d59;
  border-radius: 3px; }

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  background: #525ce5;
  border-radius: 4px;
  padding: 10px 20px; }

.gmaps-overlay_arrow {
  right: 50%;
  margin-right: -16px;
  width: 0;
  height: 0;
  position: absolute; }
  .gmaps-overlay_arrow.above {
    bottom: -15px;
    border-right: 16px solid transparent;
    border-left: 16px solid transparent;
    border-top: 16px solid #525ce5; }
  .gmaps-overlay_arrow.below {
    top: -15px;
    border-right: 16px solid transparent;
    border-left: 16px solid transparent;
    border-bottom: 16px solid #525ce5; }

.jvectormap-label {
  border: none;
  background: #eff2f7;
  color: #303d59;
  font-family: var(--bs-font-sans-serif);
  font-size: 0.875rem;
  padding: 5px 8px; }

.editable-input .form-control {
  display: inline-block; }

.editable-buttons {
  margin-right: 7px; }
  .editable-buttons .editable-cancel {
    margin-right: 7px; }

.home-btn {
  position: absolute;
  top: 15px;
  left: 25px; }

.home-center {
  display: table;
  width: 100%;
  height: 100%; }

.home-desc-center {
  display: table-cell;
  vertical-align: middle; }

.authentication-bg {
  background-image: url(../images/title-img.png);
  height: 100vh;
  background-size: cover;
  background-position: center; }

.authentication-bg .bg-overlay {
  background-color: #525ce5; }

.error-page {
  text-transform: uppercase;
  background: repeating-linear-gradient(-45deg, #525ce5, #525ce5 10px, #23c58f 10px, #23c58f 20px);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 120px;
  line-height: .8;
  position: relative; }

.faq-icon i {
  width: 30px;
  height: 30px;
  line-height: 28px;
  border: 1px solid;
  border-radius: 50%;
  text-align: center;
  float: left;
  font-size: 16px;
  display: inline-block; }

.faq-icon:after {
  content: "";
  position: absolute;
  width: 30px;
  height: 30px;
  opacity: 0.2;
  left: 50px;
  margin-top: -10px;
  border-radius: 50%;
  background: #525ce5; }

.search-box .form-control {
  border-radius: 30px;
  padding-right: 40px;
  border: 1px solid #2f3a50; }

.search-box .search-icon {
  font-size: 16px;
  position: absolute;
  right: 13px;
  top: 0;
  line-height: 38px; }

.categories-group-list {
  display: block;
  color: #eff2f7;
  font-weight: 500;
  padding: 8px 16px; }
  .categories-group-list[aria-expanded="true"] {
    background-color: #2f3a50; }
  .categories-group-list:last-child {
    border: 0; }
  .categories-group-list:hover {
    color: #eff2f7; }

.categories-list {
  padding: 8px 0px; }
  .categories-list li a {
    display: block;
    padding: 4px 16px;
    color: #a7aebd; }
  .categories-list li.active a {
    color: #525ce5; }

.product-detai-imgs .nav .nav-link {
  margin: 7px 0px; }
  .product-detai-imgs .nav .nav-link.active {
    background-color: #2f3a50; }

.product-color a {
  display: inline-block;
  text-align: center;
  color: #a7aebd; }
  .product-color a .product-color-item {
    margin: 7px;
    border: 2px solid #2f3a50;
    border-radius: 4px; }
  .product-color a.active, .product-color a:hover {
    color: #525ce5; }
    .product-color a.active .product-color-item, .product-color a:hover .product-color-item {
      border-color: #525ce5 !important; }

.product-track {
  border: 1px solid #2f3a50; }

.ecommerce-sortby-list li {
  color: #eff2f7; }
  .ecommerce-sortby-list li a {
    color: #a7aebd;
    padding: 4px; }
  .ecommerce-sortby-list li.active a {
    color: #525ce5; }

.product-img {
  position: relative; }
  .product-img .product-ribbon {
    position: absolute;
    top: 0;
    right: 0px;
    padding: 6px 8px;
    border-radius: 50% 50% 75% 25%/68% 44% 56% 32%;
    width: 62px;
    height: 60px;
    color: #fff;
    font-size: 15px;
    text-align: center; }
  .product-img .product-like {
    position: absolute;
    top: 0;
    left: 0; }
    .product-img .product-like a {
      display: inline-block;
      width: 40px;
      height: 40px;
      border: 2px solid #2f3a50;
      line-height: 38px;
      border-radius: 50%;
      text-align: center;
      color: #b3b7c1; }

.product-detail .nav-pills .nav-link {
  margin-bottom: 7px; }
  .product-detail .nav-pills .nav-link.active {
    background-color: #2f3a50; }
  .product-detail .nav-pills .nav-link .tab-img {
    width: 5rem; }

.product-detail .product-img {
  border: 1px solid #2f3a50;
  padding: 24px; }

.product-desc-list li {
  padding: 4px 0px; }

.product-review-link .list-inline-item a {
  color: #c3cbe4; }

.product-review-link .list-inline-item:not(:last-child) {
  margin-left: 14px; }

.product-cart-touchspin {
  border: 1px solid #313a4e;
  background-color: #283247;
  border-radius: 0.25rem; }
  .product-cart-touchspin .form-control {
    border-color: transparent;
    height: 32px; }
  .product-cart-touchspin .input-group-btn .btn {
    background-color: transparent !important;
    border-color: transparent !important;
    color: #525ce5 !important;
    font-size: 16px;
    padding: 3px 12px;
    -webkit-box-shadow: none;
            box-shadow: none; }

.shipping-address {
  -webkit-box-shadow: none;
          box-shadow: none; }
  .shipping-address.active {
    border-color: #525ce5 !important; }

.twitter-bs-wizard .chackout-border:before {
  content: "";
  width: 139px;
  height: 2px;
  background: rgba(82, 92, 229, 0.2);
  position: absolute;
  top: 26px;
  margin-right: 100px; }

.twitter-bs-wizard .add-product-border:before {
  content: "";
  width: 324px;
  height: 2px;
  background: rgba(82, 92, 229, 0.2);
  position: absolute;
  top: 26px;
  margin-right: 100px; }

@media (max-width: 1024px) {
  .twitter-bs-wizard .chackout-border, .twitter-bs-wizard .add-product-border {
    width: 180px; }
    .twitter-bs-wizard .chackout-border:before, .twitter-bs-wizard .add-product-border:before {
      background: transparent !important; } }

/* ==============
  Email
===================*/
.email-leftbar {
  width: 236px;
  float: right;
  padding: 20px;
  border-radius: 5px; }

.email-rightbar {
  margin-right: 260px; }

.chat-user-box p.user-title {
  color: #eff2f7;
  font-weight: 600; }

.chat-user-box p {
  font-size: 12px; }

@media (max-width: 767px) {
  .email-leftbar {
    float: none;
    width: 100%; }
  .email-rightbar {
    margin: 0; } }

.mail-list a {
  display: block;
  color: #c3cbe4;
  line-height: 24px;
  padding: 8px 5px; }
  .mail-list a.active {
    color: #f14e4e;
    font-weight: 500; }

.message-list {
  display: block;
  padding-right: 0; }
  .message-list li {
    position: relative;
    display: block;
    height: 50px;
    line-height: 50px;
    cursor: default;
    -webkit-transition-duration: .3s;
            transition-duration: .3s; }
    .message-list li a {
      color: #c3cbe4; }
    .message-list li:hover {
      background: #2f3a50;
      -webkit-transition-duration: .05s;
              transition-duration: .05s; }
    .message-list li .col-mail {
      float: right;
      position: relative; }
    .message-list li .col-mail-1 {
      width: 320px; }
      .message-list li .col-mail-1 .star-toggle,
      .message-list li .col-mail-1 .checkbox-wrapper-mail,
      .message-list li .col-mail-1 .dot {
        display: block;
        float: right; }
      .message-list li .col-mail-1 .dot {
        border: 4px solid transparent;
        border-radius: 100px;
        margin: 22px 26px 0;
        height: 0;
        width: 0;
        line-height: 0;
        font-size: 0; }
      .message-list li .col-mail-1 .checkbox-wrapper-mail {
        margin: 15px 20px 0 10px; }
      .message-list li .col-mail-1 .star-toggle {
        margin-top: 18px;
        margin-right: 5px; }
      .message-list li .col-mail-1 .title {
        position: absolute;
        top: 0;
        right: 110px;
        left: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-bottom: 0; }
    .message-list li .col-mail-2 {
      position: absolute;
      top: 0;
      right: 320px;
      left: 0;
      bottom: 0; }
      .message-list li .col-mail-2 .subject,
      .message-list li .col-mail-2 .date {
        position: absolute;
        top: 0; }
      .message-list li .col-mail-2 .subject {
        right: 0;
        left: 200px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap; }
      .message-list li .col-mail-2 .date {
        left: 0;
        width: 170px;
        padding-right: 80px; }
    .message-list li.active, .message-list li.active:hover {
      -webkit-box-shadow: inset -3px 0 0 #525ce5;
              box-shadow: inset -3px 0 0 #525ce5; }
    .message-list li.unread {
      background-color: #2f3a50;
      font-weight: 500;
      color: #dee4ef; }
      .message-list li.unread a {
        color: #dee4ef;
        font-weight: 500; }
  .message-list .checkbox-wrapper-mail {
    cursor: pointer;
    height: 20px;
    width: 20px;
    position: relative;
    display: inline-block;
    -webkit-box-shadow: inset 0 0 0 1px #a7aebd;
            box-shadow: inset 0 0 0 1px #a7aebd;
    border-radius: 1px; }
    .message-list .checkbox-wrapper-mail input {
      opacity: 0;
      cursor: pointer; }
    .message-list .checkbox-wrapper-mail input:checked ~ label {
      opacity: 1; }
    .message-list .checkbox-wrapper-mail label {
      position: absolute;
      height: 20px;
      width: 20px;
      right: 0;
      cursor: pointer;
      opacity: 0;
      margin-bottom: 0;
      -webkit-transition-duration: .05s;
              transition-duration: .05s;
      top: 0; }
      .message-list .checkbox-wrapper-mail label:before {
        content: "\F012C";
        font-family: "Material Design Icons";
        top: 0;
        height: 20px;
        color: #dee4ef;
        width: 20px;
        position: absolute;
        margin-top: -16px;
        right: 4px;
        font-size: 13px; }

@media (max-width: 575.98px) {
  .message-list li .col-mail-1 {
    width: 200px; } }

@media (min-width: 992px) {
  .chat-leftsidebar {
    min-width: 380px; } }

.chat-leftsidebar .chat-leftsidebar-nav .nav {
  background-color: #1f293f; }

.chat-noti-dropdown.active:before {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #f14e4e;
  border-radius: 50%;
  left: 0; }

.chat-noti-dropdown .btn {
  padding: 6px;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 20px; }

.chat-list {
  margin: 0; }
  .chat-list li.active a {
    background-color: #1f293f;
    -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); }
  .chat-list li a {
    display: block;
    padding: 14px 16px;
    color: #c3cbe4;
    -webkit-transition: all 0.4s;
    transition: all 0.4s;
    border: 1px solid #2f3a50;
    border-radius: 4px;
    margin-top: 10px; }
    .chat-list li a:hover {
      background-color: #1f293f;
      -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); }

.user-chat-nav .dropdown .nav-btn {
  height: 40px;
  width: 40px;
  line-height: 34px;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
  font-size: 16px;
  background-color: #2f3a50;
  border-radius: 50%; }

.user-chat-nav .dropdown .dropdown-menu {
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid #2f3a50; }

.chat-conversation li {
  clear: both; }

.chat-conversation .chat-day-title {
  position: relative;
  text-align: center;
  margin-bottom: 24px; }
  .chat-conversation .chat-day-title .title {
    background-color: #1f293f;
    position: relative;
    z-index: 1;
    padding: 6px 24px; }
  .chat-conversation .chat-day-title:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 1px;
    right: 0;
    left: 0;
    background-color: #2f3a50;
    top: 10px; }
  .chat-conversation .chat-day-title .badge {
    font-size: 12px; }

.chat-conversation .conversation-list {
  margin-bottom: 24px;
  display: inline-block;
  position: relative; }
  .chat-conversation .conversation-list .arrow-left {
    position: relative; }
    .chat-conversation .conversation-list .arrow-left:before {
      content: "";
      position: absolute;
      top: 10px;
      left: 100%;
      border: 7px solid transparent;
      border-left: 7px solid rgba(82, 92, 229, 0.1); }
  .chat-conversation .conversation-list .ctext-wrap {
    padding: 12px 24px;
    background-color: rgba(82, 92, 229, 0.1);
    border-radius: 8px 8px 0px 8px;
    overflow: hidden; }
    .chat-conversation .conversation-list .ctext-wrap .conversation-name {
      font-weight: 500;
      color: #525ce5;
      margin-bottom: 4px;
      position: relative; }
  .chat-conversation .conversation-list .dropdown {
    float: left; }
    .chat-conversation .conversation-list .dropdown .dropdown-toggle {
      font-size: 18px;
      padding: 4px;
      color: #c3cbe4; }
      @media (max-width: 575.98px) {
        .chat-conversation .conversation-list .dropdown .dropdown-toggle {
          display: none; } }
    .chat-conversation .conversation-list .dropdown .dropdown-menu {
      -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
      border: 1px solid #2f3a50; }
  .chat-conversation .conversation-list .chat-time {
    font-size: 12px; }

.chat-conversation .right .conversation-list {
  float: left; }
  .chat-conversation .right .conversation-list .arrow-right {
    position: relative; }
    .chat-conversation .right .conversation-list .arrow-right:before {
      content: "";
      position: absolute;
      top: 10px;
      right: 100%;
      border: 7px solid transparent;
      border-right: 7px solid #2f3a50; }
  .chat-conversation .right .conversation-list .ctext-wrap {
    background-color: #2f3a50;
    text-align: left;
    border-radius: 8px 8px 8px 0px; }
  .chat-conversation .right .conversation-list .dropdown {
    float: right; }
  .chat-conversation .right .conversation-list.last-chat .conversation-list:before {
    left: 0;
    right: auto; }

.chat-input-section {
  border-top: 1px solid #2f3a50; }

.chat-input {
  border-radius: 30px;
  background-color: #2f3a50 !important;
  border-color: #2f3a50 !important;
  padding-left: 120px; }

.chat-input-links {
  position: absolute;
  left: 16px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%); }
  .chat-input-links li a {
    font-size: 16px;
    line-height: 36px;
    padding: 0px 4px;
    display: inline-block; }

@media (max-width: 575.98px) {
  .chat-send {
    min-width: auto; } }

.search-box .search-icon {
  font-size: 16px;
  position: absolute;
  right: 13px;
  top: 2px;
  font-size: 15px;
  line-height: 34px; }

.search-box .form-control {
  padding-right: 40px;
  border-radius: 5px; }

.counter-number {
  font-size: 32px;
  text-align: center; }
  .counter-number span {
    font-size: 16px;
    display: block;
    padding-top: 7px; }

.coming-box {
  float: right;
  width: 21%;
  padding: 14px 7px;
  margin: 0px 12px 24px 12px;
  background-color: #fff;
  border-radius: 5px;
  border-radius: calc(0.25rem - 0);
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08); }

@media (max-width: 991.98px) {
  .coming-box {
    width: 40%; } }

/************** vertical timeline **************/
.timeline {
  position: relative;
  width: 100%;
  padding: 30px 0; }

.timeline .timeline-end,
.timeline .timeline-start,
.timeline .timeline-year {
  position: relative;
  width: 100%;
  text-align: center;
  z-index: 1; }

.timeline .timeline-end p,
.timeline .timeline-start p,
.timeline .timeline-year p {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin: 0;
  padding: 30px 0;
  text-align: center;
  background: url(../images/user-img.png);
  background-color: #525ce5;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 100px;
  color: #fff;
  text-transform: uppercase; }

.timeline .timeline-year {
  margin: 30px 0; }

.timeline .timeline-continue {
  position: relative;
  width: 100%;
  padding: 60px 0; }
  .timeline .timeline-continue:after {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    right: 50%;
    margin-right: -1px;
    background: #525ce5; }

.timeline .timeline-date {
  margin: 40px 10px 0 10px; }

.timeline .row.timeline-left,
.timeline .row.timeline-right .timeline-date {
  text-align: left; }

.timeline .row.timeline-right,
.timeline .row.timeline-left .timeline-date {
  text-align: right; }

.timeline .timeline-date::after {
  content: "";
  display: block;
  position: absolute;
  width: 14px;
  height: 14px;
  top: 45px;
  background: #525ce5;
  border-radius: 15px;
  z-index: 1; }

.timeline .row.timeline-left .timeline-date::after {
  right: -7px; }

.timeline .row.timeline-right .timeline-date::after {
  left: -7px; }

.timeline .timeline-box,
.timeline .timeline-launch {
  position: relative;
  display: inline-block;
  margin: 15px;
  padding: 20px;
  border: 1px solid #1f293f;
  border-radius: 6px; }

.timeline .timeline-launch {
  width: 100%;
  margin: 15px 0;
  padding: 0;
  border: none;
  text-align: center;
  background: transparent; }

.timeline .timeline-box::after,
.timeline .timeline-box::before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid; }

.timeline .row.timeline-left .timeline-box::after,
.timeline .row.timeline-left .timeline-box::before {
  right: 100%; }

.timeline .row.timeline-right .timeline-box::after,
.timeline .row.timeline-right .timeline-box::before {
  left: 100%; }

.timeline .timeline-launch .timeline-box::after,
.timeline .timeline-launch .timeline-box::before {
  right: 50%;
  margin-right: -10px; }

.timeline .timeline-box::after {
  top: 26px;
  border-color: transparent #2f3a50 transparent transparent;
  border-width: 10px; }

.timeline .timeline-box::before {
  top: 25px;
  border-color: transparent #1f293f transparent transparent;
  border-width: 11px; }

.timeline .row.timeline-right .timeline-box::after {
  border-color: transparent transparent transparent #2f3a50; }

.timeline .row.timeline-right .timeline-box::before {
  border-color: transparent transparent transparent #1f293f; }

.timeline .timeline-launch .timeline-box::after {
  top: -20px;
  border-color: transparent transparent #1f293f transparent; }

.timeline .timeline-launch .timeline-box::before {
  top: -19px;
  border-color: transparent transparent #2f3a50 transparent;
  border-width: 10px;
  z-index: 1; }

.timeline .timeline-launch .timeline-text {
  width: 100%; }

@media (max-width: 767px) {
  .timeline .timeline-continue::after {
    right: 40px; }
  .timeline .timeline-end,
  .timeline .timeline-start,
  .timeline .timeline-year,
  .timeline .row.timeline-left,
  .timeline .row.timeline-right .timeline-date,
  .timeline .row.timeline-right,
  .timeline .row.timeline-left .timeline-date,
  .timeline .timeline-launch {
    text-align: right; }
  .timeline .row.timeline-left .timeline-date::after,
  .timeline .row.timeline-right .timeline-date::after {
    right: 47px; }
  .timeline .timeline-box,
  .timeline .row.timeline-right .timeline-date,
  .timeline .row.timeline-left .timeline-date {
    margin-right: 55px; }
  .timeline .timeline-launch .timeline-box {
    margin-right: 0; }
  .timeline .row.timeline-left .timeline-box::after {
    right: -20px;
    border-color: transparent transparent transparent #2f3a50; }
  .timeline .row.timeline-left .timeline-box::before {
    right: -22px;
    border-color: transparent transparent transparent #1f293f; }
  .timeline .timeline-launch .timeline-box::after,
  .timeline .timeline-launch .timeline-box::before {
    right: 30px;
    margin-right: 0; } }

.pricing-nav-tabs {
  display: inline-block;
  background-color: #1f293f;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  padding: 4px;
  border-radius: 7px; }
  .pricing-nav-tabs li {
    display: inline-block; }

.pricing-box .plan-features li {
  padding: 7px 0px; }

/*********************
    Faqs
**********************/
.faq-nav-tabs .nav-item {
  margin: 0px 8px; }

.faq-nav-tabs .nav-link {
  text-align: center;
  margin-bottom: 8px;
  border: 2px solid #2f3a50;
  color: #a7aebd; }
  .faq-nav-tabs .nav-link .nav-icon {
    font-size: 40px;
    margin-bottom: 8px;
    display: block; }
  .faq-nav-tabs .nav-link.active {
    border-color: #525ce5;
    background-color: transparent;
    color: #a7aebd; }
    .faq-nav-tabs .nav-link.active .nav-icon {
      color: #525ce5; }

.text-error {
  font-size: 120px; }
  @media (max-width: 575.98px) {
    .text-error {
      font-size: 86px; } }

.error-text {
  color: #f14e4e;
  position: relative; }
  .error-text .error-img {
    position: absolute;
    width: 120px;
    right: -15px;
    left: 0;
    bottom: 47px; }
    @media (max-width: 575.98px) {
      .error-text .error-img {
        width: 86px;
        right: -12px;
        bottom: 38px; } }

/*# sourceMappingURL=app-dark-rtl.min.css.map */
