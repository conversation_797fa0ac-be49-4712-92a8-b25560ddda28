{"version": 3, "sources": ["app.scss", "app.css", "custom/fonts/_fonts.scss", "custom/structure/_topbar.scss", "_variables.scss", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_vertical.scss", "custom/structure/_horizontal-nav.scss", "../../node_modules/bootstrap/scss/_functions.scss", "custom/structure/_layouts.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "custom/components/_accordion.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_forms.scss", "custom/components/_widgets.scss", "custom/components/_demos.scss", "custom/components/_print.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_calendar.scss", "custom/plugins/_color-picker.scss", "custom/plugins/_session-timeout.scss", "custom/plugins/_range-slider.scss", "custom/plugins/_sweatalert2.scss", "custom/plugins/_rating.scss", "custom/plugins/_parsley.scss", "custom/plugins/_select2.scss", "custom/plugins/_switch.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_bootstrap-touchspin.scss", "custom/plugins/_datatable.scss", "custom/plugins/_form-editors.scss", "custom/plugins/_form-upload.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_table-editable.scss", "custom/plugins/_apexcharts.scss", "custom/plugins/_chartist.scss", "custom/plugins/_flot.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_google-map.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_x-editable.scss", "custom/pages/_authentication.scss", "custom/pages/_ecommerce.scss", "custom/pages/_email.scss", "custom/pages/_chat.scss", "custom/pages/_coming-soon.scss", "custom/pages/_timeline.scss", "custom/pages/_extras-pages.scss"], "names": [], "mappings": "AAAA;;;;;;CCMC;ACFD,2FAAY;ACAZ;EACI,eAAe;EACf,MAAM;EACN,QAAQ;EACR,OAAO;EACP,aAAa;EACb,yBCqBe,EAAA;;ADlBnB;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,sBAAsB;EACtB,yBAA8B;UAA9B,8BAA8B;EAC9B,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,cAAc;EACd,YCWgB;EDVhB,6BAA8C;EAC9C,sDC8lB0D;UD9lB1D,8CC8lB0D,EAAA;EDtmB9D;IAYY,yBCkEM,EAAA;;AD7DlB;EACI,iBAAiB;EACjB,kBAAkB;EAClB,YC8B0B,EAAA;;AD3B9B;EACI,iBAAiB,EAAA;EADrB;IAIQ,aAAa,EAAA;;AAIrB;EACI,cCeiB,EAAA;;ADZrB;EACI,aCUe,EAAA;;ADNnB;EAGY,iBAAiB,EAAA;;AAQ7B;EACI,iBAAiB,EAAA;;AAIrB;EACI,wCAAwC;EACxC,2BAA2B;EAC3B,yBCgDa;ED/Cb,0BAA0B;EAC1B,4BAA4B;EAC5B,WCeW;EDdX,sBAAsB,EAAA;;AAI1B,WAAA;AACA;EACI,uBAAuC;EACvC,cCec;EDdd,aAAa;EACb,kBAAkB;EAClB,MAAM;EACN,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,WAAW;EACX,QAAQ;EACR,YAAY;EACZ,eAAe;EACf,2CAAmC;UAAnC,mCAAmC;EACnC,uBAAe;EAAf,eAAe,EAAA;EAZnB;IAeM,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,WAAW,EAAA;EAhBjB;IAmBM,oBAAa;IAAb,oBAAa;IAAb,aAAa;IACb,yBAA8B;QAA9B,sBAA8B;YAA9B,8BAA8B;IAC9B,yBAAmB;QAAnB,sBAAmB;YAAnB,mBAAmB;IACnB,WAAW,EAAA;EAtBjB;IAyBM,mBAAS;QAAT,aAAS;YAAT,SAAS;IACT,YAAY;IACZ,aAAa;IACb,wBAAgB;YAAhB,gBAAgB;IAChB,6BAA6B,EAAA;EA7BnC;IAgCM,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,eAAe,EAAA;IArCrB;MAwCQ,cCGS,EAAA;;ADEf;EACE,uCAA+B;UAA/B,+BAA+B,EAAA;;AAKnC;EAEQ,kBAAkB;EAClB,gBAAgB,EAAA;EAHxB;IAKY,cCxCM,EAAA;;AD6ClB;EAEI;IACI,OAAO,EAAA;EAEX;IACI,WAAW,EAAA;EAGf;IAGQ,aAAa,EAAA;EAHrB;IAOQ,qBAAqB,EAAA,EACxB;;AAIT;EACI,sDAA+G,EAAA;;AAGnH;EACI,YC1IgB;ED2IhB,mCAA2B;UAA3B,2BAA2B;EAC3B,cC1IuB;ED2IvB,SAAS;EACT,kBAAkB,EAAA;EALtB;IAQQ,cC/ImB,EAAA;;ADmJ3B;EACI,YAAY;EACZ,WAAW;EACX,yBCzFc;ED0Fd,YAAY,EAAA;;AAGhB;EAEQ,eAAe;EACf,cC7JmB,EAAA;;AD0J3B;EAOQ,kBAAkB;EAClB,SAAS;EACT,UAAU,EAAA;;AAIlB;EAEQ,qBAAqB,EAAA;EAF7B;IAKY,yBC/GM,EAAA;;ADqHlB;EACI,cAAc;EACd,kBAAkB;EAClB,iBAAiB;EACjB,kBAAkB;EAClB,mBAAmB;EACnB,cAAc;EACd,6BAA6B;EAC7B,cC1Hc,EAAA;EDkHlB;IAWQ,YAAY,EAAA;EAXpB;IAeQ,cAAc;IACd,gBAAgB;IAChB,uBAAuB;IACvB,mBAAmB,EAAA;EAlB3B;IAsBQ,qBC5IU,EAAA;;ADiJlB;EAGY,gBAAgB,EAAA;;AAK5B;EAEQ,yBCpNgB,EAAA;;ADkNxB;EAOgB,2CClKD,EAAA;;AD2Jf;EAYY,oCCvKG,EAAA;;AD2Jf;EAiBQ,cClOwB,EAAA;EDiNhC;IAoBY,cCrOoB,EAAA;;ADiNhC;EAyBQ,2CCpLO,EAAA;;AD2Jf;EA8BY,cC/OoB,EAAA;;ADiNhC;EAqCY,cCtPoB,EAAA;;ADiNhC;EAiDY,2CC5Pc;ED6Pd,WC7MG,EAAA;;AD2Jf;;EAsDY,+BCjNG,EAAA;;ADsNf;EAEQ,mBC/RiB,EAAA;;AD6RzB;EAMQ,aAAa,EAAA;;AANrB;EAUQ,cAAc,EAAA;;AAItB;EACI;IAEQ,gBAAgB,EAAA;IAFxB;MAKY,qBAAqB;MACrB,sBAAsB,EAAA,EACzB;;AAKb;EAGI;IACI,aAAa,EAAA,EAChB;;AAGL;EAEQ,WAAW,EAAA;;AAFnB;EAKQ,gBC1TY;ED2TZ,6DAA0H,EAAA;;AAIlI;EACI;IAEQ,gBAAgB,EAAA,EACnB;;AE7VT;EAGQ,6BAA6B;EAC7B,UAAU,EAAA;;AAJlB;EAQQ,WD8EO;EC7EP,yBAAyB;EACzB,gBAAgB;EAChB,0BAA0B,EAAA;;AAIlC;EACI,yBAA6C,EAAA;;AAGjD;EAGQ,eAAe;EACf,iBAAiB;EACjB,cDImB,EAAA;;AE5B3B;EACI,SAAS;EACT,4BAA6C;EAC7C,kBAAkB;EAClB,QAAQ;EACR,6BFmFc;EElFd,cFqCkB;EEpClB,WFGkB;EEFlB,YFiCgB;EEhChB,sDFsmB0D;UEtmB1D,8CFsmB0D;EErmB1D,sBFgCY,EAAA;;AE7BhB;EACI;IACI,OAAO,EAAA,EACV;;AAIL;EAEQ,UFXuB,EAAA;;AEe/B;EAEQ,kBAAkB,EAAA;;AC5B1B;EACI,sBHqFW;EGpFX,iFAAyE;UAAzE,yEAAyE;EACzE,cAAc;EACd,eAAe;EACf,sCAA8B;EAA9B,8BAA8B;EAC9B,YH8CmB;EG7CnB,aAAa;EACb,uBAAuB;EACvB,aAA+B;EAC/B,MAAM;EACN,SAAS,EAAA;EAXb;IAcQ,yBAAoC;IACpC,YAAY;IACZ,WAAW;IACX,iBAAiB;IACjB,cHsEU;IGrEV,kBAAkB;IAClB,kBAAkB,EAAA;IApB1B;MAuBY,yBAAqC,EAAA;;AAMjD;EACI,wCHgEc;EG/Dd,kBAAkB;EAClB,OAAO;EACP,QAAQ;EACR,MAAM;EACN,SAAS;EACT,aAAa;EACb,aAAa;EACb,oCAA4B;EAA5B,4BAA4B,EAAA;;AAGhC;EAEQ,QAAQ,EAAA;;AAFhB;EAKQ,cAAc,EAAA;;AC0BlB;EDrBA;IACI,cAAc,EAAA;IADlB;MAGQ,uBAAuB,EAAA,EAC1B;;AEvDT;EACI,SAAS,EAAA;EADb;IAIQ,cAAc;IACd,WAAW,EAAA;EALnB;IASQ,aAAa,EAAA;IATrB;MAYY,aAAa,EAAA;IAZzB;MAgBY,cACJ,EAAA;EAjBR;IAqBQ,kBAAkB;IAClB,SAAS;IACT,gBAAgB;IAChB,wCAAgC;YAAhC,gCAAgC;IAChC,iCAAyB;YAAzB,yBAAyB;IACzB,+CAAuC;IAAvC,uCAAuC,EAAA;;AAK/C;EACI,YLtBkB;EKuBlB,aAAa;EACb,mBL/BgB;EKgChB,SAAS;EACT,aAAa;EACb,eAAe;EACf,SLZgB;EKahB,sDLwkB0D;UKxkB1D,8CLwkB0D,EAAA;;AKrkB9D;EACI,kBAAkB;EAClB,kBAAkB;EAClB,uCAAuC;EACvC,yBLsEa;EKrEb,4BAA4B;EAC5B,sBAAsB;EACtB,2BAA2B;EAC3B,eAAe,EAAA;EARnB;IAUQ,kBAAkB,EAAA;IAV1B;MAYY,WAAW;MACX,YAAY;MACZ,yBLmEK;MKlEL,YAAY,EAAA;IAfxB;MAkBY,kBAAkB;MAC1B,WAAW;MACX,WAAW;MACX,YAAY;MACZ,UAAU;MACV,6BAA6B;MAC7B,kBAAkB;MAClB,kBAAkB,EAAA;;AAKtB;EACI,kBL/DkB;EKgElB,gBAAgB,EAAA;EAFpB;IAKQ,yBAAyB;IACzB,gBLpDY,EAAA;;AKyDpB;EACI,qBAAqB,EAAA;EADzB;IAMgB,gCAAwB;YAAxB,wBAAwB,EAAA;EANxC;IAaY,iBAAiB;IACjB,oCAAoC;IACpC,cAAc;IACd,YAAY;IACZ,yCAAyB;IAAzB,iCAAyB;IAAzB,yBAAyB;IAAzB,gDAAyB;IACzB,eAAe,EAAA;EAlB3B;IAyBgB,cAAc;IACd,uBAAuB;IACvB,cL1GiB;IK2GjB,kBAAkB;IAClB,iBAAiB;IACjB,2BAAmB;IAAnB,mBAAmB;IACnB,gBAAgB;IAChB,kBAAkB,EAAA;IAhClC;MAmCoB,qBAAqB;MACrB,kBAAkB;MAClB,sBAAsB;MACtB,eAAe;MACf,uBAAuB;MACvB,sBAAsB;MACtB,cLtHkB;MKuHlB,2BAAmB;MAAnB,mBAAmB,EAAA;IA1CvC;MA8CoB,cL1HmB,EAAA;MK4EvC;QAiDwB,cL7He,EAAA;EK4EvC;IAuDgB,eAAe,EAAA;EAvD/B;IA2DgB,UAAU,EAAA;IA3D1B;MAgEwB,kCAAkC;MAClC,eAAe;MACf,cLhJa;MKiJb,wCAAwC,EAAA;MAnEhE;QAqE4B,iBAAiB;QACjB,oCAAoC;QACpC,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,sBAAsB;QACtB,qBAAqB,EAAA;IA3EjD;MAgFwB,UAAU,EAAA;MAhFlC;QAoFgC,gCAAgC;QAChC,eAAe,EAAA;;AAW/C;EACI,6BAA6B;EAC7B,qBAAqB;EACrB,oBAAoB;EACpB,eAAe;EACf,eAAe;EACf,yBAAyB;EACzB,cLpLkC;EKqLlC,gBLxHsB,EAAA;;AK2H1B;EACI,yBAAiD,EAAA;EADrD;IAGQ,yBAAiD;IACjD,oCAAyD,EAAA;IAJjE;MAMY,yBAAiD,EAAA;EAN7D;IAUQ,yBAAiD,EAAA;EAVzD;IAaQ,yBAAiD;IACjD,oCAAyD,EAAA;IAdjE;MAiBY,yBAAiD,EAAA;;AAK7D;EACI;IACI,aAAa,EAAA;EAGjB;IACI,yBAAyB,EAAA;EAG7B;IAEQ,cAAc,EAAA,EACjB;;AAKT;EAGQ,aAAa,EAAA;;AAHrB;EAQQ,iBLlOuB,EAAA;;AK0N/B;EAYQ,sBAA0C,EAAA;;AAZlD;EAiBY,aAAa,EAAA;;AAjBzB;EAqBY,cAAc,EAAA;;AArB1B;EA2BQ,kBAAkB;EAClB,sBAA0C;EAC1C,UAAU,EAAA;EA7BlB;;IAiCY,4BAA4B,EAAA;EAjCxC;IAqCY,wBAAwB,EAAA;EArCpC;IAyCY,oBAAoB,EAAA;EAzChC;;;IAkDgB,wBAAwB,EAAA;EAlDxC;IAsDgB,0BAA0B,EAAA;EAtD1C;IA2DoB,aAAa,EAAA;EA3DjC;IAiEoB,kBAAkB;IAClB,mBAAmB,EAAA;IAlEvC;MAqEwB,kBAAkB;MAClB,gBAAgB;MAChB,wBAAgB;MAAhB,gBAAgB;MAChB,SAAS,EAAA;MAxEjC;QA6E4B,cL3SW,EAAA;MK8NvC;QAiF4B,kBAAkB;QAClB,gBAAgB,EAAA;MAlF5C;QAsF4B,aAAa;QACb,kBAAkB,EAAA;IAvF9C;MA6F4B,kBAAkB;MAClB,yBAAgD;MAChD,yBL3TS;MK4TT,wBAAgB;MAAhB,gBAAgB,EAAA;MAhG5C;QAkGgC,eAAe,EAAA;IAlG/C;MAuG4B,cAAc;MACd,ULlUG;MKmUH,kBAAkB;MAClB,YAAY;MACZ,uBAAuB;MACvB,wDAA+C;cAA/C,gDAA+C,EAAA;MA5G3E;QA+GgC,wDAA+C;gBAA/C,gDAA+C,EAAA;MA/G/E;QAmHgC,wBAAgB;gBAAhB,gBAAgB;QAChB,iBAAiB;QACjB,kBAAkB;QAClB,YAAY;QACZ,UAAU;QACV,cLxVK;QKyVL,SAAS,EAAA;QAzHzC;UA4HoC,cL1VG,EAAA;EK8NvC;IAoIoB,cAAc;IACd,aAAa;IACb,aAAa;IACb,yBLzWA,EAAA;IKkOpB;MA4IgC,cAAc;MACd,WAAW;MACX,uBAAuB;MACvB,iBAAiB;MACjB,kBAAkB;MAClB,YAAY,EAAA;IAjJ5C;MAuJgC,kBAAkB;MAClB,WAAW;MACX,SAAS;MACT,iCAAyB;cAAzB,yBAAyB,EAAA;IA1JzD;MAiK4B,cL/SV,EAAA;;AK0TlB;EAGQ,gBAAgB,EAAA;;AAHxB;EAQQ,mBL1YiB,EAAA;;AKkYzB;EAgBoB,cLjZkB,EAAA;EKiYtC;IAmBwB,cLlZmB,EAAA;EK+X3C;IAuBwB,cLrZoB,EAAA;IK8X5C;MA0B4B,cLxZgB,EAAA;;AK8X5C;EAmC4B,cLnac,EAAA;EKgY1C;IAsCgC,cLpaY,EAAA;;AK8X5C;EAgDQ,kBAAkB,EAAA;EAhD1B;IA6DgC,mBAAyC;IACzC,cL5bY,EAAA;IK8X5C;MAgEoC,cL9bQ,EAAA;EK8X5C;IAsEoC,cLtcM,EAAA;IKgY1C;MAwEwC,cLldD,EAAA;EK0YvC;IAgFwB,uBAAuC,EAAA;EAhF/D;IA0FgC,yBAAsD,EAAA;EA1FtF;IAmGoC,yBAAiD,EAAA;EAnGrF;IAyGoC,yBAAiD,EAAA;;AAzGrF;EA0HQ,yBAAsD,EAAA;EA1H9D;IA4HY,yBAAsD;IACtD,oCAA8D,EAAA;IA7H1E;MA+HgB,yBAAsD,EAAA;EA/HtE;IAmIY,yBAAsD,EAAA;EAnIlE;IAsIY,yBAAsD;IACtD,oCAA8D,EAAA;IAvI1E;MA0IgB,yBAAsD,EAAA;;AA1ItE;EAgJQ,cL/gBmC,EAAA;;AKohB3C;EAEQ,yBAAyB,EAAA;;AAMjC;EAEQ,YLpiBiB,EAAA;;AKkiBzB;EAKQ,YLviBiB;EKwiBjB,kBAAkB,EAAA;EAN1B;;IAUY,wBAAwB,EAAA;;AAVpC;EAcQ,kBLhjBiB,EAAA;;AKkiBzB;EAiBQ,WLnjBiB,EAAA;;AKkiBzB;EAwBoB,cAAc,EAAA;;AAxBlC;EA8BwB,oBAAoB,EAAA;EA9B5C;IAgC4B,aAAa,EAAA;;AAhCzC;EAwCgC,oBAAoB,EAAA;;AAxCpD;EAkDY,iBLrlBmB,EAAA;;AKmiB/B;EAsDgB,gBAAgB,EAAA;EAtDhC;IA2DgC,qBAAqB,EAAA;;AA3DrD;EAmEY,ULtmBmB,EAAA;;AHyoB/B;;EQxBY,iBAAiB;EACjB,yCAAyB;EAAzB,iCAAyB;EAAzB,yBAAyB;EAAzB,gDAAyB,EAAA;;AR4BrC;;EQtBgB,gCAAwB;UAAxB,wBAAwB,EAAA;;AChoBxC;EACI,gBN0Cc;EMzCd,yBAA0C;EAC1C,sDNymB0D;UMzmB1D,8CNymB0D;EMxmB1D,gBNmBgB;EMlBhB,eAAe;EACf,OAAO;EACP,QAAQ;EACR,YAAY,EAAA;EARhB;IAWQ,SAAS;IACT,UAAU,EAAA;EAZlB;IAkBY,eAAe;IACf,kBAAkB;IAClB,sBAAsB;IACtB,cNpBqB,EAAA;IMDjC;MAuBgB,eAAe;MACf,QAAQ;MACR,kBAAkB,EAAA;IAzBlC;MA4BgB,cNvBwB;MMwBxB,6BAA6B,EAAA;EA7B7C;IAkCY,cNjCqB,EAAA;IMDjC;MAoCgB,cN/BwB;MMgCxB,uBAAuB,EAAA;EArCvC;IA2CgB,cNtCwB,EAAA;EMLxC;IAkDoB,cN7CoB;IM8CpB,6BAA6B,EAAA;;AFK7C;EEIA;;IAGQ,cAAc,EAAA,EACjB;;AFRL;EEaA;IAKoB,eAAe,EAAA;EALnC;IAYQ,qBAAqB;IACrB,gBAAgB,EAAA;EAbxB;IAoBgB,SAAS;IACT,WAAW,EAAA;EArB3B;IAyBY,aAAa;IACb,kCNogBoB,EAAA;IM9hBhC;MA8BoB,WAAW;MACX,mDAA2C;cAA3C,2CAA2C;MAC3C,kBAAkB,EAAA;IAhCtC;MAsCoB,kBAAkB;MAClB,iBAAiB;MACjB,UAAU;MACV,aACJ,EAAA;EA1ChB;IAgDgB,cAAc,EAAA;EAhD9B;IAsDQ,cACJ,EAAA;EAGJ;IACI,aAAa,EAAA,EAChB;;AAGL;EACI,qBAAqB,EAAA;EADzB;IAIQ,qBAAqB;IACrB,mBAAmB;IACnB,yBAAyB;IACzB,WAAW;IACX,YAAY;IACZ,qBAAqB;IACrB,UAAU;IACV,QAAQ;IACR,iBAAiB;IACjB,kDAA0C;YAA1C,0CAA0C;IAC1C,6BAAqB;YAArB,qBAAqB;IACrB,oCAA4B;IAA5B,4BAA4B;IAC5B,WAAW,EAAA;;AF/Ef;EEsFA;IAMwB,WAAW;IACX,UAAU,EAAA,EACb;;AF9FrB;EEwGA;IAEQ,cN1HS,EAAA;IMwHjB;MAIY,cN5HK,EAAA;EMwHjB;IASQ,aNlIO,EAAA;EMsIf;IACI,iBAAiB;IACjB,gBAAgB;IAChB,UAAU,EAAA;IAHd;MAMY,uBAAuB,EAAA;IANnC;MAYY,6BAA6B;MAC7B,YAAY;MACZ,wBAAgB;cAAhB,gBAAgB;MAChB,kBAAkB,EAAA;MAf9B;QAiBgB,WAAW,EAAA;QAjB3B;UAoBoB,WAAW,EAAA;IApB/B;MA0BY,kBAAkB;MAClB,6BAA6B,EAAA;MA3BzC;QA+BgB,cNpNoB,EAAA;IMqLpC;MAsCY,WAAW;MACX,kBAAkB,EAAA,EACrB;;AF1KT;EEkLA;IAGY,cNxLK,EAAA;EMqLjB;IAOY,aN7LG,EAAA;EMsLf;IAWQ,yBAAyB,EAAA;IAXjC;MAegB,+BNtKL,EAAA;MMuJX;QAkBoB,+BNzKT,EAAA;IMuJX;MAyBwB,0CAAmC,EAAA,EACtC;;AAUzB;EAGQ,aAAa,EAAA;;AAHrB;EAOQ,cAAc,EAAA;;AAOtB;EAEQ,yBC7F6B,EAAA;;AD2FrC;EAOgB,2CNhND,EAAA;;AMyMf;EAYY,oCNrNG,EAAA;;AMyMf;EAmBY,+BN5NG,EAAA;;AMyMf;EAyBQ,+BNlOO,EAAA;EMyMf;IA4BY,WNrOG,EAAA;;AMyMf;EAiCQ,2CN1OO,EAAA;;AMyMf;EAsCY,+BN/OG,EAAA;;AMyMf;EA2CQ,aAAa,EAAA;;AA3CrB;EA+CQ,cAAc,EAAA;;AA/CtB;EAqDY,2CN9Sc;EM+Sd,WN/PG,EAAA;;AMyMf;;EA0DY,+BNnQG,EAAA;;AQtFf;EACI,yBR8DyB,EAAA;EQ/D7B;IAGQ,yBRke2B;IQje3B,iBR0DsB;IQzDtB,cAAc;IACd,sDRymBsD;YQzmBtD,8CRymBsD,EAAA;EQ/mB9D;IAUQ,iBRoDsB;IQnDtB,cAAc,EAAA;EAXtB;IAeQ,cAAc;IACd,+BAA2D,EAAA;EAhBnE;IAqBY,8BAAqE,EAAA;;AAQjF;EAEQ,eAAe,EAAA;;AAFvB;EAKQ,iBR4BsB,EAAA;;ASjE9B;;;;;;uDZ82BuD;AYv2BtD;EACG,kBAAkB;EAClB,eAAe;EACf,qBAAqB;EACrB,gBAAgB;EAChB,yBAAyB;EACzB,sBAAsB;EACtB,qBAAqB;EACrB,iBAAiB;EACjB,wCAAwC,EAAA;;AAE1C;EACE,kBAAkB;EAClB,kBAAkB;EAClB,YAAY;EACZ,aAAa;EACb,iBAAiB;EACjB,kBAAkB;EAClB,UAAU;EACV,8BAA8B;EAI9B,qJAAqJ;EACrJ,qCAAqC;EAGrC,6BAA6B;EAC7B,uDAAuD;EAGvD,uDAAuC;EAAvC,+CAAuC;EAAvC,uCAAuC;EAAvC,0DAAuC;EACvC,2CAA2C;EAI3C,mCAAmC;EACnC,oBAAoB,EAAA;;AAEtB;EACE,oCAAoC;EAIpC,6KAA6K,EAAA;;AAE/K;EACE,8BAA8B,EAAA;;AAEhC;EACE,oCAAoC,EAAA;;AAEtC;EACE,mCAAmC;EAGnC,2BAA2B,EAAA;;AAE7B;;EAEE,gCAAgC;EAIhC,wBAAwB;EACxB,2EAA2E,EAAA;;AAE7E;;;;EAIE,mBAAmB;EACnB,sBAAsB;EACtB,eAAe;EACf,YAAY;EACZ,aAAa;EACb,cAAc;EACd,kCAAkC;EAClC,cAAc;EACd,gBAAgB;EAChB,kBAAkB;EAClB,qBAAqB;EACrB,UAAU,EAAA;;AAEZ;EACE,qBAAqB;EACrB,oBAAoB,EAAA;;AAEtB;EACE,SAAS;EACT,qBAAqB,EAAA;;AAEvB;EACE,oBAAoB;EACpB,sBAAsB,EAAA;;AAExB;EACE,UAAU,EAAA;;AAEZ;EACE,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,UAAU,EAAA;;AAEZ;EACE,kBAAkB;EAClB,YAAY;EACZ,aAAa;EACb,kBAAkB;EAClB,kBAAkB,EAAA;;AAEpB;EACE,wBAAwB;EACxB,yDAAyD;EACzD,iDAAiD;EACjD,6BAA6B;EAG7B,qBAAqB,EAAA;;AAEvB;EACE,uDAAuD;EACvD,+CAA+C,EAAA;;AAEjD;EACE,cAAc,EAAA;;AAGlB;EAEQ,0CTjDO,EAAA;;ASqDf;EAEQ,wCTzBS,EAAA;;AS4BjB;EAEQ,yCTvBS,EAAA;;AS0BjB;EAEQ,yCT1BS,EAAA;;AS6BjB;EAEQ,yCTlCS,EAAA;;ASqCjB;EAEQ,wCTzCS,EAAA;;AUvHjB;EACE,YAAY;EACZ,WAAW,EAAA;;AAGb;EACE,cAAc;EACd,aAAa,EAAA;;AAGf;EACE,cAAc;EACd,aAAa,EAAA;;AAGf;EACE,YAAY;EACZ,WAAW,EAAA;;AAGb;EACE,cAAc;EACd,aAAa,EAAA;;AAGf;EAEE,WAAW;EACX,YAAY,EAAA;;AAId;EACE,yBAAmB;MAAnB,sBAAmB;UAAnB,mBAAmB;EACnB,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,YAAY;EACZ,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB;EACvB,WAAW,EAAA;;ACrCb;EAGY,kBAAkB,EAAA;;AAH9B;EAWoB,iBAAiB,EAAA;;AAXrC;EAkBQ,kBAAkB,EAAA;;AAI1B;EAEQ,yBX+DU;EW9DV,wBAAgB;UAAhB,gBAAgB,EAAA;;AAHxB;EAMQ,kBAAkB;EAClB,kBAAkB,EAAA;EAP1B;IAUY,kBAAkB;IAClB,qBAAqB;IACrB,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,eAAe;IACf,yBX6EK;IW5EL,WX8CG;IW7CH,kBAAkB;IAClB,kBAAkB;IAClB,UAAU;IACV,QAAQ;IACR,mCAA2B;YAA3B,2BAA2B,EAAA;;AAtBvC;EA8BoB,iBAAiB,EAAA;;ACpDrC;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAG9B;EACI,0BAA0B,EAAA;;AAM9B;EACI,oBAAa;EAAb,oBAAa;EAAb,aAAa;EACb,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB,EAAA;;AAGzB;EACE,mBAAO;MAAP,WAAO;UAAP,OAAO,EAAA;;AAQX;EACI,YAAY;EACZ,WAAW;EACX,6BAA6B;EAC7B,cAAc;EACd,yBZoBc;EYnBd,kBAAkB;EAClB,cZkBc;EYjBd,kBAAkB;EAClB,4BAAoB;EAApB,oBAAoB,EAAA;EATxB;IAYQ,cZcU;IYbV,yBZSU,EAAA;;AYJlB;EACI,eAAe,EAAA;;AAGnB;EACI,eAAe,EAAA;;AAGnB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAGpB;EACI,gBAAgB,EAAA;;AAKpB;EACI,kBAAkB;EAClB,YAAY;EACZ,WAAW;EACX,QAAQ;EACR,SAAS;EACT,OAAO;EACP,MAAM;EACN,YAAY;EACZ,sBZnBW,EAAA;;AYwBf;EACI,mBAAO;MAAP,WAAO;UAAP,OAAO,EAAA;;AAOX;EAEQ,eAAe;EACf,wBZw3CmC;EYv3CnC,2WAA+F,EAAA;;ACnIvG;EACI,eAAe;EACf,MAAM;EACN,OAAO;EACP,QAAQ;EACR,SAAS;EACT,sBb+EW;Ea9EX,aAAa,EAAA;;AAGjB;EACI,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,SAAS;EACT,QAAQ;EACR,uBAAuB,EAAA;;AAG3B;EACI,cAAc;EACd,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,0DAAkD;UAAlD,kDAAkD,EAAA;;AAGtD;EACI,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,OAAO;EACP,MAAM;EACN,2DAAmD;UAAnD,mDAAmD,EAAA;EANvD;IAQQ,WAAW;IACX,cAAc;IACd,UAAU;IACV,WAAW;IACX,yBb4ES;Ia3ET,mBAAmB;IACnB,kEAA0D;YAA1D,0DAA0D,EAAA;EAdlE;IAkBQ,8BAAsB;YAAtB,sBAAsB,EAAA;IAlB9B;MAoBY,8BAAsB;cAAtB,sBAAsB,EAAA;EApBlC;IAwBQ,8BAAsB;YAAtB,sBAAsB,EAAA;IAxB9B;MA0BY,8BAAsB;cAAtB,sBAAsB,EAAA;EA1BlC;IA8BQ,8BAAsB;YAAtB,sBAAsB,EAAA;IA9B9B;MAgCY,8BAAsB;cAAtB,sBAAsB,EAAA;EAhClC;IAoCQ,8BAAsB;YAAtB,sBAAsB,EAAA;IApC9B;MAsCY,8BAAsB;cAAtB,sBAAsB,EAAA;EAtClC;IA0CQ,8BAAsB;YAAtB,sBAAsB,EAAA;IA1C9B;MA4CY,8BAAsB;cAAtB,sBAAsB,EAAA;EA5ClC;IAgDQ,8BAAsB;YAAtB,sBAAsB,EAAA;IAhD9B;MAkDY,8BAAsB;cAAtB,sBAAsB,EAAA;;AAKlC;EACI;IACI,iCAAyB;YAAzB,yBAAyB,EAAA,EAAA;;AAFjC;EACI;IACI,iCAAyB;YAAzB,yBAAyB,EAAA,EAAA;;AAIjC;EACI;IACI,iCAAyB;YAAzB,yBAAyB,EAAA,EAAA;;AAFjC;EACI;IACI,iCAAyB;YAAzB,yBAAyB,EAAA,EAAA;;AAIjC;EACI;IACI,6BAAqB;YAArB,qBAAqB,EAAA;EAEzB;IACI,2BAAqB;YAArB,mBAAqB,EAAA,EAAA;;AAL7B;EACI;IACI,6BAAqB;YAArB,qBAAqB,EAAA;EAEzB;IACI,2BAAqB;YAArB,mBAAqB,EAAA,EAAA;;ACjG7B;EACE,eAAe;EACf,qBAAqB;EACrB,oBd08BsE,EAAA;Ec78BxE;IAKI,YAAY;IACZ,cAAc;IACd,oBAA4C,EAAA;EAPhD;IAUI,cAAc,EAAA;;AAIlB;EACE,kBAAkB;EAClB,gBAAgB,EAAA;;AAIlB;EACE,eAAe;EACf,gBAAgB,EAAA;;ACrBlB;EACI,6BfoFc,EAAA;;AejFlB;EACI,gCfgFc,EAAA;;Ae7ElB;EACI,mBfUkB,EAAA;;AePtB;EACI,8BfwEc,EAAA;;AetElB;EACI;IACI,iBAAiB,EAAA,EACpB;;AAGL;EACI,aAAa,EAAA;EADjB;IAGQ,oCAAqC;IACrC,sBAAsB;IACtB,uBAAuB;IACvB,6BAA6B;IAC7B,WAAW;IACX,YAAY,EAAA;;AAIpB;EAEQ,gBAAgB;EAChB,kBAAkB,EAAA;EAH1B;IAKY,WAAW;IACX,kBAAkB;IAClB,UAAU;IACV,YAAY;IACZ,0CfwCG;IevCH,UAAU;IACV,gCAAwB;YAAxB,wBAAwB;IACxB,SAAS;IACT,4BAAoB;IAApB,oBAAoB,EAAA;EAbhC;IAiBY,WAAW;IACX,WAAW;IACX,4BAAoB;IAApB,oBAAoB,EAAA;;AAnBhC;EA0BgB,UAAU,EAAA;;AAQ1B;EAGY,cfmBM;EelBN,cAAc;EACd,iBAAiB;EACjB,gCfWM,EAAA;;AejBlB;EAWgB,gBAAgB,EAAA;;AAXhC;EAiBgB,kBAAkB,EAAA;;AASlC;EAEQ,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,+BfbU;EecV,SAAS;EACT,SAAS,EAAA;;AAIjB;EACI,iBAAiB,EAAA;EADrB;IAKQ,kBAAkB;IAClB,sBAAsB,EAAA;IAN9B;MASY,kBAAkB;MAClB,WAAW;MACX,QAAQ;MACR,UAAU,EAAA;IAZtB;MAgBY,mBAAmB,EAAA;;AC3H/B;EACI,iBAAiB;EACjB,oBAAoB,EAAA;EAFxB;IAKQ,mBAAmB;IACnB,gBAAgB,EAAA;;AAMxB;EACI,iBAAiB,EAAA;;AAKrB;EACI,kBAAkB;EAClB,SAAS;EACT,WAAW;EACX,YAAY;EACZ,UAAU;EACV,UAAU;EACV,cAAc,EAAA;;AAMlB;EACE,kBAAkB;EAClB,chByDgB,EAAA;EgB3DlB;IAKI,cAAc;IACd,eAAe;IACf,chBqDc;IgBpDd,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,gBAAgB;IAChB,mBAAmB;IACnB,kBAAkB;IAClB,yBhB0Cc;IgBzCd,4BAAoB;IAApB,oBAAoB,EAAA;EAfxB;IAmBI,gBAAgB,EAAA;IAnBpB;MAuBQ,yBhB6DS;MgB5DT,WhB8BO,EAAA;;AgBrBf;EAEQ,yBhBoBU;EgBnBV,gBAAgB;EAChB,gBAAgB;EAChB,gBhBJgB;EgBKhB,kBAAkB,EAAA;;AAO1B;EACE,sBhBOa;EgBNb,yBhBSgB;EgBRhB,sBhBqhBkC;EgBphBlC,aAAa;EACb,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB,EAAA;EAPrB;IAUI,eAAe,EAAA;;AAInB;EACE,cAAc,EAAA;;AAIhB;EACE,aAAa,EAAA;EADf;IAGI,gCAAiC,EAAA;;AAIrC;EAEM,gBAAgB,EAAA;EAFtB;IAIU,WAAW;IACX,YAAY;IACZ,4BAA4B;IAC5B,0BAA0B;IAC1B,6BAA6B;IAC7B,oDAAiD;IACjD,yBAA0B;IAC1B,0BAA0B,EAAA;;AClHpC;EACI;;;;;IAKI,wBAAwB,EAAA;EAE5B;;;;;IAKI,UAAU;IACV,SAAS,EAAA;EAGb;IACI,SAAS,EAAA,EACZ;;ApBs5CL;EqB76CE,kBAAkB;EAClB,4BAAsB;EAAtB,6BAAsB;MAAtB,0BAAsB;UAAtB,sBAAsB;EACtB,mBAAe;MAAf,eAAe;EACf,uBAA2B;MAA3B,oBAA2B;UAA3B,2BAA2B;EAC3B,yBAAyB;MAAzB,yBAAyB;EACzB,wBAAuB;MAAvB,qBAAuB;UAAvB,uBAAuB,EAAA;;AAGzB;EACE,gBAAgB;EAChB,cAAc;EACd,eAAe;EACf,kBAAkB;EAClB,mBAAmB,EAAA;;AAGrB;EACE,kBAAkB;EAClB,kBAAkB;EAClB,gBAAgB;EAChB,UAAU;EACV,SAAS;EACT,OAAO;EACP,MAAM;EACN,SAAS;EACT,QAAQ;EACR,sBAAsB;EACtB,uBAAuB;EACvB,UAAU,EAAA;;AAGZ;EACE,6BAA6B;EAC7B,sCAA8B;UAA9B,8BAA8B;EAC9B,uBAAuB;EACvB,kBAAkB;EAClB,MAAM;EACN,kBAAkB;EAClB,SAAS;EACT,mBAAmB;EACnB,UAAU;EACV,SAAS;EACT,iCAAiC,EAAA;;AAGnC;EACE,kBAAkB;EAClB,yCAAiC;UAAjC,iCAAiC;EACjC,kBAAkB;EAClB,cAAc;EACd,YAAY;EAAE,mGAAA;EACd,WAAW;EACX,mBAAmB;EACnB,cAAc;EAAE,mFAAA;EAChB,eAAe;EAAE,kDAAA;EACjB,gBAAgB;EAAE,0CAAA;EAClB,qBAAqB;EACrB,uBAAuB,EAAA;;AAGzB;;EAEE,aAAa,EAAA;;AAGf;;EAEE,YAAY;EACZ,cAAc,EAAA;;AAGhB;EACE,gBAAgB;EAChB,eAAe;EACf,WAAW;EACX,oBAAoB,EAAA;;AAGtB;EACE,sCAA8B;UAA9B,8BAA8B;EAC9B,YAAY;EACZ,WAAW;EACX,cAAc;EACd,kBAAkB;EAClB,WAAW;EACX,eAAe;EACf,gBAAgB;EAChB,WAAW;EACX,UAAU;EACV,SAAS;EACT,oBAAoB;EACpB,yBAAkB;MAAlB,0BAAkB;UAAlB,kBAAkB;EAClB,oBAAc;MAAd,cAAc;EACd,0BAAa;MAAb,aAAa,EAAA;;AAGf;EACE,2BAAmB;UAAnB,mBAAmB;EACnB,cAAc;EACd,UAAU;EACV,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,aAAa;EACb,YAAY;EACZ,eAAe;EACf,cAAc;EACd,gBAAgB;EAChB,oBAAoB;EACpB,WAAW,EAAA;;AAGb;EACE,UAAU;EACV,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,oBAAoB;EACpB,gBAAgB,EAAA;;ArB06ClB;EqBt6CE,oBAAoB;EACpB,sBAAiB;GAAjB,qBAAiB;OAAjB,iBAAiB;EACjB,yBAAyB,EAAA;;ArBy6C3B;EqBr6CE,mBAAmB,EAAA;;AAGrB;EACE,kBAAkB;EAClB,UAAU;EACV,UAAU;EACV,gBAAgB,EAAA;;AAGlB;EACE,kBAAkB;EAClB,WAAW;EACX,mBAAmB;EACnB,kBAAkB;EAClB,OAAO;EACP,QAAQ;EACR,UAAU;EACV,uCAA+B;EAA/B,+BAA+B,EAAA;;AAGjC;EACE,0DAAA;EACA,YAAY;EACZ,qCAA6B;EAA7B,6BAA6B,EAAA;;AAG/B;EACE,MAAM;EACN,WAAW,EAAA;;AAGb;EACE,QAAQ;EACR,WAAW,EAAA;;AAGb;EACE,OAAO;EACP,YAAY,EAAA;;AAGd;EACE,YAAY;EACZ,SAAS;EACT,UAAU,EAAA;;AAGZ;EACE,WAAW;EACX,OAAO;EACP,QAAQ;EACR,WAAW;EACX,aAAa;EACb,eAAe;EACf,WAAW,EAAA;;AAGb,gBAAA;ArB85CA;EqB55CE,WAAW;EACX,OAAO,EAAA;;AAGT;EACE,cAAc;EACd,eAAe;EACf,UAAU;EACV,kBAAkB;EAClB,aAAa;EACb,YAAY;EACZ,kBAAkB;EAClB,kBAAkB,EAAA;;AAGpB;EACE,eAAe;EACf,OAAO;EACP,kBAAkB;EAClB,kBAAkB;EAClB,qBAAqB,EAAA;;AAGvB;EACE,YAAY,EAAA;;ACjNd;EAEM,eAAe;EACf,iBAAiB;EACjB,yBAAyB,EAAA;;AAI/B;EAEM,mBnB6EY;EmB5EZ,eAAe;EACf,iBAAiB;EACjB,eAAe;EACf,yBAAyB;EACzB,gBnBoDoB,EAAA;;AmBhD1B;;;;;;;;;;EAWM,qBnByDY,EAAA;;AmBpElB;EAcM,mBAAkC,EAAA;;AAIxC;EACE,gBnBgDa;EmB/Cb,qBnBiDgB;EmBhDhB,cnBqDgB;EmBpDhB,0BAA0B;EAC1B,wBAAgB;UAAhB,gBAAgB;EAChB,4BAA4B;EAC5B,uBAAuB,EAAA;;AAGzB;;;EAGE,yBnBkEe;EmBjEf,WnBmCa;EmBlCb,iBAAiB,EAAA;;AAGnB;EACE,kBAAkB;EAClB,YAAY;EACZ,YAAY;EACZ,oBAAoB;EACpB,eAAe;EACf,gBAAgB;EAChB,kBAAkB,EAAA;;AAGpB;EACE,2BAA0B;EAC1B,iBAAiB,EAAA;;AAGnB;EACE,yBnB6Ce,EAAA;;AmB1CjB;EACE,WnBWa,EAAA;;AmBRf;EAGM,qBnBOY,EAAA;;AmBFd;EARJ;IASM,cAAc,EAAA,EAkCjB;;AA3CH;EAaU,eAAe;EACf,iBAAiB;EACjB,yBAAyB,EAAA;;AAG7B;EAlBN;;;IAuBc,WAAW;IACX,cAAc;IACd,kBAAkB;IAClB,WAAW;IACX,cAAc,EAAA;EA3B5B;IA+Bc,WAAW,EAAA;EA/BzB;IAmCc,aAAa,EAAA,EAChB;;AApCX;EAwCU,0BAA0B,EAAA;;AAKpC;EACE,yBnBnCgB,EAAA;;AmBsClB;EACE,iCAAmC,EAAA;;AtB+kDrC;EsB1kDE,yBAAyB,EAAA;;AtB6kD3B;EsBzkDE,kBAAkB,EAAA;;ACxIpB;EACE,sBpBoFa;EoBnFb,aAAa,EAAA;EAFf;IAII,qBAAqB;IACnB,oBAAoB;IACpB,oBAAoB;IACpB,gBAAgB;IAChB,cpBqFY,EAAA;IoB7FlB;MAWQ,yBpB2EU,EAAA;IoBtFlB;MAeQ,yBpB2GS;MoB1GT,gBAAgB;MAChB,eAAe,EAAA;;AAKvB;EACE,+BpBgEgB,EAAA;;AoB7DlB;EACE,sBpB0Da;EoBzDb,gCAA4C;EAC5C,cpB+DgB,EAAA;EoBlElB;IAKI,aAAa,EAAA;;AvB+sDjB;EuBvsDI,cAAc,EAAA;;AvB0sDlB;EuBrsDM,qCAAoC;EACpC,wCAAuC;EACvC,sCAAqC;EACrC,yCAAwC,EAAA;;AvBwsD9C;EuBnsDI,yBpBqCc;EoBpCd,cAAc;EACd,yBAAyB;EACzB,4BAA4B;EAC5B,gCpB6iBgC;EoB5iBhC,mCpB4iBgC,EAAA;;AqBtmBpC;EAEQ,aAAa,EAAA;;AAFrB;EAMQ,crBkHS;EqBjHT,gBrB2DgB,EAAA;;AqBlExB;EAWQ,sBrB2EO;EqB1EP,crB4GS;EqB3GT,wBAAgB;UAAhB,gBAAgB,EAAA;;ACbxB;EACI,sCtBwpBoF,EAAA;;AsBrpBxF;;;;EAMQ,8BAA+B;EAC/B,eAAe,EAAA;;AAPvB;;;EAcY,aAAa,EAAA;;AAdzB;EAmBQ,mBtBkEU;EsBjEV,qBtBiEU,EAAA;;AsBrFlB;EAwBQ,eAAe;EACf,ctB8DU,EAAA;;AsBvFlB;;EA8BQ,ctByDU;EsBxDV,mBtBsDU;EsBrDV,eAAe,EAAA;;AAhCvB;EAoCQ,yBtB4ES;EsB3ET,WAAW;EACX,YAAY;EACZ,SAAS;EACT,iCAAqC,EAAA;;AC3C7C;EAEI,eAAe;EACf,gBvB8DoB,EAAA;;AuB1DxB;EACE,eAAe,EAAA;;AAGjB;EAEI,qBvB+Ga;EuB9Gb,cvB8Ga,EAAA;;AuBjHjB;EAOM,yBvBwGW,EAAA;;AuB/GjB;EAWM,qCvBoGW,EAAA;;AuB/GjB;EAeI,qBvB+Fa;EuB9Fb,cvB8Fa,EAAA;;AuB1FjB;EAEI,wBAAgB;UAAhB,gBAAgB,EAAA;;AAIpB;EAEI,mBvB4Ea,EAAA;EuB9EjB;IAIM,mBvB0EW,EAAA;IuB9EjB;MAMQ,kCvBwES,EAAA;;AuB9EjB;EAYI,mBvBkEa,EAAA;;AuB9DjB;EACE,qDAAuD,EAAA;;ACtDzD;EACE,kBxBoFa,EAAA;;AwBjFf;EACE,eAAe,EAAA;;AAGjB;EACE,QAAQ,EAAA;;AAGV;EAEI,qBAAqB;EACrB,sBAAsB,EAAA;EAH1B;IAMM,gBAAgB,EAAA;;AClBtB;EACE,czBsHe,EAAA;;AyBnHjB;EACE,qBzBkHe,EAAA;;AyB/GjB;EACE,aAAa;EACb,SAAS;EACT,UAAU,EAAA;EAHZ;IAKI,cAAc,EAAA;EALlB;IAQI,eAAe;IACf,gBAAgB;IAChB,czBqGa;IyBpGb,eAAe,EAAA;;ACnBnB;EACE,cAAc,EAAA;EADhB;IAGI,sB1BkFW;I0BjFX,yB1BqFc;I0BpFd,YAAY,EAAA;IALhB;MAOM,aAAa,EAAA;IAPnB;MAWM,iBAAiB;MACjB,kBAAkB;MAClB,c1B+EY,EAAA;I0B5FlB;MAiBM,YAAY;MACZ,WAAW;MACX,UAAU,EAAA;MAnBhB;QAsBQ,yDAA2D;QAC3D,2BAA2B,EAAA;IAvBnC;MA4BM,c1BgEY,EAAA;;A0B3DlB;EAMQ,oEAAsE;EACtE,sCAAsC,EAAA;;AAM9C;EAEM,aAAa;EACb,sB1BoCS,EAAA;E0BvCf;IAKU,yB1BsCQ;I0BrCR,sB1BiCK;I0BhCL,c1BsCQ;I0BrCR,aAAa,EAAA;;AARvB;EAYM,yB1ByDW,EAAA;;A0BrEjB;EAeM,yB1ByBY;E0BxBZ,c1BwqCmD,EAAA;E0BxrCzD;IAkBU,yB1BmDO;I0BlDP,W1BoBK,EAAA;;A0Bff;EACE,iBAAiB,EAAA;;AAGnB;EACE,qC1BoBa;E0BnBb,sB1BSa;E0BRb,sD1BiiB4D;U0BjiB5D,8C1BiiB4D,EAAA;;A0B9hB9D;EAEI,yB1BMc,EAAA;;A0BFlB;EAEI,gBAAgB;EAChB,sB1BJW;E0BKX,oCAAgD,EAAA;EAJpD;IAOM,iBAAiB,EAAA;EAPvB;IAUM,SAAS;IACT,c1BLY,EAAA;I0BNlB;MAaU,c1BPQ,EAAA;I0BNlB;MAaU,c1BPQ,EAAA;I0BNlB;MAaU,c1BPQ,EAAA;I0BNlB;MAaU,c1BPQ,EAAA;I0BNlB;MAaU,c1BPQ,EAAA;E0BNlB;IAiBM,yB1BhBY;I0BiBZ,yB1BhBY;I0BiBZ,kBAAkB;IAClB,cAAc,EAAA;;AAKpB;EAGM,qB1BzBY,EAAA;;A0BsBlB;EAQI,gB1BrDsB,EAAA;;A0B2D1B;EACI,WAAW;EACX,WAAW;EACX,kBAAkB,EAAA;EAHtB;IAKI,WAAW;IACX,YAAY;IACZ,kBAAkB,EAAA;;AAItB;EACE,eAAe,EAAA;;AAGjB;;;EAGE,qBAAqB;EACrB,eAAe;EACf,iBAAiB;EACjB,c1BxDgB,EAAA;E0BkDlB;;;IASI,iBAAiB,EAAA;IATrB;;;MAaQ,gBAAgB;MAChB,kCAAkC,EAAA;;AAM1C;;;EAIE,+B1B/Ea,EAAA;;A0BmFf;EACE,gBAAgB,EAAA;;AAMlB;EACE,iBAAiB;EACjB,YAAY;EACZ,WAAW,EAAA;;ACnLb,eAAA;AACA;EACE,aAAa,EAAA;EADf;IAGI,cAAc;IACd,cAAc;IACd,WAAW;IACX,YAAY;IACZ,yB3BkFc;I2BjFd,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,gB3BkDoB;I2BjDpB,wCAAgC;IAAhC,gCAAgC,EAAA;IAhBpC;MAkBM,c3B2EY;M2B1EZ,6BAA6B;MAC7B,cAAc;MACd,oBAAoB;MACpB,gBAAgB;MAChB,eAAe;MACf,iBAAiB;MACjB,kBAAkB;MAClB,UAAU;MACV,WAAW;MACX,SAAS;MACT,kBAAkB;MAClB,qBAAqB;MACrB,gBAAgB;MAChB,wCAAgC;MAAhC,gCAAgC,EAAA;IAhCtC;MAoCM,WAAW;MACX,kBAAkB;MAClB,SAAS;MACT,yB3BgDY;M2B/CZ,wBAAgB;cAAhB,gBAAgB;MAChB,mBAAmB;MACnB,YAAY;MACZ,WAAW;MACX,QAAQ;MACR,wCAAgC;MAAhC,gCAAgC,EAAA;EA7CtC;IAkDI,yB3BiEa,EAAA;;A2B7DjB;EACE,yB3B4De,EAAA;E2B7DjB;IAGI,W3B4BW;I2B3BX,4BAA4B;IAC5B,WAAW;IACX,SAAS,EAAA;EANb;IAUI,UAAU;IACV,yB3BsBc,EAAA;;A2BlBlB;EACE,yB3BiDe,EAAA;;A2B/CjB;;EAEE,W3BWa,EAAA;;A2BRf;EACE,yB3B4Ce,EAAA;;A2BzCjB;EACE,yBAAyB,EAAA;;AAG3B;EACE,yB3B6Be,EAAA;;A2B1BjB;EACE,yB3BgCe,EAAA;;A2B7BjB;EACE,yB3B8Be,EAAA;;A2B3BjB;EACE,yB3BuBe,EAAA;;A2BpBjB;EACE,yB3BiBe,EAAA;;A2BdjB;EACE,yB3BbgB,EAAA;;A2BgBlB;EACE,iBAAiB,EAAA;EADnB;IAGI,kBAAkB,EAAA;;AChHtB;EACE,yB5BqFgB;E4BpFhB,YAAY;EACZ,uBAAuB,EAAA;EAHzB;IAOQ,gBAAgB,EAAA;EAPxB;IAaU,oCAAqC;IACrC,sBAAsB;IACtB,wBAAgB;YAAhB,gBAAgB;IAChB,sBAAwB,EAAA;EAhBlC;;;IAuBY,mB5BgEM,EAAA;E4BvFlB;;;IA8BY,c5B4DM;I4B3DN,YAAY,EAAA;EA/BxB;IAmCY,yB5BqDM,EAAA;;A4B9ClB;EAEI,YAAY,EAAA;;AC3ChB;EAIQ,0BAA0B;EAC1B,6BAA6B,EAAA;;AALrC;EAaU,yBAAyB;EACzB,4BAA4B,EAAA;;AAOpC;EAEI,mBAAmB;EACrB,qBAAqB,EAAA;;AAHvB;EAMI,uCAAuC;EACvC,wCAAwC;EACxC,oCAAoC;EACpC,uCAAuC,EAAA;;AAT3C;EAYI,qCAAqC;EACvC,0CAA0C;EAC1C,oCAAoC;EACpC,uCAAuC,EAAA;;ACvC3C;EACE,yB9BwFgB,EAAA;;A8BpFlB;EAEI,iBAAiB,EAAA;EAFrB;IAIM,kBAAkB;IAClB,eAAe,EAAA;;ACPrB;EACI,oCAAsC,EAAA;;AAG1C;EAEQ,wCAA0C,EAAA;;AAFlD;;;EAQQ,iCAAqC;EACrC,2BAA2B,EAAA;;AATnC;EAaQ,yBAA2B,EAAA;EAbnC;IAgBY,oCAAsC,EAAA;;AAhBlD;EAsBY,oCAAsC,EAAA;;AAtBlD;EA2BQ,gCAAkC,EAAA;;AA3B1C;;;EAiCQ,8BAAgC,EAAA;;AAjCxC;EAqCQ,yBAA2B,EAAA;EArCnC;IAwCY,wBAA0B,EAAA;;AAxCtC;EA6CQ,iCAAqC,EAAA;;AA7C7C;;;EAmDQ,yBAA2B,EAAA;;AAnDnC;EAuDQ,0CAAwD,EAAA;;AAGhE;EACI,wBAAwB,EAAA;;AChE5B,aAAA;AACA;EACE,iBAAiB;EACjB,0BhCsFgB;EgCrFhB,gBhCiFa;EgChFb,kBAAkB,EAAA;EAJpB;IAOI,eAAe;IACf,WAAW,EAAA;;ACTf;EAGQ,kBAAkB,EAAA;EAH1B;IASY,WAAW;IACX,YAAY;IACZ,WAAW;IACX,kCjCuGK;IiCtGL,kBAAkB;IAClB,SAAS;IACT,kBAAkB,EAAA;EAf9B;IAsBY,qBAAqB;IACrB,mBAAmB;IACnB,gBAAgB;IAChB,YAAY;IACZ,iBAAiB;IACjB,cjCwFK;IiCvFL,kBAAkB;IAClB,kBAAkB;IAClB,wCjCqFK,EAAA;IiCnFL;MAhCZ;QAiCgB,cAAc;QACd,6BAA6B;QAC7B,YAAY,EAAA,EAEnB;EArCT;IAyCgB,cAAc;IACd,eAAe;IACf,gBjC2nBiB,EAAA;IiCznBjB;MA7ChB;QA8CoB,aAAa,EAAA,EAEpB;EAhDb;IAmDgB,6BAA6B;IAC7B,cjCwCE,EAAA;IiC5FlB;MAuDoB,yBjC4DH;MiC3DG,WjC6BL,EAAA;;AiCrFf;EA+DQ,iBAAiB;EACjB,eAAe;EACf,gBAAgB;EAChB,gBAAgB,EAAA;EAlExB;IAqEY,qBAAqB,EAAA;IArEjC;MAwEgB,qBAAqB;MACrB,sBAAsB;MACtB,yBjCyCC;MiCxCD,WjCUD;MiCTC,qBAAqB,EAAA;IA5ErC;MAiFoB,mBAAmB;MACnB,yBAAuC,EAAA;IAlF3D;MAuFgB,YAAY,EAAA;;AAM5B;EACI,iBAAiB;EACjB,iBAAiB,EAAA;;AAIrB;EACI;IAIgB,kCAAkC,EAAA,EACrC;;ACzGjB;EAEI,cAAc,EAAA;;AAFlB;EAKI,uBAAuB,EAAA;;AAL3B;EASM,yBlCkFY;EkCjFZ,clC4EY;EkC3EZ,yBlCgFY,EAAA;EkC3FlB;IAaU,yBlCsGO;IkCrGP,qBlCqGO;IkCpGP,WlCsEK;IkCrEL,oDlCmGO;YkCnGP,4ClCmGO,EAAA;;AkCnHjB;EAoBM,YAAY,EAAA;EApBlB;IAsBQ,QAAQ;IACR,kCAA0B;YAA1B,0BAA0B;IAC1B,oBAAoB,EAAA;;AAxB5B;EA8BM,eAAe;EACf,mBAAmB,EAAA;;AA/BzB;EAoCI,kBAAkB;EAClB,yBAAiC,EAAA;EArCrC;IAwCM,oCAAmD,EAAA;EAxCzD;IA4CM,qBAAqB;IACrB,iBAAiB;IACjB,kBAAkB,EAAA;IA9CxB;MAgDQ,+BAA+B;MAC/B,oCAAoC;MACpC,sBlCmCO;MkClCP,kBAAkB;MAClB,yBlCoCU;MkCnCV,WAAW;MACX,qBAAqB;MACrB,YAAY;MACZ,OAAO;MACP,kBAAkB;MAClB,kBAAkB;MAClB,4BAA4B;MAC5B,WAAW;MACX,wBAAwB,EAAA;IA7DhC;MAgEQ,clCuBU;MkCtBV,qBAAqB;MACrB,eAAe;MACf,YAAY;MACZ,OAAO;MACP,kBAAkB;MAClB,iBAAiB;MACjB,gBAAgB;MAChB,kBAAkB;MAClB,SAAS;MACT,WAAW,EAAA;EA1EnB;IA8EM,eAAe;IACf,UAAU;IACV,UAAU;IACV,wBAAwB,EAAA;IAjF9B;MAoFQ,aAAa,EAAA;EApFrB;IAyFQ,oBAAoB;IACpB,aAAa,EAAA;EA1FrB;IA+FQ,gBAAgB;IAChB,kCAAkC;IAClC,gBAAgB,EAAA;EAjGxB;IAsGQ,yBlChBU;IkCiBV,mBAAmB,EAAA;EAvG3B;IA4GQ,yBlCOS;IkCNT,qBlCMS,EAAA;EkCnHjB;IAgHQ,WlC3BO,EAAA;;AkCrFf;EAuHM,oBAA8B;EAC9B,yBlCLW,EAAA;EkCnHjB;IA0HQ,WlCrCO,EAAA;;AkCrFf;;;EAkIM,mBlCfW;EkCgBX,qBlChBW;EkCiBX,WlC/CS,EAAA;EkCrFf;;;IAuIU,WlClDK,EAAA;;AkCwDb;EADF;IAIQ,qBAAqC,EAAA,EACtC;;AAKP;EAIQ,mCAA2B;UAA3B,2BAA2B,EAAA;EAJnC;IAMU,mCAA2B;YAA3B,2BAA2B,EAAA;;AC5JrC;EAEI,kC5B6N4D;E4B5N5D,uBnCq0B+B;EmCp0B/B,yBnCqFc;EmCpFd,sBnCgFW;EmC/EX,cnCsFc;EmCrFd,sBnC8lBgC,EAAA;EmCrmBpC;IASM,aAAa;IACb,qBnC26BkE,EAAA;;AoCt7BxE;EACI,2BAA2B,EAAA;EAD/B;IAGQ,iDAAyC;IACzC,apCuFU,EAAA;EoC3FlB;IAOQ,cAAc,EAAA;;AAItB;;EAEI,iDAAyC,EAAA;;AAG7C;EACI,gBpCiDoB,EAAA;;AoC9CxB;EACI,oBAAoB;EACpB,epCgDqB,EAAA;;AoC7CzB;EACI,yBAA2B;EAC3B,iDAAyC;EACzC,0BAA0B,EAAA;;AAG9B;EACI,qBAAuB,EAAA;;AAG3B;;EAGQ,iDAAyC;EACzC,apCoDU,EAAA;;AqC3FlB;EACI,WAAW,EAAA;;AAGf;EACI,iBAAiB,EAAA;EADrB;IAIQ,arCmFU;IqClFV,crCkFU;IqCjFV,eAAe;IACf,cAAc,EAAA;;AAItB;EAEQ,WrCqEO;EqCpEP,UrCoEO;EqCnEP,eAAe,EAAA;;AAIvB;EACI,6BrCsEc,EAAA;;AqCnElB;;;;EAOgB,erCkFC,EAAA;;AqCzFjB;;;;EAegB,erCiFC,EAAA;;AqChGjB;;;;EAuBgB,erCsEC,EAAA;;AqC7FjB;;;;EA+BgB,erCmEC,EAAA;;AqClGjB;;;;EAuCgB,erCyDC,EAAA;;AqChGjB;;;;EA+CgB,erCoBE,EAAA;;AqCnElB;;;;EAuDgB,erCoCC,EAAA;;AqC7BjB;;EAGQ,arCwBS,EAAA;;AqCpBjB;;EAGQ,arCwBS,EAAA;;AqCpBjB;;EAGQ,arCgBS,EAAA;;AqCZjB;;EAGQ,arCUS,EAAA;;AqCNjB;EACI,iBAAiB,EAAA;;AAGrB;EACI,kBAAkB;EAClB,qBAAqB;EACrB,UAAU;EACV,eAAe;EACf,iBAAiB;EACjB,kBAAkB;EAClB,mBrClCc;EqCmCd,crCxCc;EqCyCd,kBAAkB;EAClB,oBAAoB;EACpB,UAAU;EACV,sCAA8B;EAA9B,8BAA8B,EAAA;EAZlC;IAcQ,UAAU,EAAA;;AAIlB;EACI,iBAAiB,EAAA;;AAGrB;EACI,iBAAiB,EAAA;;AClJrB,eAAA;AAEA;EACE,4BAA4B;EAC5B,oCAAsC;EACtC,oCAAgD;EAChD,sDtC2mB4D;UsC3mB5D,8CtC2mB4D;EsC1mB5D,YAAY;EACZ,ctCkFgB;EsCjFhB,UAAU;EACV,6BAA6B;EAC7B,0BAA0B,EAAA;;AAG5B;EAEM,wCAAwC,EAAA;;AAF9C;EAKM,YAAY,EAAA;;AAIlB;EACE,iBAAiB;EACjB,iBAAiB;EACjB,mBAAmB;EACnB,eAAe;EACf,gBtCwCsB;EsCvCtB,ctCgEgB,EAAA;;AsC7DlB;EAEM,kBAAkB,EAAA;EAFxB;IAIU,kBAAkB,EAAA;;AAK5B;EAEM,cAAc,EAAA;;AlC+BhB;EkCzBF;IACI,aAAa,EAAA,EAChB;;ACjDH;EACE,+BAAuB;UAAvB,uBAAuB;EACvB,sBAAsB;EACtB,uBAAuB;EACvB,oCAAsC;EACtC,oDvC2Fa;UuC3Fb,4CvC2Fa;EuC1Fb,4BAA4B;EAC5B,kBAAkB;EAClB,gCAAkC,EAAA;;AAGpC;EACE,yBAA2B;EAC3B,0BAA0B;EAC1B,4BAA4B;EAC5B,iDAAyC;EACzC,2BAA2C,EAAA;;ACf7C;EACE,aAAa;EACb,mBxCoFgB;EwCnFhB,kBAAkB,EAAA;;AAGpB;EACE,cAAc;EACd,kBAAkB;EAClB,WxC4Ea;EwC3Eb,eAAe;EACf,iBAAiB;EACjB,mBxCuGe;EwCtGf,kBAAkB;EAClB,kBAAkB,EAAA;;AAGpB;EACE,SAAS;EACT,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,kBAAkB,EAAA;EALpB;IAOI,aAAa;IACb,mCAAmC;IACnC,oCAAoC;IACpC,8BxCwFa,EAAA;EwClGjB;IAaI,UAAU;IACV,mCAAmC;IACnC,oCAAoC;IACpC,iCxCkFa,EAAA;;AyCpHjB;EACI,YAAY;EACZ,mBzC4Fc;EyC3Fd,czCoFc;EyCnFd,sCzCqpBoF;EyCppBpF,mBzC0pBiC;EyCzpBjC,gBAAgB,EAAA;;ACNpB;EAEM,qBAAqB,EAAA;;AAIzB;EACE,gBAAgB,EAAA;EADlB;IAGI,gBAAgB,EAAA;;ACTtB;EACI,kBAAkB;EAClB,SAAS;EACT,WAAW,EAAA;;AAKf;EACI,cAAc;EACd,WAAW;EACX,YAAY,EAAA;;AAGd;EACE,mBAAmB;EACnB,sBAAsB,EAAA;;AAG1B;EACI,8CAA8C;EAC9C,aAAa;EACb,sBAAsB;EACtB,2BAA2B,EAAA;;AAG/B;EACI,yB3CyFa,EAAA;;A2ClFjB;EACI,yBAAyB;EACzB,+FAAmG;EACnG,6BAA6B;EAC7B,oCAAoC;EACpC,gBAAgB;EAChB,eAAe;EACf,kBAAkB,EAAA;;AAOtB;EAEQ,WAAW;EACX,YAAY;EACZ,iBAAiB;EACjB,iBAAiB;EACjB,kBAAkB;EAClB,kBAAkB;EAClB,YAAY;EACZ,eAAe;EACf,qBAAqB,EAAA;;AAV7B;EAaY,WAAW;EACX,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,YAAY;EACZ,WAAW;EACX,iBAAiB;EACjB,kBAAkB;EAClB,mB3C+CK,EAAA;;A4ClHjB;EAEQ,mBAAmB;EACnB,kBAAkB;EAClB,yB5CmFU,EAAA;;A4CvFlB;EAOQ,eAAe;EACf,kBAAkB;EAClB,UAAU;EACV,MAAM;EACN,iBAAiB,EAAA;;AAKzB;EACI,cAAc;EACd,c5C0Ec;E4CzEd,gB5C6CoB;E4C5CpB,iBAAiB,EAAA;EAJrB;IAOQ,yB5CgEU,EAAA;E4CvElB;IAWQ,SAAS,EAAA;EAXjB;IAeQ,c5C6DU,EAAA;;A4CzDlB;EACI,gBAAgB,EAAA;EADpB;IAIY,cAAc;IACd,iBAAiB;IACjB,c5CkDM,EAAA;E4CxDlB;IAWgB,c5CoEC,EAAA;;A4C1DjB;EAGY,eAAe,EAAA;EAH3B;IAMgB,yB5CyBE,EAAA;;A4CnBlB;EAEQ,qBAAqB;EACrB,kBAAkB;EAClB,c5CmBU,EAAA;E4CvBlB;IAOY,WAAW;IACX,yB5CUM;I4CTN,kBAAkB,EAAA;EAT9B;IAYY,c5CkCK,EAAA;I4C9CjB;MAcgB,gCAAiC,EAAA;;AAMjD;EACI,yB5CHc,EAAA;;A4CMlB;EAEQ,c5CFU,EAAA;E4CAlB;IAIY,c5CLM;I4CMN,YAAY,EAAA;EALxB;IAUgB,c5CYC,EAAA;;A4CNjB;EACI,kBAAkB,EAAA;EADtB;IAIQ,kBAAkB;IAClB,MAAM;IACN,SAAS;IACT,gBAAgB;IAChB,8CAA8C;IAC9C,WAAW;IACX,YAAY;IACZ,W5CnCO;I4CoCP,eAAe;IACf,kBAAkB,EAAA;EAb1B;IAiBQ,kBAAkB;IAClB,MAAM;IACN,QAAQ,EAAA;IAnBhB;MAqBY,qBAAqB;MACrB,WAAW;MACX,YAAY;MACZ,yB5C7CM;M4C8CN,iBAAiB;MACjB,kBAAkB;MAClB,kBAAkB;MAClB,c5C/CM,EAAA;;A4CoDlB;EAGY,kBAAkB,EAAA;EAH9B;IAKgB,yB5C3DE,EAAA;E4CsDlB;IAUgB,WAAW,EAAA;;AAV3B;EAgBQ,yB5CvEU;E4CwEV,aAAa,EAAA;;AAIrB;EAEQ,gBAAgB,EAAA;;AAIxB;EAGY,c5CjFM,EAAA;;A4C8ElB;EAMY,kBAAkB,EAAA;;AAO9B;EACI,yB5C9Fc;E4C+Fd,sB5CnGW;E4CoGX,sB5C4agC,EAAA;E4C/apC;IAKQ,yBAAyB;IACzB,YACJ,EAAA;EAPJ;IAUQ,wCAAwC;IACxC,oCAAoC;IACpC,yBAA0B;IAC1B,eAAe;IACf,iBAAiB;IACjB,wBAAgB;YAAhB,gBAAgB,EAAA;;AAOxB;EACI,wBAAgB;UAAhB,gBAAgB,EAAA;EADpB;IAGQ,gCAAiC,EAAA;;AAIzC;EAGQ,WAAW;EACX,YAAY;EACZ,WAAW;EACX,kC5CtGS;E4CuGT,kBAAkB;EAClB,SAAS;EACT,kBAAkB,EAAA;;AAT1B;EAeQ,WAAW;EACX,YAAY;EACZ,WAAW;EACX,kC5ClHS;E4CmHT,kBAAkB;EAClB,SAAS;EACT,kBAAkB,EAAA;;AAK1B;EACI;IAGQ,YAAY,EAAA;IAHpB;MAKY,kCAAkC,EAAA,EACrC;;ACzPb;;oBhDi0FoB;AgD9zFpB;EACE,YAAY;EACZ,WAAW;EACX,aAAa;EACb,kBAAkB,EAAA;;AAGpB;EACE,kBAAkB,EAAA;;AAGpB;EAEI,c7CkFc;E6CjFd,gBAAgB,EAAA;;AAHpB;EAMI,eAAe,EAAA;;AAInB;EACE;IACE,WAAW;IACX,WAAW,EAAA;EAEb;IACE,SAAS,EAAA,EACV;;AAIH;EAEI,cAAc;EACd,c7C0Dc;E6CzDd,iBAAiB;EACjB,gBAAgB,EAAA;EALpB;IAOM,c7CkFW;I6CjFX,gBAAgB,EAAA;;AAKtB;EACE,cAAc;EACd,eAAe,EAAA;EAFjB;IAKI,kBAAkB;IAClB,cAAc;IACd,YAAY;IACZ,iBAAiB;IACjB,eAAe;IACf,gCAAwB;YAAxB,wBAAwB,EAAA;IAV5B;MAaM,c7CmCY,EAAA;I6ChDlB;MAiBM,mB7C4BY;M6C3BZ,iCAAyB;cAAzB,yBAAyB,EAAA;IAlB/B;MAsBM,WAAW;MACX,kBAAkB,EAAA;IAvBxB;MA2BM,YAAY,EAAA;MA3BlB;;;QAgCQ,cAAc;QACd,WAAW,EAAA;MAjCnB;QAqCQ,6BAA6B;QAC7B,oBAAoB;QACpB,mBAAmB;QACnB,SAAS;QACT,QAAQ;QACR,cAAc;QACd,YAAY,EAAA;MA3CpB;QA+CQ,wBAAwB,EAAA;MA/ChC;QAmDQ,gBAAgB;QAChB,gBAAgB,EAAA;MApDxB;QAwDQ,kBAAkB;QAClB,MAAM;QACN,WAAW;QACX,QAAQ;QACR,uBAAuB;QACvB,gBAAgB;QAChB,mBAAmB;QACnB,gBAAgB,EAAA;IA/DxB;MAoEM,kBAAkB;MAClB,MAAM;MACN,WAAW;MACX,QAAQ;MACR,SAAS,EAAA;MAxEf;;QA4EQ,kBAAkB;QAClB,MAAM,EAAA;MA7Ed;QAiFQ,OAAO;QACP,YAAY;QACZ,uBAAuB;QACvB,gBAAgB;QAChB,mBAAmB,EAAA;MArF3B;QAyFQ,QAAQ;QACR,YAAY;QACZ,kBAAkB,EAAA;IA3F1B;MAiGM,yC7CzBW;c6CyBX,iC7CzBW,EAAA;I6CxEjB;MAsGI,yB7CzDc;M6C0Dd,gBAAgB;MAChB,cAAuB,EAAA;MAxG3B;QA0GQ,cAAuB;QACvB,gBAAgB,EAAA;EA3GxB;IAkHI,eAAe;IACf,YAAY;IACZ,WAAW;IACX,kBAAkB;IAClB,qBAAqB;IACrB,2C7CzEc;Y6CyEd,mC7CzEc;I6C0Ed,kBAAkB,EAAA;IAxHtB;MA2HM,UAAU;MACV,eAAe,EAAA;IA5HrB;MA+HM,UAAU,EAAA;IA/HhB;MAmIM,kBAAkB;MAClB,YAAY;MACZ,WAAW;MACX,OAAO;MACP,eAAe;MACf,UAAU;MACV,gBAAgB;MAChB,iCAAyB;cAAzB,yBAAyB;MACzB,MAAM,EAAA;MA3IZ;QA6IQ,iBAAiB;QACjB,oCAAoC;QACpC,MAAM;QACN,YAAY;QACZ,cAAuB;QACvB,WAAW;QACX,kBAAkB;QAClB,iBAAiB;QACjB,SAAS;QACT,eAAe,EAAA;;AAMvB;EACE;IACI,YAAY,EAAA,EACf;;AC1MC;EADJ;IAEM,gBAAgB,EAAA,EAWnB;;AAbH;EAQQ,sB9C8EO,EAAA;;A8CvEb;EAGM,WAAW;EACX,kBAAkB;EAClB,UAAU;EACV,WAAW;EACX,yB9CkGS;E8CjGT,kBAAkB;EAClB,QAAQ,EAAA;;AATd;EAcI,YAAY;EACZ,wBAAgB;UAAhB,gBAAgB;EAChB,eAAe,EAAA;;AAInB;EACE,SAAS,EAAA;EADX;IAKQ,sB9C8CK;I8C7CL,sD9CskBoD;Y8CtkBpD,8C9CskBoD,EAAA;E8C5kB5D;IAUM,cAAc;IACd,kBAAkB;IAClB,c9C6CU;I8C5CV,4BAAoB;IAApB,oBAAoB;IACpB,yB9CuCU;I8CtCV,kBAAkB;IAClB,gBAAgB,EAAA;IAhBtB;MAkBQ,sB9CiCK;M8ChCL,sD9CyjBoD;c8CzjBpD,8C9CyjBoD,EAAA;;A8CjjB5D;EAGM,YAAY;EACZ,WAAW;EACX,iBAAiB;EACjB,wBAAgB;UAAhB,gBAAgB;EAChB,UAAU;EACV,eAAe;EACf,yB9CgBU;E8CfV,kBAAkB,EAAA;;AAVxB;EAcM,sD9CmiBsD;U8CniBtD,8C9CmiBsD;E8CliBtD,yB9CWU,EAAA;;A8CLhB;EAEI,WAAW,EAAA;;AAFf;EAMI,kBAAkB;EAClB,kBAAkB;EAClB,mBAAmB,EAAA;EARvB;IAWM,sB9CRO;I8CSP,kBAAkB;IAClB,UAAU;IACV,iBAAiB,EAAA;EAdvB;IAkBM,WAAW;IACX,kBAAkB;IAClB,WAAW;IACX,WAAW;IACX,OAAO;IACP,QAAQ;IACR,yB9CnBU;I8CoBV,SAAS,EAAA;EAzBf;IA4BM,eAAe,EAAA;;AA5BrB;EAgCI,mBAAmB;EACnB,qBAAqB;EACrB,kBAAkB,EAAA;EAlCtB;IAoCM,kBAAkB,EAAA;IApCxB;MAsCQ,WAAW;MACd,kBAAkB;MAClB,SAAS;MACT,WAAW;MACX,6BAA6B;MAC7B,8C9CVU,EAAA;E8CjCf;IAgDM,kBAAkB;IAClB,wC9ChBS;I8CiBT,8BAA8B;IAC9B,gBAAgB,EAAA;IAnDtB;MAuDQ,gBAAgB;MAChB,c9CvBO;M8CwBP,kBAAkB;MAClB,kBAAkB,EAAA;EA1D1B;IAgEM,YAAY,EAAA;IAhElB;MAkEQ,eAAe;MACf,YAAY;MACZ,c9C3DQ,EAAA;M8C4DR;QArER;UAsEU,aAAa,EAAA,EAEhB;IAxEP;MA2EQ,sD9CidoD;c8CjdpD,8C9CidoD;M8ChdpD,yB9CvEQ,EAAA;E8CLhB;IAiFM,eAAe,EAAA;;AAjFrB;EAuFM,YAAY,EAAA;EAvFlB;IAyFQ,kBAAkB,EAAA;IAzF1B;MA2FU,WAAW;MACd,kBAAkB;MAClB,SAAS;MACT,UAAU;MACV,6BAA6B;MAC7B,8B9C5FS,EAAA;E8CJhB;IAoGQ,yB9ChGQ;I8CiGR,iBAAiB;IACjB,8BAA8B,EAAA;EAtGtC;IAyGQ,WAAW,EAAA;EAzGnB;IA+GY,QAAQ;IACR,UAAU,EAAA;;AAWtB;EACE,6B9CvHc,EAAA;;A8C0HhB;EACE,mBAAmB;EACnB,oCAAmC;EACnC,gCAAgC;EAChC,oBAAoB,EAAA;;AAGtB;EACE,kBAAkB;EAClB,WAAW;EACX,QAAQ;EACR,mCAA2B;UAA3B,2BAA2B,EAAA;EAJ7B;IAOM,eAAe;IACf,iBAAiB;IACjB,gBAAgB;IAChB,qBAAqB,EAAA;;AAMzB;EADF;IAEI,eAAe,EAAA,EAElB;;AAGD;EACE,eAAe;EACf,kBAAkB;EAClB,UAAU;EACV,QAAQ;EACR,eAAe;EACf,iBAAiB,EAAA;;AAInB;EACE,kBAAkB;EAClB,kBAAkB,EAAA;;ACzPtB;EACI,eAAe;EACf,kBAAkB,EAAA;EAFtB;IAIQ,eAAe;IACf,cAAc;IACd,gBAAgB,EAAA;;AAIxB;EACI,WAAW;EACX,UAAU;EACV,iBAAiB;EACjB,0BAAwE;EACxE,sB/CqEW;E+CpEX,kBAAkB;EAClB,sBxC8NyB;EwC7NzB,sD/C2lB0D;U+C3lB1D,8C/C2lB0D,EAAA;;A+CxlB9D;EACI;IACI,UAAU,EAAA,EACb;;AC3BL,gDAAA;AAGA;EACI,kBAAkB;EAClB,WAAW;EACX,eAAe,EAAA;;AAGjB;;;EAGE,kBAAkB;EAClB,WAAW;EACX,kBAAkB;EAClB,UAAU,EAAA;;AAGZ;;;EAGE,qBAAqB;EACrB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,eAAe;EACf,kBAAkB;EAClB,uCAAuC;EACvC,yBhDyFa;EgDxFb,4BAA4B;EAC5B,sBAAsB;EACtB,oBAAoB;EACpB,WhDuDW;EgDtDX,yBAAyB,EAAA;;AAG3B;EAEI,cAAc,EAAA;;AAFlB;EAKI,kBAAkB;EAClB,WAAW;EACX,eAAe,EAAA;EAPnB;IASM,kBAAkB;IAClB,WAAW;IACX,UAAU;IACV,YAAY;IACZ,MAAM;IACN,SAAS;IACT,iBAAiB;IACjB,mBhDiES,EAAA;;AgDjFf;EAoBI,wBAAwB,EAAA;;AAI5B;;EAEE,iBAAiB,EAAA;;AAGnB;;EAEE,gBAAgB,EAAA;;AAGlB;EACE,WAAW;EACX,cAAc;EACd,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,mBhDwCa;EgDvCb,mBAAmB;EACnB,UAAU,EAAA;;AAGZ;EACE,UAAU,EAAA;;AAGZ;EACE,WAAW,EAAA;;AAGb;;EAEE,kBAAkB;EAClB,qBAAqB;EACrB,YAAY;EACZ,aAAa;EACb,yBhDPc;EgDQd,kBAAkB,EAAA;;AAGpB;EACE,WAAW;EACX,cAAc;EACd,UAAU;EACV,YAAY;EACZ,kBAAkB;EAClB,uBAAuB,EAAA;;AAGzB;;EAEE,WAAW;EACX,cAAc;EACd,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,mBAAmB,EAAA;;AAGrB;;EAEE,UAAU,EAAA;;AAGZ;;EAEE,WAAW,EAAA;;AAGb;;EAEE,SAAS;EACT,kBAAkB,EAAA;;AAGpB;EACE,SAAS;EACT,yDhDjDc;EgDkDd,kBAAkB,EAAA;;AAGpB;EACE,SAAS;EACT,yDhDtDc;EgDuDd,kBAAkB,EAAA;;AAGpB;EACE,yDAAwD,EAAA;;AAG1D;EACE,yDAA2D,EAAA;;AAG7D;EACE,UAAU;EACV,yDAA2D,EAAA;;AAG7D;EACE,UAAU;EACV,yDAAwD;EACxD,kBAAkB;EAClB,UAAU,EAAA;;AAGZ;EACE,WAAW,EAAA;;AAGb;EACE;IACE,UAAU,EAAA;EAGZ;;;;;;;;IAQE,gBAAgB,EAAA;EAGlB;;IAEE,UAAU,EAAA;EAGZ;;;IAGE,iBAAiB,EAAA;EAGnB;IACE,cAAc,EAAA;EAGhB;IACE,WAAW;IACX,yDAAwD,EAAA;EAG1D;IACE,WAAW;IACX,yDAA2D,EAAA;EAG7D;;IAEE,UAAU;IACV,cAAc,EAAA,EACf;;ACpNL;EACI,qBAAqB;EACrB,sBjDiFW;EiDhFX,sDjDymB0D;UiDzmB1D,8CjDymB0D;EiDxmB1D,YAAY;EACZ,kBAAkB,EAAA;EALtB;IAOQ,qBAAqB,EAAA;;AAK7B;EAGY,gBAAgB,EAAA;;AAK5B;;sBpDyzGsB;AoDrzGtB;EAEQ,eAAe,EAAA;;AAFvB;EAKQ,kBAAkB;EAClB,kBAAkB;EAClB,yBjDsDU;EiDrDV,cjD0DU,EAAA;EiDlElB;IAUY,eAAe;IACf,kBAAkB;IAClB,cAAc,EAAA;EAZ1B;IAgBY,qBjDyEK;IiDxEL,6BAA6B;IAC7B,cjDgDM,EAAA;IiDlElB;MAqBgB,cjDoEC,EAAA;;AiD9DjB;EACI,gBAAgB,EAAA;EAChB;IAFJ;MAGQ,eAAe,EAAA,EAEtB;;AAED;EACI,cjD0Da;EiDzDb,kBAAkB,EAAA;EAFtB;IAKQ,kBAAkB;IAClB,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,YAAY,EAAA;IAEZ;MAXR;QAYY,WAAW;QACX,WAAW;QACX,YAAY,EAAA,EAEnB", "file": "app-rtl.min.css", "sourcesContent": ["/*\r\nTemplate Name: <PERSON><PERSON> -  Admin & Dashboard Template\r\nAuthor: Themesdesign\r\nVersion: 1.0.0\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\r\n\r\n\r\n//Fonts\r\n@import \"custom/fonts/fonts\";\r\n\r\n//Core files\r\n@import \"./node_modules/bootstrap/scss/functions\";\r\n@import \"./node_modules/bootstrap/scss/variables\";\r\n@import \"variables\";\r\n@import \"./node_modules/bootstrap/scss/mixins.scss\";\r\n\r\n// Structure\r\n@import \"custom/structure/general\";\r\n@import \"custom/structure/topbar\";\r\n@import \"custom/structure/page-head\";\r\n@import \"custom/structure/footer\";\r\n@import \"custom/structure/right-sidebar\";\r\n@import \"custom/structure/vertical\";\r\n@import \"custom/structure/horizontal-nav\";\r\n@import \"custom/structure/layouts\";\r\n\r\n// Components\r\n@import \"custom/components/waves\";\r\n@import \"custom/components/avatar\";\r\n@import \"custom/components/accordion\";\r\n@import \"custom/components/helper\";\r\n@import \"custom/components/preloader\";\r\n@import \"custom/components/forms\";\r\n@import \"custom/components/widgets\";\r\n@import \"custom/components/demos\";\r\n@import \"custom/components/print\";\r\n\r\n// Plugins\r\n@import \"custom/plugins/custom-scrollbar\";\r\n@import \"custom/plugins/calendar\";\r\n@import \"custom/plugins/color-picker\";\r\n@import \"custom/plugins/session-timeout\";\r\n@import \"custom/plugins/range-slider\";\r\n@import \"custom/plugins/sweatalert2\";\r\n@import \"custom/plugins/rating\";\r\n@import \"custom/plugins/parsley\";\r\n@import \"custom/plugins/select2\";\r\n@import \"custom/plugins/switch\";\r\n@import \"custom/plugins/datepicker\";\r\n@import \"custom/plugins/bootstrap-touchspin\";\r\n@import \"custom/plugins/datatable\";\r\n@import \"custom/plugins/form-editors\";\r\n@import \"custom/plugins/form-upload\";\r\n@import \"custom/plugins/form-wizard\";\r\n@import \"custom/plugins/responsive-table\";\r\n@import \"custom/plugins/table-editable\";\r\n@import \"custom/plugins/apexcharts\";\r\n@import \"custom/plugins/chartist\";\r\n@import \"custom/plugins/flot\";\r\n@import \"custom/plugins/sparkline-chart\";\r\n@import \"custom/plugins/google-map\";\r\n@import \"custom/plugins/vector-maps\";\r\n@import \"custom/plugins/x-editable\";\r\n\r\n// Pages\r\n@import \"custom/pages/authentication\";\r\n@import \"custom/pages/ecommerce\";\r\n@import \"custom/pages/email\";\r\n@import \"custom/pages/chat\";\r\n@import \"custom/pages/coming-soon\";\r\n@import \"custom/pages/timeline\";\r\n@import \"custom/pages/extras-pages\";\r\n", "/*\r\nTemplate Name: <PERSON><PERSON> -  <PERSON><PERSON> & Dashboard Template\r\nAuthor: Themesdesign\r\nVersion: 1.0.0\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\n@import url(\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap\");\n#page-topbar {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1001;\n  background-color: #ffffff; }\n\n.navbar-header {\n  display: flex;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 auto;\n  height: 70px;\n  padding: 0 calc(24px / 2) 0 0;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n  .navbar-header .dropdown.show .header-item {\n    background-color: #f9fafc; }\n\n.navbar-brand-box {\n  padding: 0 1.5rem;\n  text-align: center;\n  width: 260px; }\n\n.logo {\n  line-height: 70px; }\n  .logo .logo-sm {\n    display: none; }\n\n.logo-dark {\n  display: block; }\n\n.logo-light {\n  display: none; }\n\n.fullscreen-enable [data-toggle=\"fullscreen\"] .mdi-fullscreen::before {\n  content: '\\F0294'; }\n\n.page-content-wrapper {\n  margin-top: -90px; }\n\n.page-title-box {\n  background: url(../images/title-img.png);\n  background-position: center;\n  background-color: #525ce5;\n  margin: 0 -24px 23px -24px;\n  padding: 24px 24px 92px 24px;\n  color: #fff;\n  background-size: cover; }\n\n/* Search */\n.search-wrap {\n  background-color: white;\n  color: #343a40;\n  z-index: 9997;\n  position: absolute;\n  top: 0;\n  display: flex;\n  width: 100%;\n  right: 0;\n  height: 70px;\n  padding: 0 15px;\n  transform: translate3d(0, -100%, 0);\n  transition: .3s; }\n  .search-wrap form {\n    display: flex;\n    width: 100%; }\n  .search-wrap .search-bar {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%; }\n  .search-wrap .search-input {\n    flex: 1 1;\n    border: none;\n    outline: none;\n    box-shadow: none;\n    background-color: transparent; }\n  .search-wrap .close-search {\n    width: 36px;\n    height: 64px;\n    line-height: 64px;\n    text-align: center;\n    color: inherit;\n    font-size: 24px; }\n    .search-wrap .close-search:hover {\n      color: #f14e4e; }\n\n.search-wrap.open {\n  transform: translate3d(0, 0, 0); }\n\n.megamenu-list li {\n  position: relative;\n  padding: 5px 0px; }\n  .megamenu-list li a {\n    color: #495057; }\n\n@media (max-width: 992px) {\n  #page-topbar {\n    left: 0; }\n  .navbar-brand-box {\n    width: auto; }\n  .logo span.logo-lg {\n    display: none; }\n  .logo span.logo-sm {\n    display: inline-block; } }\n\n.page-content {\n  padding: calc(70px) calc(24px / 2) 60px calc(24px / 2); }\n\n.header-item {\n  height: 70px;\n  box-shadow: none !important;\n  color: #636e75;\n  border: 0;\n  border-radius: 0px; }\n  .header-item:hover {\n    color: #636e75; }\n\n.header-profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: #eaedf1;\n  padding: 3px; }\n\n.noti-icon i {\n  font-size: 24px;\n  color: #636e75; }\n\n.noti-icon .badge {\n  position: absolute;\n  top: 20px;\n  right: 6px; }\n\n.notification-item .media {\n  padding: 0.75rem 1rem; }\n  .notification-item .media:hover {\n    background-color: #eaedf1; }\n\n.dropdown-icon-item {\n  display: block;\n  border-radius: 3px;\n  line-height: 34px;\n  text-align: center;\n  padding: 15px 0 9px;\n  display: block;\n  border: 1px solid transparent;\n  color: #74788d; }\n  .dropdown-icon-item img {\n    height: 24px; }\n  .dropdown-icon-item span {\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap; }\n  .dropdown-icon-item:hover {\n    border-color: #edf1f5; }\n\n.fullscreen-enable [data-toggle=\"fullscreen\"] .bx-fullscreen::before {\n  content: \"\\ea3f\"; }\n\nbody[data-topbar=\"dark\"] #page-topbar {\n  background-color: #1f293f; }\n\nbody[data-topbar=\"dark\"] .navbar-header .dropdown.show .header-item {\n  background-color: rgba(255, 255, 255, 0.05); }\n\nbody[data-topbar=\"dark\"] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\nbody[data-topbar=\"dark\"] .header-item {\n  color: #a3acc1; }\n  body[data-topbar=\"dark\"] .header-item:hover {\n    color: #a3acc1; }\n\nbody[data-topbar=\"dark\"] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25); }\n\nbody[data-topbar=\"dark\"] .noti-icon i {\n  color: #a3acc1; }\n\nbody[data-topbar=\"dark\"] .title-tooltip li i {\n  color: #a3acc1; }\n\nbody[data-topbar=\"dark\"] .app-search .form-control {\n  background-color: rgba(241, 245, 247, 0.07);\n  color: #fff; }\n\nbody[data-topbar=\"dark\"] .app-search span,\nbody[data-topbar=\"dark\"] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-sidebar=\"dark\"] .navbar-brand-box {\n  background: #1f293f; }\n\nbody[data-sidebar=\"dark\"] .logo-dark {\n  display: none; }\n\nbody[data-sidebar=\"dark\"] .logo-light {\n  display: block; }\n\n@media (max-width: 600px) {\n  .navbar-header .dropdown {\n    position: static; }\n    .navbar-header .dropdown .dropdown-menu {\n      left: 10px !important;\n      right: 10px !important; } }\n\n@media (max-width: 380px) {\n  .navbar-brand-box {\n    display: none; } }\n\nbody[data-layout=\"horizontal\"] .navbar-brand-box {\n  width: auto; }\n\nbody[data-layout=\"horizontal\"] .page-content {\n  margin-top: 70px;\n  padding: calc(36px + 24px) calc(24px / 2) 60px calc(24px / 2); }\n\n@media (max-width: 992px) {\n  body[data-layout=\"horizontal\"] .page-content {\n    margin-top: 10px; } }\n\n.page-title-box .breadcrumb {\n  background-color: transparent;\n  padding: 0; }\n\n.page-title-box h4 {\n  color: #fff;\n  text-transform: uppercase;\n  font-weight: 500;\n  font-size: 16px !important; }\n\n.topbar-social-icon {\n  padding: calc(38px / 2) 0; }\n\n.title-tooltip li i {\n  font-size: 20px;\n  margin-left: 10px;\n  color: #636e75; }\n\n.footer {\n  bottom: 0;\n  padding: 20px calc(24px / 2);\n  position: absolute;\n  right: 0;\n  border-top: 1px solid #edf1f5;\n  color: #74788d;\n  left: 260px;\n  height: 60px;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  background-color: #fff; }\n\n@media (max-width: 992px) {\n  .footer {\n    left: 0; } }\n\n.vertical-collpsed .footer {\n  left: 70px; }\n\nbody[data-layout=\"horizontal\"] .footer {\n  left: 0 !important; }\n\n.right-bar {\n  background-color: #fff;\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 280px;\n  z-index: 9999;\n  float: right !important;\n  right: -290px;\n  top: 0;\n  bottom: 0; }\n  .right-bar .right-bar-toggle {\n    background-color: #444c54;\n    height: 24px;\n    width: 24px;\n    line-height: 24px;\n    color: #edf1f5;\n    text-align: center;\n    border-radius: 50%; }\n    .right-bar .right-bar-toggle:hover {\n      background-color: #4b545c; }\n\n.rightbar-overlay {\n  background-color: rgba(52, 58, 64, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all .2s ease-out; }\n\n.right-bar-enabled .right-bar {\n  right: 0; }\n\n.right-bar-enabled .rightbar-overlay {\n  display: block; }\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto; }\n    .right-bar .slimscroll-menu {\n      height: auto !important; } }\n\n.metismenu {\n  margin: 0; }\n  .metismenu li {\n    display: block;\n    width: 100%; }\n  .metismenu .mm-collapse {\n    display: none; }\n    .metismenu .mm-collapse:not(.mm-show) {\n      display: none; }\n    .metismenu .mm-collapse.mm-show {\n      display: block; }\n  .metismenu .mm-collapsing {\n    position: relative;\n    height: 0;\n    overflow: hidden;\n    transition-timing-function: ease;\n    transition-duration: .35s;\n    transition-property: height, visibility; }\n\n.vertical-menu {\n  width: 260px;\n  z-index: 1001;\n  background: #ffffff;\n  bottom: 0;\n  margin-top: 0;\n  position: fixed;\n  top: 70px;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n.user-sidebar {\n  position: relative;\n  text-align: center;\n  background: url(../images/user-img.png);\n  background-color: #525ce5;\n  background-repeat: no-repeat;\n  background-size: cover;\n  background-position: center;\n  padding: 20px 0; }\n  .user-sidebar .user-img {\n    position: relative; }\n    .user-sidebar .user-img img {\n      width: 60px;\n      height: 60px;\n      border: 3px solid #23c58f;\n      padding: 5px; }\n    .user-sidebar .user-img .avatar-online {\n      position: absolute;\n      bottom: 4px;\n      width: 10px;\n      height: 10px;\n      z-index: 1;\n      border: 2px solid transparent;\n      border-radius: 50%;\n      margin-left: -15px; }\n\n.main-content {\n  margin-left: 260px;\n  overflow: hidden; }\n  .main-content .content {\n    padding: 0 15px 10px 15px;\n    margin-top: 70px; }\n\n#sidebar-menu {\n  padding: 0px 0 30px 0; }\n  #sidebar-menu .mm-active > .has-arrow:after {\n    transform: rotate(90deg); }\n  #sidebar-menu .has-arrow:after {\n    content: \"\\F0142\";\n    font-family: 'Material Design Icons';\n    display: block;\n    float: right;\n    transition: transform .2s;\n    font-size: 1rem; }\n  #sidebar-menu ul li a {\n    display: block;\n    padding: .625rem 1.2rem;\n    color: #27303f;\n    position: relative;\n    font-size: 14.5px;\n    transition: all .4s;\n    margin: 0px 17px;\n    border-radius: 3px; }\n    #sidebar-menu ul li a i {\n      display: inline-block;\n      min-width: 1.75rem;\n      padding-bottom: .125em;\n      font-size: 16px;\n      line-height: 1.40625rem;\n      vertical-align: middle;\n      color: #27303f;\n      transition: all .4s; }\n    #sidebar-menu ul li a:hover {\n      color: #525ce5; }\n      #sidebar-menu ul li a:hover i {\n        color: #525ce5; }\n  #sidebar-menu ul li .badge {\n    margin-top: 5px; }\n  #sidebar-menu ul li ul.sub-menu {\n    padding: 0; }\n    #sidebar-menu ul li ul.sub-menu li a {\n      padding: .4rem 1.5rem .4rem 2.8rem;\n      font-size: 14px;\n      color: #27303f;\n      background-color: transparent !important; }\n      #sidebar-menu ul li ul.sub-menu li a:before {\n        content: \"\\F09DF\";\n        font-family: 'Material Design Icons';\n        font-size: 20px;\n        line-height: 10px;\n        padding-right: 2px;\n        vertical-align: middle;\n        display: inline-block; }\n    #sidebar-menu ul li ul.sub-menu li ul.sub-menu {\n      padding: 0; }\n      #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n        padding: .4rem 1.5rem .4rem 4rem;\n        font-size: 14px; }\n\n.menu-title {\n  padding: 12px 20px !important;\n  letter-spacing: .05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 11px;\n  text-transform: uppercase;\n  color: #27303f;\n  font-weight: 600; }\n\n.mm-active {\n  color: #525ce5 !important; }\n  .mm-active > a {\n    color: #525ce5 !important;\n    background-color: #f5f7fa !important; }\n    .mm-active > a i {\n      color: #525ce5 !important; }\n  .mm-active > i {\n    color: #525ce5 !important; }\n  .mm-active .active {\n    color: #525ce5 !important;\n    background-color: #f5f7fa !important; }\n    .mm-active .active i {\n      color: #525ce5 !important; }\n\n@media (max-width: 992px) {\n  .vertical-menu {\n    display: none; }\n  .main-content {\n    margin-left: 0 !important; }\n  body.sidebar-enable .vertical-menu {\n    display: block; } }\n\n.vertical-collpsed .user-sidebar {\n  display: none; }\n\n.vertical-collpsed .main-content {\n  margin-left: 70px; }\n\n.vertical-collpsed .navbar-brand-box {\n  width: 70px !important; }\n\n.vertical-collpsed .logo span.logo-lg {\n  display: none; }\n\n.vertical-collpsed .logo span.logo-sm {\n  display: block; }\n\n.vertical-collpsed .vertical-menu {\n  position: absolute;\n  width: 70px !important;\n  z-index: 5; }\n  .vertical-collpsed .vertical-menu .simplebar-mask,\n  .vertical-collpsed .vertical-menu .simplebar-content-wrapper {\n    overflow: visible !important; }\n  .vertical-collpsed .vertical-menu .simplebar-scrollbar {\n    display: none !important; }\n  .vertical-collpsed .vertical-menu .simplebar-offset {\n    bottom: 0 !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .menu-title,\n  .vertical-collpsed .vertical-menu #sidebar-menu .badge,\n  .vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {\n    display: none !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {\n    height: inherit !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {\n    display: none; }\n  .vertical-collpsed .vertical-menu #sidebar-menu > ul > li {\n    position: relative;\n    white-space: nowrap; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {\n      padding: 15px 20px;\n      min-height: 55px;\n      transition: none;\n      margin: 0; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {\n        color: #525ce5; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n        font-size: 1.15rem;\n        margin-left: 4px; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n        display: none;\n        padding-left: 25px; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n      position: relative;\n      width: calc(190px + 70px);\n      background-color: #f5f7fa;\n      transition: none; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {\n        display: inline; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n      display: block;\n      left: 70px;\n      position: absolute;\n      width: 190px;\n      height: auto !important;\n      box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {\n        box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n        box-shadow: none;\n        padding: 8px 20px;\n        position: relative;\n        width: 190px;\n        z-index: 6;\n        color: #27303f;\n        margin: 0; }\n        .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n          color: #525ce5; }\n  .vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n    padding: 5px 0;\n    z-index: 9999;\n    display: none;\n    background-color: #ffffff; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n      display: block;\n      left: 190px;\n      height: auto !important;\n      margin-top: -36px;\n      position: absolute;\n      width: 190px; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {\n      position: absolute;\n      right: 20px;\n      top: 12px;\n      transform: rotate(270deg); }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {\n      color: #f9fafc; }\n\nbody[data-sidebar=\"dark\"] .user-sidebar {\n  background: none; }\n\nbody[data-sidebar=\"dark\"] .vertical-menu {\n  background: #1f293f; }\n\nbody[data-sidebar=\"dark\"] #sidebar-menu ul li a {\n  color: #8590a5; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li a i {\n    color: #8590a5; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li a:hover {\n    color: #d7e4ec; }\n    body[data-sidebar=\"dark\"] #sidebar-menu ul li a:hover i {\n      color: #d7e4ec; }\n\nbody[data-sidebar=\"dark\"] #sidebar-menu ul li ul.sub-menu li a {\n  color: #8590a5; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li ul.sub-menu li a:hover {\n    color: #d7e4ec; }\n\nbody[data-sidebar=\"dark\"].vertical-collpsed {\n  min-height: 1400px; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n    background: #222d46;\n    color: #d7e4ec; }\n    body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n      color: #d7e4ec; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n    color: #8590a5; }\n    body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n      color: #525ce5; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n    background-color: white; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n    color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n    color: #525ce5 !important; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n    color: #525ce5 !important; }\n\nbody[data-sidebar=\"dark\"] .mm-active {\n  color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"] .mm-active > a {\n    color: #d7e4ec !important;\n    background-color: #2b364e !important; }\n    body[data-sidebar=\"dark\"] .mm-active > a i {\n      color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"] .mm-active > i {\n    color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"] .mm-active .active {\n    color: #d7e4ec !important;\n    background-color: #2b364e !important; }\n    body[data-sidebar=\"dark\"] .mm-active .active i {\n      color: #d7e4ec !important; }\n\nbody[data-sidebar=\"dark\"] .menu-title {\n  color: #8590a5; }\n\nbody[data-layout=\"horizontal\"] .main-content {\n  margin-left: 0 !important; }\n\nbody[data-sidebar-size=\"small\"] .navbar-brand-box {\n  width: 160px; }\n\nbody[data-sidebar-size=\"small\"] .vertical-menu {\n  width: 160px;\n  text-align: center; }\n  body[data-sidebar-size=\"small\"] .vertical-menu .has-arrow:after,\n  body[data-sidebar-size=\"small\"] .vertical-menu .badge {\n    display: none !important; }\n\nbody[data-sidebar-size=\"small\"] .main-content {\n  margin-left: 160px; }\n\nbody[data-sidebar-size=\"small\"] .footer {\n  left: 160px; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li a i {\n  display: block; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li a {\n  padding-left: 1.5rem; }\n  body[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li a:before {\n    display: none; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-left: 1.5rem; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .main-content {\n  margin-left: 70px; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: left; }\n  body[data-sidebar-size=\"small\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n    display: inline-block; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .footer {\n  left: 70px; }\n\n[dir=\"rtl\"]\n#sidebar-menu .has-arrow:after {\n  content: \"\\F0141\";\n  transition: transform .2s; }\n\n[dir=\"rtl\"]\n#sidebar-menu .mm-active > .has-arrow:after {\n  transform: rotate(90deg); }\n\n.topnav {\n  background: #fff;\n  padding: 0 calc(24px / 2);\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  margin-top: 70px;\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 100; }\n  .topnav .topnav-menu {\n    margin: 0;\n    padding: 0; }\n  .topnav .navbar-nav .nav-link {\n    font-size: 15px;\n    position: relative;\n    padding: 1.2rem 1.5rem;\n    color: #27303f; }\n    .topnav .navbar-nav .nav-link i {\n      font-size: 15px;\n      top: 2px;\n      position: relative; }\n    .topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {\n      color: #525ce5;\n      background-color: transparent; }\n  .topnav .navbar-nav .dropdown-item {\n    color: #27303f; }\n    .topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {\n      color: #525ce5;\n      background: transparent; }\n  .topnav .navbar-nav .nav-item .nav-link.active {\n    color: #525ce5; }\n  .topnav .navbar-nav .dropdown.active > a {\n    color: #525ce5;\n    background-color: transparent; }\n\n@media (min-width: 1200px) {\n  body[data-layout=\"horizontal\"] .container-fluid,\n  body[data-layout=\"horizontal\"] .navbar-header {\n    max-width: 85%; } }\n\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 0; }\n  .topnav .dropdown-item {\n    padding: .5rem 1.5rem;\n    min-width: 180px; }\n  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {\n    left: 0px;\n    right: auto; }\n  .topnav .dropdown .dropdown-menu {\n    margin-top: 0;\n    border-radius: 0 0 0.25rem 0.25rem; }\n    .topnav .dropdown .dropdown-menu .arrow-down::after {\n      right: 15px;\n      transform: rotate(-135deg) translateY(-50%);\n      position: absolute; }\n    .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n      position: absolute;\n      top: 0 !important;\n      left: 100%;\n      display: none; }\n  .topnav .dropdown:hover > .dropdown-menu {\n    display: block; }\n  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {\n    display: block; }\n  .navbar-toggle {\n    display: none; } }\n\n.arrow-down {\n  display: inline-block; }\n  .arrow-down:after {\n    border-color: initial;\n    border-style: solid;\n    border-width: 0 0 1px 1px;\n    content: \"\";\n    height: .4em;\n    display: inline-block;\n    right: 5px;\n    top: 50%;\n    margin-left: 10px;\n    transform: rotate(-45deg) translateY(-50%);\n    transform-origin: top;\n    transition: all .3s ease-out;\n    width: .4em; }\n\n@media (max-width: 1199.98px) {\n  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto; } }\n\n@media (max-width: 991.98px) {\n  .navbar-brand-box .logo-dark {\n    display: block; }\n    .navbar-brand-box .logo-dark span.logo-sm {\n      display: block; }\n  .navbar-brand-box .logo-light {\n    display: none; }\n  .topnav {\n    max-height: 360px;\n    overflow-y: auto;\n    padding: 0; }\n    .topnav .navbar-nav .nav-link {\n      padding: 0.75rem 1.1rem; }\n    .topnav .dropdown .dropdown-menu {\n      background-color: transparent;\n      border: none;\n      box-shadow: none;\n      padding-left: 20px; }\n      .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {\n        width: auto; }\n        .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {\n          margin: 0px; }\n    .topnav .dropdown .dropdown-item {\n      position: relative;\n      background-color: transparent; }\n      .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {\n        color: #525ce5; }\n    .topnav .arrow-down::after {\n      right: 15px;\n      position: absolute; } }\n\n@media (min-width: 992px) {\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .navbar-brand-box .logo-dark {\n    display: block; }\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .navbar-brand-box .logo-light {\n    display: none; }\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav {\n    background-color: #141b2d; }\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link {\n      color: rgba(255, 255, 255, 0.5); }\n      body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link:focus, body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link:hover {\n        color: rgba(255, 255, 255, 0.9); }\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav > .dropdown.active > a {\n      color: rgba(255, 255, 255, 0.9) !important; } }\n\nbody[data-layout=\"horizontal\"] .logo-dark {\n  display: none; }\n\nbody[data-layout=\"horizontal\"] .logo-light {\n  display: block; }\n\nbody[data-topbar=\"colored\"] #page-topbar {\n  background-color: #778beb; }\n\nbody[data-topbar=\"colored\"] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05); }\n\nbody[data-topbar=\"colored\"] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\nbody[data-topbar=\"colored\"] .title-tooltip li i {\n  color: rgba(255, 255, 255, 0.8); }\n\nbody[data-topbar=\"colored\"] .header-item {\n  color: rgba(255, 255, 255, 0.5); }\n  body[data-topbar=\"colored\"] .header-item:hover {\n    color: #fff; }\n\nbody[data-topbar=\"colored\"] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25); }\n\nbody[data-topbar=\"colored\"] .noti-icon i {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-topbar=\"colored\"] .logo-dark {\n  display: none; }\n\nbody[data-topbar=\"colored\"] .logo-light {\n  display: block; }\n\nbody[data-topbar=\"colored\"] .app-search .form-control {\n  background-color: rgba(241, 245, 247, 0.07);\n  color: #fff; }\n\nbody[data-topbar=\"colored\"] .app-search span,\nbody[data-topbar=\"colored\"] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-layout-size=\"boxed\"] {\n  background-color: #f1f3f7; }\n  body[data-layout-size=\"boxed\"] #layout-wrapper {\n    background-color: #f5f7fa;\n    max-width: 1400px;\n    margin: 0 auto;\n    box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n  body[data-layout-size=\"boxed\"] #page-topbar {\n    max-width: 1400px;\n    margin: 0 auto; }\n  body[data-layout-size=\"boxed\"] .footer {\n    margin: 0 auto;\n    max-width: calc(1400px - 260px); }\n  body[data-layout-size=\"boxed\"].vertical-collpsed .footer {\n    max-width: calc(1400px - 70px); }\n\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] #page-topbar, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] #layout-wrapper, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .footer {\n  max-width: 100%; }\n\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .container-fluid, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .navbar-header {\n  max-width: 1400px; }\n\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent; }\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none; }\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2); }\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important; }\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1; }\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em; }\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em; }\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom; }\n\n.waves-input-wrapper.waves-button {\n  padding: 0; }\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1; }\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%; }\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms; }\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }\n\n.waves-block {\n  display: block; }\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4); }\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(82, 92, 229, 0.4); }\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(35, 197, 143, 0.4); }\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(91, 164, 229, 0.4); }\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(238, 177, 72, 0.4); }\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(241, 78, 78, 0.4); }\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem; }\n\n.avatar-sm {\n  height: 2.5rem;\n  width: 2.5rem; }\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem; }\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem; }\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem; }\n\n.mini-stat-icon {\n  width: 46px;\n  height: 46px; }\n\n.avatar-title {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%; }\n\n.custom-accordion .card + .card {\n  margin-top: 0.5rem; }\n\n.custom-accordion a.collapsed i.accor-plus-icon:before {\n  content: \"\\F0415\"; }\n\n.custom-accordion .card-header {\n  border-radius: 7px; }\n\n.custom-accordion-arrow .card {\n  border: 1px solid #edf1f5;\n  box-shadow: none; }\n\n.custom-accordion-arrow .card-header {\n  padding-left: 45px;\n  position: relative; }\n  .custom-accordion-arrow .card-header .accor-arrow-icon {\n    position: absolute;\n    display: inline-block;\n    width: 24px;\n    height: 24px;\n    line-height: 24px;\n    font-size: 16px;\n    background-color: #525ce5;\n    color: #fff;\n    border-radius: 50%;\n    text-align: center;\n    left: 10px;\n    top: 50%;\n    transform: translateY(-50%); }\n\n.custom-accordion-arrow a.collapsed i.accor-arrow-icon:before {\n  content: \"\\F0142\"; }\n\n.font-size-10 {\n  font-size: 10px !important; }\n\n.font-size-11 {\n  font-size: 11px !important; }\n\n.font-size-12 {\n  font-size: 12px !important; }\n\n.font-size-13 {\n  font-size: 13px !important; }\n\n.font-size-14 {\n  font-size: 14px !important; }\n\n.font-size-15 {\n  font-size: 15px !important; }\n\n.font-size-16 {\n  font-size: 16px !important; }\n\n.font-size-17 {\n  font-size: 17px !important; }\n\n.font-size-18 {\n  font-size: 18px !important; }\n\n.font-size-20 {\n  font-size: 20px !important; }\n\n.font-size-22 {\n  font-size: 22px !important; }\n\n.font-size-24 {\n  font-size: 24px !important; }\n\n.media {\n  display: flex;\n  align-items: flex-start; }\n\n.media-body {\n  flex: 1; }\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 2px);\n  display: block;\n  border: 1px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd;\n  text-align: center;\n  transition: all 0.4s; }\n  .social-list-item:hover {\n    color: #74788d;\n    background-color: #edf1f5; }\n\n.w-xs {\n  min-width: 80px; }\n\n.w-sm {\n  min-width: 95px; }\n\n.w-md {\n  min-width: 110px; }\n\n.w-lg {\n  min-width: 140px; }\n\n.w-xl {\n  min-width: 160px; }\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  opacity: 0.7;\n  background-color: #000; }\n\n.flex-1 {\n  flex: 1; }\n\n.alert-dismissible .btn-close {\n  font-size: 10px;\n  padding: 1.05rem 1.25rem;\n  background: transparent url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e\") center/1em auto no-repeat; }\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  z-index: 9999; }\n\n#status {\n  width: 40px;\n  height: 40px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin: -20px 0 0 -20px; }\n\n.spinner-chase {\n  margin: 0 auto;\n  width: 40px;\n  height: 40px;\n  position: relative;\n  animation: spinner-chase 2.5s infinite linear both; }\n\n.chase-dot {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  top: 0;\n  animation: chase-dot 2.0s infinite ease-in-out both; }\n  .chase-dot:before {\n    content: '';\n    display: block;\n    width: 25%;\n    height: 25%;\n    background-color: #525ce5;\n    border-radius: 100%;\n    animation: chase-dot-before 2.0s infinite ease-in-out both; }\n  .chase-dot:nth-child(1) {\n    animation-delay: -1.1s; }\n    .chase-dot:nth-child(1):before {\n      animation-delay: -1.1s; }\n  .chase-dot:nth-child(2) {\n    animation-delay: -1.0s; }\n    .chase-dot:nth-child(2):before {\n      animation-delay: -1.0s; }\n  .chase-dot:nth-child(3) {\n    animation-delay: -0.9s; }\n    .chase-dot:nth-child(3):before {\n      animation-delay: -0.9s; }\n  .chase-dot:nth-child(4) {\n    animation-delay: -0.8s; }\n    .chase-dot:nth-child(4):before {\n      animation-delay: -0.8s; }\n  .chase-dot:nth-child(5) {\n    animation-delay: -0.7s; }\n    .chase-dot:nth-child(5):before {\n      animation-delay: -0.7s; }\n  .chase-dot:nth-child(6) {\n    animation-delay: -0.6s; }\n    .chase-dot:nth-child(6):before {\n      animation-delay: -0.6s; }\n\n@keyframes spinner-chase {\n  100% {\n    transform: rotate(360deg); } }\n\n@keyframes chase-dot {\n  80%, 100% {\n    transform: rotate(360deg); } }\n\n@keyframes chase-dot-before {\n  50% {\n    transform: scale(0.4); }\n  100%, 0% {\n    transform: scale(1); } }\n\n.form-check-right {\n  padding-left: 0;\n  display: inline-block;\n  padding-right: 1.5em; }\n  .form-check-right .form-check-input {\n    float: right;\n    margin-left: 0;\n    margin-right: -1.5em; }\n  .form-check-right .form-check-label {\n    display: block; }\n\n.form-check {\n  position: relative;\n  text-align: left; }\n\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0; }\n\n.dash-summary {\n  border-top: 1px solid #eaedf1; }\n\n.dash-main-border {\n  border-bottom: 1px solid #eaedf1; }\n\n.dash-info-widget {\n  background: #f9fafc; }\n\n.dash-goal {\n  border-left: 1px solid #eaedf1; }\n\n@media (max-width: 768px) {\n  .dash-goal {\n    border-left: none; } }\n\n.carousel-indicators {\n  bottom: -20px; }\n  .carousel-indicators button {\n    background-color: #525ce5 !important;\n    width: 10px !important;\n    height: 10px !important;\n    border-radius: 50% !important;\n    margin: 5px;\n    opacity: 0.5; }\n\n.mini-stats-wid .mini-stat-icon {\n  overflow: hidden;\n  position: relative; }\n  .mini-stats-wid .mini-stat-icon:before, .mini-stats-wid .mini-stat-icon:after {\n    content: \"\";\n    position: absolute;\n    width: 8px;\n    height: 54px;\n    background-color: rgba(255, 255, 255, 0.1);\n    left: 16px;\n    transform: rotate(32deg);\n    top: -5px;\n    transition: all 0.4s; }\n  .mini-stats-wid .mini-stat-icon::after {\n    left: -12px;\n    width: 12px;\n    transition: all 0.2s; }\n\n.mini-stats-wid:hover .mini-stat-icon::after {\n  left: 60px; }\n\n.inbox-wid .inbox-list-item a {\n  color: #495057;\n  display: block;\n  padding: 11px 0px;\n  border-bottom: 1px solid #edf1f5; }\n\n.inbox-wid .inbox-list-item:first-child a {\n  padding-top: 0px; }\n\n.inbox-wid .inbox-list-item:last-child a {\n  border-bottom: 0px; }\n\n.activity-border:before {\n  content: \"\";\n  position: absolute;\n  height: 38px;\n  border-left: 2px dashed #eaedf1;\n  top: 40px;\n  left: 0px; }\n\n.activity-wid {\n  margin-left: 16px; }\n  .activity-wid .activity-list {\n    position: relative;\n    padding: 0 0 33px 30px; }\n    .activity-wid .activity-list .activity-icon {\n      position: absolute;\n      left: -20px;\n      top: 0px;\n      z-index: 2; }\n    .activity-wid .activity-list:last-child {\n      padding-bottom: 0px; }\n\n.button-items {\n  margin-left: -8px;\n  margin-bottom: -12px; }\n  .button-items .btn {\n    margin-bottom: 12px;\n    margin-left: 8px; }\n\n.mfp-popup-form {\n  max-width: 1140px; }\n\n.bs-example-modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block; }\n\n.icon-demo-content {\n  text-align: center;\n  color: #adb5bd; }\n  .icon-demo-content i {\n    display: block;\n    font-size: 24px;\n    color: #74788d;\n    width: 48px;\n    height: 48px;\n    line-height: 46px;\n    margin: 0px auto;\n    margin-bottom: 16px;\n    border-radius: 4px;\n    border: 1px solid #edf1f5;\n    transition: all 0.4s; }\n  .icon-demo-content .col-lg-4 {\n    margin-top: 24px; }\n    .icon-demo-content .col-lg-4:hover i {\n      background-color: #525ce5;\n      color: #fff; }\n\n.grid-structure .grid-container {\n  background-color: #f9fafc;\n  margin-top: 10px;\n  font-size: .8rem;\n  font-weight: 500;\n  padding: 10px 20px; }\n\n.card-radio {\n  background-color: #fff;\n  border: 2px solid #eaedf1;\n  border-radius: 0.25rem;\n  padding: 1rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap; }\n  .card-radio:hover {\n    cursor: pointer; }\n\n.card-radio-label {\n  display: block; }\n\n.card-radio-input {\n  display: none; }\n  .card-radio-input:checked + .card-radio {\n    border-color: #525ce5 !important; }\n\n.navs-carousel .owl-nav {\n  margin-top: 16px; }\n  .navs-carousel .owl-nav button {\n    width: 30px;\n    height: 30px;\n    line-height: 28px !important;\n    font-size: 20px !important;\n    border-radius: 50% !important;\n    background-color: rgba(82, 92, 229, 0.25) !important;\n    color: #525ce5 !important;\n    margin: 4px 8px !important; }\n\n@media print {\n  .vertical-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-header,\n  .footer {\n    display: none !important; }\n  .card-body,\n  .main-content,\n  .right-bar,\n  .page-content,\n  body {\n    padding: 0;\n    margin: 0; }\n  .card {\n    border: 0; } }\n\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start; }\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit; }\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0; }\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch; }\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto;\n  /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important; }\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none; }\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table; }\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none; }\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0; }\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1; }\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden; }\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none; }\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all; }\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px; }\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear; }\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear; }\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px; }\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px; }\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px; }\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px; }\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto; }\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0; }\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll; }\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none; }\n\n.custom-scroll {\n  height: 100%; }\n\n.fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase; }\n\n.fc th.fc-widget-header {\n  background: #f9fafc;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase;\n  font-weight: 600; }\n\n.fc-unthemed .fc-content,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-list-heading td,\n.fc-unthemed .fc-list-view,\n.fc-unthemed .fc-popover,\n.fc-unthemed .fc-row,\n.fc-unthemed tbody,\n.fc-unthemed td,\n.fc-unthemed th,\n.fc-unthemed thead {\n  border-color: #f9fafc; }\n\n.fc-unthemed td.fc-today {\n  background: #fafbfc; }\n\n.fc-button {\n  background: #fff;\n  border-color: #edf1f5;\n  color: #495057;\n  text-transform: capitalize;\n  box-shadow: none;\n  padding: 6px 12px !important;\n  height: auto !important; }\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #525ce5;\n  color: #fff;\n  text-shadow: none; }\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center; }\n\n#external-events .external-event {\n  text-align: left !important;\n  padding: 8px 16px; }\n\n.fc-event, .fc-event-dot {\n  background-color: #525ce5; }\n\n.fc-event .fc-content {\n  color: #fff; }\n\n.fc .table-bordered td, .fc .table-bordered th {\n  border-color: #edf1f5; }\n\n@media (max-width: 575.98px) {\n  .fc .fc-toolbar {\n    display: block; } }\n\n.fc .fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase; }\n\n@media (max-width: 767.98px) {\n  .fc .fc-toolbar .fc-left,\n  .fc .fc-toolbar .fc-right,\n  .fc .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    text-align: center;\n    clear: both;\n    margin: 10px 0; }\n  .fc .fc-toolbar > * > * {\n    float: none; }\n  .fc .fc-toolbar .fc-today-button {\n    display: none; } }\n\n.fc .fc-toolbar .btn {\n  text-transform: capitalize; }\n\n.fc-bootstrap .fc-today.alert-info {\n  background-color: #eaedf1; }\n\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\n  background-color: #000 !important; }\n\n[dir=\"rtl\"] .fc-header-toolbar {\n  direction: ltr !important; }\n\n[dir=\"rtl\"] .fc-toolbar > * > :not(:first-child) {\n  margin-left: .75em; }\n\n.sp-container {\n  background-color: #fff;\n  z-index: 1000; }\n  .sp-container button {\n    padding: .25rem .5rem;\n    font-size: .71094rem;\n    border-radius: .2rem;\n    font-weight: 400;\n    color: #343a40; }\n    .sp-container button.sp-palette-toggle {\n      background-color: #f9fafc; }\n    .sp-container button.sp-choose {\n      background-color: #23c58f;\n      margin-left: 5px;\n      margin-right: 0; }\n\n.sp-palette-container {\n  border-right: 1px solid #edf1f5; }\n\n.sp-input {\n  background-color: #fff;\n  border-color: #ced4da !important;\n  color: #495057; }\n  .sp-input:focus {\n    outline: none; }\n\n[dir=\"rtl\"] .sp-alpha {\n  direction: rtl; }\n\n[dir=\"rtl\"] .sp-original-input-container .sp-add-on {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 4px !important;\n  border-bottom-left-radius: 4px !important; }\n\n[dir=\"rtl\"] input.spectrum.with-add-on {\n  border: 1px solid #ced4da;\n  border-left: 0;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem; }\n\n#session-timeout-dialog .close {\n  display: none; }\n\n#session-timeout-dialog .countdown-holder {\n  color: #f14e4e;\n  font-weight: 500; }\n\n#session-timeout-dialog .btn-default {\n  background-color: #fff;\n  color: #f14e4e;\n  box-shadow: none; }\n\n.irs {\n  font-family: var(--bs-font-sans-serif); }\n\n.irs--round .irs-bar,\n.irs--round .irs-to,\n.irs--round .irs-from,\n.irs--round .irs-single {\n  background: #525ce5 !important;\n  font-size: 11px; }\n\n.irs--round .irs-to:before,\n.irs--round .irs-from:before,\n.irs--round .irs-single:before {\n  display: none; }\n\n.irs--round .irs-line {\n  background: #eaedf1;\n  border-color: #eaedf1; }\n\n.irs--round .irs-grid-text {\n  font-size: 11px;\n  color: #adb5bd; }\n\n.irs--round .irs-min,\n.irs--round .irs-max {\n  color: #adb5bd;\n  background: #eaedf1;\n  font-size: 11px; }\n\n.irs--round .irs-handle {\n  border: 2px solid #525ce5;\n  width: 10px;\n  height: 16px;\n  top: 29px;\n  background-color: #fff !important; }\n\n.swal2-container .swal2-title {\n  font-size: 24px;\n  font-weight: 500; }\n\n.swal2-content {\n  font-size: 16px; }\n\n.swal2-icon.swal2-question {\n  border-color: #5ba4e5;\n  color: #5ba4e5; }\n\n.swal2-icon.swal2-success [class^=swal2-success-line] {\n  background-color: #23c58f; }\n\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: rgba(35, 197, 143, 0.3); }\n\n.swal2-icon.swal2-warning {\n  border-color: #eeb148;\n  color: #eeb148; }\n\n.swal2-styled:focus {\n  box-shadow: none; }\n\n.swal2-progress-steps .swal2-progress-step {\n  background: #525ce5; }\n  .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n    background: #525ce5; }\n    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n      background: rgba(82, 92, 229, 0.3); }\n\n.swal2-progress-steps .swal2-progress-step-line {\n  background: #525ce5; }\n\n.swal2-loader {\n  border-color: #525ce5 transparent #525ce5 transparent; }\n\n.symbol {\n  border-color: #fff; }\n\n.rating-symbol-background, .rating-symbol-foreground {\n  font-size: 24px; }\n\n.rating-symbol-foreground {\n  top: 0px; }\n\n.rating-star > span {\n  display: inline-block;\n  vertical-align: middle; }\n  .rating-star > span.badge {\n    margin-left: 4px; }\n\n.error {\n  color: #f14e4e; }\n\n.parsley-error {\n  border-color: #f14e4e; }\n\n.parsley-errors-list {\n  display: none;\n  margin: 0;\n  padding: 0; }\n  .parsley-errors-list.filled {\n    display: block; }\n  .parsley-errors-list > li {\n    font-size: 12px;\n    list-style: none;\n    color: #f14e4e;\n    margin-top: 5px; }\n\n.select2-container {\n  display: block; }\n  .select2-container .select2-selection--single {\n    background-color: #fff;\n    border: 1px solid #ced4da;\n    height: 38px; }\n    .select2-container .select2-selection--single:focus {\n      outline: none; }\n    .select2-container .select2-selection--single .select2-selection__rendered {\n      line-height: 36px;\n      padding-left: 12px;\n      color: #495057; }\n    .select2-container .select2-selection--single .select2-selection__arrow {\n      height: 34px;\n      width: 34px;\n      right: 3px; }\n      .select2-container .select2-selection--single .select2-selection__arrow b {\n        border-color: #adb5bd transparent transparent transparent;\n        border-width: 6px 6px 0 6px; }\n    .select2-container .select2-selection--single .select2-selection__placeholder {\n      color: #495057; }\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #adb5bd transparent !important;\n  border-width: 0 6px 6px 6px !important; }\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: #fff; }\n  .select2-container--default .select2-search--dropdown .select2-search__field {\n    border: 1px solid #ced4da;\n    background-color: #fff;\n    color: #74788d;\n    outline: none; }\n\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #525ce5; }\n\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #f9fafc;\n  color: #16181b; }\n  .select2-container--default .select2-results__option[aria-selected=true]:hover {\n    background-color: #525ce5;\n    color: #fff; }\n\n.select2-results__option {\n  padding: 6px 12px; }\n\n.select2-dropdown {\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n.select2-search input {\n  border: 1px solid #eaedf1; }\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: #fff;\n  border: 1px solid #ced4da !important; }\n  .select2-container .select2-selection--multiple .select2-selection__rendered {\n    padding: 2px 10px; }\n  .select2-container .select2-selection--multiple .select2-search__field {\n    border: 0;\n    color: #495057; }\n    .select2-container .select2-selection--multiple .select2-search__field::placeholder {\n      color: #495057; }\n  .select2-container .select2-selection--multiple .select2-selection__choice {\n    background-color: #edf1f5;\n    border: 1px solid #eaedf1;\n    border-radius: 1px;\n    padding: 0 7px; }\n\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ced4da; }\n\n.select2-container--default .select2-results__group {\n  font-weight: 600; }\n\n.select2-result-repository__avatar {\n  float: left;\n  width: 60px;\n  margin-right: 10px; }\n  .select2-result-repository__avatar img {\n    width: 100%;\n    height: auto;\n    border-radius: 2px; }\n\n.select2-result-repository__statistics {\n  margin-top: 7px; }\n\n.select2-result-repository__forks,\n.select2-result-repository__stargazers,\n.select2-result-repository__watchers {\n  display: inline-block;\n  font-size: 11px;\n  margin-right: 1em;\n  color: #adb5bd; }\n  .select2-result-repository__forks .fa,\n  .select2-result-repository__stargazers .fa,\n  .select2-result-repository__watchers .fa {\n    margin-right: 4px; }\n    .select2-result-repository__forks .fa.fa-flash::before,\n    .select2-result-repository__stargazers .fa.fa-flash::before,\n    .select2-result-repository__watchers .fa.fa-flash::before {\n      content: \"\\f0e7\";\n      font-family: 'Font Awesome 5 Free'; }\n\n.select2-results__option--highlighted .select2-result-repository__forks,\n.select2-results__option--highlighted .select2-result-repository__stargazers,\n.select2-results__option--highlighted .select2-result-repository__watchers {\n  color: rgba(255, 255, 255, 0.8); }\n\n.select2-result-repository__meta {\n  overflow: hidden; }\n\n.img-flag {\n  margin-right: 7px;\n  height: 15px;\n  width: 18px; }\n\n/* CSS Switch */\ninput[switch] {\n  display: none; }\n  input[switch] + label {\n    font-size: 1em;\n    line-height: 1;\n    width: 56px;\n    height: 24px;\n    background-color: #ced4da;\n    background-image: none;\n    border-radius: 2rem;\n    padding: 0.16667rem;\n    cursor: pointer;\n    display: inline-block;\n    text-align: center;\n    position: relative;\n    font-weight: 500;\n    transition: all 0.1s ease-in-out; }\n    input[switch] + label:before {\n      color: #343a40;\n      content: attr(data-off-label);\n      display: block;\n      font-family: inherit;\n      font-weight: 500;\n      font-size: 12px;\n      line-height: 21px;\n      position: absolute;\n      right: 1px;\n      margin: 3px;\n      top: -2px;\n      text-align: center;\n      min-width: 1.66667rem;\n      overflow: hidden;\n      transition: all 0.1s ease-in-out; }\n    input[switch] + label:after {\n      content: '';\n      position: absolute;\n      left: 3px;\n      background-color: #edf1f5;\n      box-shadow: none;\n      border-radius: 2rem;\n      height: 20px;\n      width: 20px;\n      top: 2px;\n      transition: all 0.1s ease-in-out; }\n  input[switch]:checked + label {\n    background-color: #525ce5; }\n\ninput[switch]:checked + label {\n  background-color: #525ce5; }\n  input[switch]:checked + label:before {\n    color: #fff;\n    content: attr(data-on-label);\n    right: auto;\n    left: 3px; }\n  input[switch]:checked + label:after {\n    left: 33px;\n    background-color: #edf1f5; }\n\ninput[switch=\"bool\"] + label {\n  background-color: #f14e4e; }\n\ninput[switch=\"bool\"] + label:before, input[switch=\"bool\"]:checked + label:before,\ninput[switch=\"default\"]:checked + label:before {\n  color: #fff; }\n\ninput[switch=\"bool\"]:checked + label {\n  background-color: #23c58f; }\n\ninput[switch=\"default\"]:checked + label {\n  background-color: #a2a2a2; }\n\ninput[switch=\"primary\"]:checked + label {\n  background-color: #525ce5; }\n\ninput[switch=\"success\"]:checked + label {\n  background-color: #23c58f; }\n\ninput[switch=\"info\"]:checked + label {\n  background-color: #5ba4e5; }\n\ninput[switch=\"warning\"]:checked + label {\n  background-color: #eeb148; }\n\ninput[switch=\"danger\"]:checked + label {\n  background-color: #f14e4e; }\n\ninput[switch=\"dark\"]:checked + label {\n  background-color: #343a40; }\n\n.square-switch {\n  margin-right: 7px; }\n  .square-switch input[switch] + label, .square-switch input[switch] + label:after {\n    border-radius: 4px; }\n\n.datepicker {\n  border: 1px solid #f9fafc;\n  padding: 8px;\n  z-index: 999 !important; }\n  .datepicker table tr th {\n    font-weight: 500; }\n  .datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {\n    background-color: #525ce5 !important;\n    background-image: none;\n    box-shadow: none;\n    color: #fff !important; }\n  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n  .datepicker table tr td span.focused,\n  .datepicker table tr td span:hover {\n    background: #edf1f5; }\n  .datepicker table tr td.new, .datepicker table tr td.old,\n  .datepicker table tr td span.new,\n  .datepicker table tr td span.old {\n    color: #adb5bd;\n    opacity: 0.6; }\n  .datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n    background-color: #eaedf1; }\n\n.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {\n  padding: 7px; }\n\n.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.bootstrap-touchspin .input-group-btn-vertical {\n  right: 0 !important;\n  left: 100% !important; }\n\n.bootstrap-touchspin .bootstrap-touchspin-up {\n  border-top-right-radius: 4px !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n  border-bottom-left-radius: 0 !important; }\n\n.bootstrap-touchspin .bootstrap-touchspin-down {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 4px !important;\n  border-top-left-radius: 0 !important;\n  border-bottom-left-radius: 0 !important; }\n\n.table-bordered {\n  border: 1px solid #edf1f5; }\n\ndiv.dataTables_wrapper div.dataTables_filter {\n  text-align: right; }\n  div.dataTables_wrapper div.dataTables_filter input {\n    margin-left: 0.5em;\n    margin-right: 0; }\n\n.tox-tinymce {\n  border: 2px solid #eaedf1 !important; }\n\n.tox .tox-statusbar {\n  border-top: 1px solid #eaedf1 !important; }\n\n.tox .tox-menubar,\n.tox .tox-edit-area__iframe,\n.tox .tox-statusbar {\n  background-color: #fff !important;\n  background: none !important; }\n\n.tox .tox-mbtn {\n  color: #495057 !important; }\n  .tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n    background-color: #eaedf1 !important; }\n\n.tox .tox-tbtn:hover {\n  background-color: #eaedf1 !important; }\n\n.tox .tox-toolbar__primary {\n  border-color: #eaedf1 !important; }\n\n.tox .tox-toolbar,\n.tox .tox-toolbar__overflow,\n.tox .tox-toolbar__primary {\n  background: #eaedf1 !important; }\n\n.tox .tox-tbtn {\n  color: #495057 !important; }\n  .tox .tox-tbtn svg {\n    fill: #495057 !important; }\n\n.tox .tox-edit-area__iframe {\n  background-color: #fff !important; }\n\n.tox .tox-statusbar a,\n.tox .tox-statusbar__path-item,\n.tox .tox-statusbar__wordcount {\n  color: #495057 !important; }\n\n.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n  border-right: 1px solid #dbe0e7 !important; }\n\n.tox-tinymce-aux {\n  z-index: 1000 !important; }\n\n/* Dropzone */\n.dropzone {\n  min-height: 230px;\n  border: 2px dashed #ced4da;\n  background: #fff;\n  border-radius: 6px; }\n  .dropzone .dz-message {\n    font-size: 24px;\n    width: 100%; }\n\n.twitter-bs-wizard .twitter-bs-wizard-nav {\n  position: relative; }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {\n    content: \"\";\n    width: 189px;\n    height: 2px;\n    background: rgba(82, 92, 229, 0.2);\n    position: absolute;\n    top: 26px;\n    margin-left: 100px; }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n    display: inline-block;\n    border-radius: 30px;\n    padding: 4px 0px;\n    width: 200px;\n    line-height: 34px;\n    color: #525ce5;\n    text-align: center;\n    position: relative;\n    background-color: rgba(82, 92, 229, 0.2); }\n    @media (max-width: 991.98px) {\n      .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n        display: block;\n        margin: 0 auto 8px !important;\n        width: 170px; } }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n    display: block;\n    margin-top: 8px;\n    font-weight: 600; }\n    @media (max-width: 575.98px) {\n      .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n        display: none; } }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {\n    background-color: transparent;\n    color: #495057; }\n    .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {\n      background-color: #525ce5;\n      color: #fff; }\n\n.twitter-bs-wizard .twitter-bs-wizard-pager-link {\n  padding-top: 24px;\n  padding-left: 0;\n  list-style: none;\n  margin-bottom: 0; }\n  .twitter-bs-wizard .twitter-bs-wizard-pager-link li {\n    display: inline-block; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li a {\n      display: inline-block;\n      padding: .47rem .75rem;\n      background-color: #525ce5;\n      color: #fff;\n      border-radius: .25rem; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {\n      cursor: not-allowed;\n      background-color: #757dea; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {\n      float: right; }\n\n.twitter-bs-wizard-tab-content {\n  padding-top: 24px;\n  min-height: 262px; }\n\n@media (max-width: 1024px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {\n    background: transparent !important; } }\n\n.table-rep-plugin .btn-toolbar {\n  display: block; }\n\n.table-rep-plugin .table-responsive {\n  border: none !important; }\n\n.table-rep-plugin .btn-group .btn-default {\n  background-color: #74788d;\n  color: #f9fafc;\n  border: 1px solid #74788d; }\n  .table-rep-plugin .btn-group .btn-default.btn-primary {\n    background-color: #525ce5;\n    border-color: #525ce5;\n    color: #fff;\n    box-shadow: 0 0 0 2px rgba(82, 92, 229, 0.5); }\n\n.table-rep-plugin .btn-group.pull-right {\n  float: right; }\n  .table-rep-plugin .btn-group.pull-right .dropdown-menu {\n    right: 0;\n    transform: none !important;\n    top: 100% !important; }\n\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal; }\n\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px;\n  color: #495057 !important; }\n  .table-rep-plugin .checkbox-row:hover {\n    background-color: #f4f6f9 !important; }\n  .table-rep-plugin .checkbox-row label {\n    display: inline-block;\n    padding-left: 5px;\n    position: relative; }\n    .table-rep-plugin .checkbox-row label::before {\n      -o-transition: 0.3s ease-in-out;\n      -webkit-transition: 0.3s ease-in-out;\n      background-color: #fff;\n      border-radius: 3px;\n      border: 1px solid #eaedf1;\n      content: \"\";\n      display: inline-block;\n      height: 17px;\n      left: 0;\n      margin-left: -20px;\n      position: absolute;\n      transition: 0.3s ease-in-out;\n      width: 17px;\n      outline: none !important; }\n    .table-rep-plugin .checkbox-row label::after {\n      color: #edf1f5;\n      display: inline-block;\n      font-size: 11px;\n      height: 16px;\n      left: 0;\n      margin-left: -20px;\n      padding-left: 3px;\n      padding-top: 1px;\n      position: absolute;\n      top: -1px;\n      width: 16px; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"] {\n    cursor: pointer;\n    opacity: 0;\n    z-index: 1;\n    outline: none !important; }\n    .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label {\n      opacity: 0.65; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:focus + label::before {\n    outline-offset: -2px;\n    outline: none; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    content: \"\\f00c\";\n    font-family: 'Font Awesome 5 Free';\n    font-weight: 900; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label::before {\n    background-color: #f9fafc;\n    cursor: not-allowed; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::before {\n    background-color: #525ce5;\n    border-color: #525ce5; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    color: #fff; }\n\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important;\n  background-color: #525ce5; }\n  .table-rep-plugin .fixed-solution .sticky-table-header table {\n    color: #fff; }\n\n.table-rep-plugin table.focus-on tbody tr.focused th,\n.table-rep-plugin table.focus-on tbody tr.focused td,\n.table-rep-plugin .sticky-table-header {\n  background: #525ce5;\n  border-color: #525ce5;\n  color: #fff; }\n  .table-rep-plugin table.focus-on tbody tr.focused th table,\n  .table-rep-plugin table.focus-on tbody tr.focused td table,\n  .table-rep-plugin .sticky-table-header table {\n    color: #fff; }\n\n@media (min-width: 992px) {\n  body[data-layout=\"horizontal\"] .fixed-solution .sticky-table-header {\n    top: 148px !important; } }\n\n.table-striped > tbody > tr:nth-of-type(odd).focused {\n  box-shadow: none !important; }\n  .table-striped > tbody > tr:nth-of-type(odd).focused td, .table-striped > tbody > tr:nth-of-type(odd).focused th {\n    box-shadow: none !important; }\n\n.table-edits input, .table-edits select {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding: 0.25rem 0.5rem;\n  border: 1px solid #ced4da;\n  background-color: #fff;\n  color: #495057;\n  border-radius: 0.25rem; }\n  .table-edits input:focus, .table-edits select:focus {\n    outline: none;\n    border-color: #b1bbc4; }\n\n.apex-charts {\n  min-height: 10px !important; }\n  .apex-charts text {\n    font-family: var(--bs-font-sans-serif) !important;\n    fill: #adb5bd; }\n  .apex-charts .apexcharts-canvas {\n    margin: 0 auto; }\n\n.apexcharts-tooltip-title,\n.apexcharts-tooltip-text {\n  font-family: var(--bs-font-sans-serif) !important; }\n\n.apexcharts-legend-series {\n  font-weight: 500; }\n\n.apexcharts-gridline {\n  pointer-events: none;\n  stroke: #f8f9fa; }\n\n.apexcharts-legend-text {\n  color: #74788d !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-size: 13px !important; }\n\n.apexcharts-pie-label {\n  fill: #fff !important; }\n\n.apexcharts-yaxis text,\n.apexcharts-xaxis text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #adb5bd; }\n\n.ct-golden-section:before {\n  float: none; }\n\n.ct-chart {\n  max-height: 300px; }\n  .ct-chart .ct-label {\n    fill: #adb5bd;\n    color: #adb5bd;\n    font-size: 12px;\n    line-height: 1; }\n\n.ct-chart.simple-pie-chart-chartist .ct-label {\n  color: #fff;\n  fill: #fff;\n  font-size: 16px; }\n\n.ct-grid {\n  stroke: rgba(52, 58, 64, 0.1); }\n\n.ct-chart .ct-series.ct-series-a .ct-bar,\n.ct-chart .ct-series.ct-series-a .ct-line,\n.ct-chart .ct-series.ct-series-a .ct-point,\n.ct-chart .ct-series.ct-series-a .ct-slice-donut {\n  stroke: #525ce5; }\n\n.ct-chart .ct-series.ct-series-b .ct-bar,\n.ct-chart .ct-series.ct-series-b .ct-line,\n.ct-chart .ct-series.ct-series-b .ct-point,\n.ct-chart .ct-series.ct-series-b .ct-slice-donut {\n  stroke: #23c58f; }\n\n.ct-chart .ct-series.ct-series-c .ct-bar,\n.ct-chart .ct-series.ct-series-c .ct-line,\n.ct-chart .ct-series.ct-series-c .ct-point,\n.ct-chart .ct-series.ct-series-c .ct-slice-donut {\n  stroke: #f14e4e; }\n\n.ct-chart .ct-series.ct-series-d .ct-bar,\n.ct-chart .ct-series.ct-series-d .ct-line,\n.ct-chart .ct-series.ct-series-d .ct-point,\n.ct-chart .ct-series.ct-series-d .ct-slice-donut {\n  stroke: #5ba4e5; }\n\n.ct-chart .ct-series.ct-series-e .ct-bar,\n.ct-chart .ct-series.ct-series-e .ct-line,\n.ct-chart .ct-series.ct-series-e .ct-point,\n.ct-chart .ct-series.ct-series-e .ct-slice-donut {\n  stroke: #23c58f; }\n\n.ct-chart .ct-series.ct-series-f .ct-bar,\n.ct-chart .ct-series.ct-series-f .ct-line,\n.ct-chart .ct-series.ct-series-f .ct-point,\n.ct-chart .ct-series.ct-series-f .ct-slice-donut {\n  stroke: #343a40; }\n\n.ct-chart .ct-series.ct-series-g .ct-bar,\n.ct-chart .ct-series.ct-series-g .ct-line,\n.ct-chart .ct-series.ct-series-g .ct-point,\n.ct-chart .ct-series.ct-series-g .ct-slice-donut {\n  stroke: #6f42c1; }\n\n.ct-series-a .ct-area,\n.ct-series-a .ct-slice-pie {\n  fill: #525ce5; }\n\n.ct-series-b .ct-area,\n.ct-series-b .ct-slice-pie {\n  fill: #23c58f; }\n\n.ct-series-c .ct-area,\n.ct-series-c .ct-slice-pie {\n  fill: #eeb148; }\n\n.ct-series-d .ct-area,\n.ct-series-d .ct-slice-pie {\n  fill: #23c58f; }\n\n.ct-area {\n  fill-opacity: .33; }\n\n.chartist-tooltip {\n  position: absolute;\n  display: inline-block;\n  opacity: 0;\n  min-width: 10px;\n  padding: 2px 10px;\n  border-radius: 3px;\n  background: #343a40;\n  color: #eaedf1;\n  text-align: center;\n  pointer-events: none;\n  z-index: 1;\n  transition: opacity .2s linear; }\n  .chartist-tooltip.tooltip-show {\n    opacity: 1; }\n\n.ct-line {\n  stroke-width: 3px; }\n\n.ct-point {\n  stroke-width: 7px; }\n\n/* Flot chart */\n.flotTip {\n  padding: 8px 12px !important;\n  background-color: #343a40 !important;\n  border: 1px solid #343a40 !important;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  z-index: 100;\n  color: #edf1f5;\n  opacity: 1;\n  border-radius: 3px !important;\n  font-size: 14px !important; }\n\n.legend div {\n  background-color: transparent !important; }\n\n.legend tr {\n  height: 30px; }\n\n.legendLabel {\n  padding-left: 5px;\n  line-height: 10px;\n  padding-right: 10px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #adb5bd; }\n\n.legendColorBox div {\n  border-radius: 3px; }\n  .legendColorBox div div {\n    border-radius: 3px; }\n\n.float-lable-box table {\n  margin: 0 auto; }\n\n@media (max-width: 575.98px) {\n  .legendLabel {\n    display: none; } }\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #343a40 !important;\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #212529 !important; }\n\n.jqsfield {\n  color: #edf1f5 !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-weight: 500 !important; }\n\n.gmaps, .gmaps-panaroma {\n  height: 300px;\n  background: #f9fafc;\n  border-radius: 3px; }\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #525ce5;\n  border-radius: 4px;\n  padding: 10px 20px; }\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute; }\n  .gmaps-overlay_arrow.above {\n    bottom: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-top: 16px solid #525ce5; }\n  .gmaps-overlay_arrow.below {\n    top: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-bottom: 16px solid #525ce5; }\n\n.jvectormap-label {\n  border: none;\n  background: #343a40;\n  color: #f9fafc;\n  font-family: var(--bs-font-sans-serif);\n  font-size: 0.875rem;\n  padding: 5px 8px; }\n\n.editable-input .form-control {\n  display: inline-block; }\n\n.editable-buttons {\n  margin-left: 7px; }\n  .editable-buttons .editable-cancel {\n    margin-left: 7px; }\n\n.home-btn {\n  position: absolute;\n  top: 15px;\n  right: 25px; }\n\n.home-center {\n  display: table;\n  width: 100%;\n  height: 100%; }\n\n.home-desc-center {\n  display: table-cell;\n  vertical-align: middle; }\n\n.authentication-bg {\n  background-image: url(../images/title-img.png);\n  height: 100vh;\n  background-size: cover;\n  background-position: center; }\n\n.authentication-bg .bg-overlay {\n  background-color: #525ce5; }\n\n.error-page {\n  text-transform: uppercase;\n  background: repeating-linear-gradient(45deg, #525ce5, #525ce5 10px, #23c58f 10px, #23c58f 20px);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  font-size: 120px;\n  line-height: .8;\n  position: relative; }\n\n.faq-icon i {\n  width: 30px;\n  height: 30px;\n  line-height: 28px;\n  border: 1px solid;\n  border-radius: 50%;\n  text-align: center;\n  float: right;\n  font-size: 16px;\n  display: inline-block; }\n\n.faq-icon:after {\n  content: \"\";\n  position: absolute;\n  width: 30px;\n  height: 30px;\n  opacity: 0.2;\n  right: 50px;\n  margin-top: -10px;\n  border-radius: 50%;\n  background: #525ce5; }\n\n.search-box .form-control {\n  border-radius: 30px;\n  padding-left: 40px;\n  border: 1px solid #eaedf1; }\n\n.search-box .search-icon {\n  font-size: 16px;\n  position: absolute;\n  left: 13px;\n  top: 0;\n  line-height: 38px; }\n\n.categories-group-list {\n  display: block;\n  color: #343a40;\n  font-weight: 500;\n  padding: 8px 16px; }\n  .categories-group-list[aria-expanded=\"true\"] {\n    background-color: #eaedf1; }\n  .categories-group-list:last-child {\n    border: 0; }\n  .categories-group-list:hover {\n    color: #343a40; }\n\n.categories-list {\n  padding: 8px 0px; }\n  .categories-list li a {\n    display: block;\n    padding: 4px 16px;\n    color: #495057; }\n  .categories-list li.active a {\n    color: #525ce5; }\n\n.product-detai-imgs .nav .nav-link {\n  margin: 7px 0px; }\n  .product-detai-imgs .nav .nav-link.active {\n    background-color: #eaedf1; }\n\n.product-color a {\n  display: inline-block;\n  text-align: center;\n  color: #495057; }\n  .product-color a .product-color-item {\n    margin: 7px;\n    border: 2px solid #edf1f5;\n    border-radius: 4px; }\n  .product-color a.active, .product-color a:hover {\n    color: #525ce5; }\n    .product-color a.active .product-color-item, .product-color a:hover .product-color-item {\n      border-color: #525ce5 !important; }\n\n.product-track {\n  border: 1px solid #edf1f5; }\n\n.ecommerce-sortby-list li {\n  color: #343a40; }\n  .ecommerce-sortby-list li a {\n    color: #495057;\n    padding: 4px; }\n  .ecommerce-sortby-list li.active a {\n    color: #525ce5; }\n\n.product-img {\n  position: relative; }\n  .product-img .product-ribbon {\n    position: absolute;\n    top: 0;\n    left: 0px;\n    padding: 6px 8px;\n    border-radius: 50% 50% 25% 75%/44% 68% 32% 56%;\n    width: 62px;\n    height: 60px;\n    color: #fff;\n    font-size: 15px;\n    text-align: center; }\n  .product-img .product-like {\n    position: absolute;\n    top: 0;\n    right: 0; }\n    .product-img .product-like a {\n      display: inline-block;\n      width: 40px;\n      height: 40px;\n      border: 2px solid #eaedf1;\n      line-height: 38px;\n      border-radius: 50%;\n      text-align: center;\n      color: #adb5bd; }\n\n.product-detail .nav-pills .nav-link {\n  margin-bottom: 7px; }\n  .product-detail .nav-pills .nav-link.active {\n    background-color: #eaedf1; }\n  .product-detail .nav-pills .nav-link .tab-img {\n    width: 5rem; }\n\n.product-detail .product-img {\n  border: 1px solid #edf1f5;\n  padding: 24px; }\n\n.product-desc-list li {\n  padding: 4px 0px; }\n\n.product-review-link .list-inline-item a {\n  color: #74788d; }\n\n.product-review-link .list-inline-item:not(:last-child) {\n  margin-right: 14px; }\n\n.product-cart-touchspin {\n  border: 1px solid #ced4da;\n  background-color: #fff;\n  border-radius: 0.25rem; }\n  .product-cart-touchspin .form-control {\n    border-color: transparent;\n    height: 32px; }\n  .product-cart-touchspin .input-group-btn .btn {\n    background-color: transparent !important;\n    border-color: transparent !important;\n    color: #525ce5 !important;\n    font-size: 16px;\n    padding: 3px 12px;\n    box-shadow: none; }\n\n.shipping-address {\n  box-shadow: none; }\n  .shipping-address.active {\n    border-color: #525ce5 !important; }\n\n.twitter-bs-wizard .chackout-border:before {\n  content: \"\";\n  width: 139px;\n  height: 2px;\n  background: rgba(82, 92, 229, 0.2);\n  position: absolute;\n  top: 26px;\n  margin-left: 100px; }\n\n.twitter-bs-wizard .add-product-border:before {\n  content: \"\";\n  width: 324px;\n  height: 2px;\n  background: rgba(82, 92, 229, 0.2);\n  position: absolute;\n  top: 26px;\n  margin-left: 100px; }\n\n@media (max-width: 1024px) {\n  .twitter-bs-wizard .chackout-border, .twitter-bs-wizard .add-product-border {\n    width: 180px; }\n    .twitter-bs-wizard .chackout-border:before, .twitter-bs-wizard .add-product-border:before {\n      background: transparent !important; } }\n\n/* ==============\r\n  Email\r\n===================*/\n.email-leftbar {\n  width: 236px;\n  float: left;\n  padding: 20px;\n  border-radius: 5px; }\n\n.email-rightbar {\n  margin-left: 260px; }\n\n.chat-user-box p.user-title {\n  color: #343a40;\n  font-weight: 600; }\n\n.chat-user-box p {\n  font-size: 12px; }\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none;\n    width: 100%; }\n  .email-rightbar {\n    margin: 0; } }\n\n.mail-list a {\n  display: block;\n  color: #74788d;\n  line-height: 24px;\n  padding: 8px 5px; }\n  .mail-list a.active {\n    color: #f14e4e;\n    font-weight: 500; }\n\n.message-list {\n  display: block;\n  padding-left: 0; }\n  .message-list li {\n    position: relative;\n    display: block;\n    height: 50px;\n    line-height: 50px;\n    cursor: default;\n    transition-duration: .3s; }\n    .message-list li a {\n      color: #74788d; }\n    .message-list li:hover {\n      background: #eaedf1;\n      transition-duration: .05s; }\n    .message-list li .col-mail {\n      float: left;\n      position: relative; }\n    .message-list li .col-mail-1 {\n      width: 320px; }\n      .message-list li .col-mail-1 .star-toggle,\n      .message-list li .col-mail-1 .checkbox-wrapper-mail,\n      .message-list li .col-mail-1 .dot {\n        display: block;\n        float: left; }\n      .message-list li .col-mail-1 .dot {\n        border: 4px solid transparent;\n        border-radius: 100px;\n        margin: 22px 26px 0;\n        height: 0;\n        width: 0;\n        line-height: 0;\n        font-size: 0; }\n      .message-list li .col-mail-1 .checkbox-wrapper-mail {\n        margin: 15px 10px 0 20px; }\n      .message-list li .col-mail-1 .star-toggle {\n        margin-top: 18px;\n        margin-left: 5px; }\n      .message-list li .col-mail-1 .title {\n        position: absolute;\n        top: 0;\n        left: 110px;\n        right: 0;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n        margin-bottom: 0; }\n    .message-list li .col-mail-2 {\n      position: absolute;\n      top: 0;\n      left: 320px;\n      right: 0;\n      bottom: 0; }\n      .message-list li .col-mail-2 .subject,\n      .message-list li .col-mail-2 .date {\n        position: absolute;\n        top: 0; }\n      .message-list li .col-mail-2 .subject {\n        left: 0;\n        right: 200px;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap; }\n      .message-list li .col-mail-2 .date {\n        right: 0;\n        width: 170px;\n        padding-left: 80px; }\n    .message-list li.active, .message-list li.active:hover {\n      box-shadow: inset 3px 0 0 #525ce5; }\n    .message-list li.unread {\n      background-color: #eaedf1;\n      font-weight: 500;\n      color: #292d32; }\n      .message-list li.unread a {\n        color: #292d32;\n        font-weight: 500; }\n  .message-list .checkbox-wrapper-mail {\n    cursor: pointer;\n    height: 20px;\n    width: 20px;\n    position: relative;\n    display: inline-block;\n    box-shadow: inset 0 0 0 1px #ced4da;\n    border-radius: 1px; }\n    .message-list .checkbox-wrapper-mail input {\n      opacity: 0;\n      cursor: pointer; }\n    .message-list .checkbox-wrapper-mail input:checked ~ label {\n      opacity: 1; }\n    .message-list .checkbox-wrapper-mail label {\n      position: absolute;\n      height: 20px;\n      width: 20px;\n      left: 0;\n      cursor: pointer;\n      opacity: 0;\n      margin-bottom: 0;\n      transition-duration: .05s;\n      top: 0; }\n      .message-list .checkbox-wrapper-mail label:before {\n        content: \"\\F012C\";\n        font-family: \"Material Design Icons\";\n        top: 0;\n        height: 20px;\n        color: #292d32;\n        width: 20px;\n        position: absolute;\n        margin-top: -16px;\n        left: 4px;\n        font-size: 13px; }\n\n@media (max-width: 575.98px) {\n  .message-list li .col-mail-1 {\n    width: 200px; } }\n\n@media (min-width: 992px) {\n  .chat-leftsidebar {\n    min-width: 380px; } }\n\n.chat-leftsidebar .chat-leftsidebar-nav .nav {\n  background-color: #fff; }\n\n.chat-noti-dropdown.active:before {\n  content: \"\";\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background-color: #f14e4e;\n  border-radius: 50%;\n  right: 0; }\n\n.chat-noti-dropdown .btn {\n  padding: 6px;\n  box-shadow: none;\n  font-size: 20px; }\n\n.chat-list {\n  margin: 0; }\n  .chat-list li.active a {\n    background-color: #fff;\n    box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n  .chat-list li a {\n    display: block;\n    padding: 14px 16px;\n    color: #74788d;\n    transition: all 0.4s;\n    border: 1px solid #edf1f5;\n    border-radius: 4px;\n    margin-top: 10px; }\n    .chat-list li a:hover {\n      background-color: #fff;\n      box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n.user-chat-nav .dropdown .nav-btn {\n  height: 40px;\n  width: 40px;\n  line-height: 34px;\n  box-shadow: none;\n  padding: 0;\n  font-size: 16px;\n  background-color: #f9fafc;\n  border-radius: 50%; }\n\n.user-chat-nav .dropdown .dropdown-menu {\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  border: 1px solid #edf1f5; }\n\n.chat-conversation li {\n  clear: both; }\n\n.chat-conversation .chat-day-title {\n  position: relative;\n  text-align: center;\n  margin-bottom: 24px; }\n  .chat-conversation .chat-day-title .title {\n    background-color: #fff;\n    position: relative;\n    z-index: 1;\n    padding: 6px 24px; }\n  .chat-conversation .chat-day-title:before {\n    content: \"\";\n    position: absolute;\n    width: 100%;\n    height: 1px;\n    left: 0;\n    right: 0;\n    background-color: #edf1f5;\n    top: 10px; }\n  .chat-conversation .chat-day-title .badge {\n    font-size: 12px; }\n\n.chat-conversation .conversation-list {\n  margin-bottom: 24px;\n  display: inline-block;\n  position: relative; }\n  .chat-conversation .conversation-list .arrow-left {\n    position: relative; }\n    .chat-conversation .conversation-list .arrow-left:before {\n      content: \"\";\n      position: absolute;\n      top: 10px;\n      right: 100%;\n      border: 7px solid transparent;\n      border-right: 7px solid rgba(82, 92, 229, 0.1); }\n  .chat-conversation .conversation-list .ctext-wrap {\n    padding: 12px 24px;\n    background-color: rgba(82, 92, 229, 0.1);\n    border-radius: 8px 8px 8px 0px;\n    overflow: hidden; }\n    .chat-conversation .conversation-list .ctext-wrap .conversation-name {\n      font-weight: 500;\n      color: #525ce5;\n      margin-bottom: 4px;\n      position: relative; }\n  .chat-conversation .conversation-list .dropdown {\n    float: right; }\n    .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n      font-size: 18px;\n      padding: 4px;\n      color: #74788d; }\n      @media (max-width: 575.98px) {\n        .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n          display: none; } }\n    .chat-conversation .conversation-list .dropdown .dropdown-menu {\n      box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n      border: 1px solid #edf1f5; }\n  .chat-conversation .conversation-list .chat-time {\n    font-size: 12px; }\n\n.chat-conversation .right .conversation-list {\n  float: right; }\n  .chat-conversation .right .conversation-list .arrow-right {\n    position: relative; }\n    .chat-conversation .right .conversation-list .arrow-right:before {\n      content: \"\";\n      position: absolute;\n      top: 10px;\n      left: 100%;\n      border: 7px solid transparent;\n      border-left: 7px solid #f9fafc; }\n  .chat-conversation .right .conversation-list .ctext-wrap {\n    background-color: #f9fafc;\n    text-align: right;\n    border-radius: 8px 8px 0px 8px; }\n  .chat-conversation .right .conversation-list .dropdown {\n    float: left; }\n  .chat-conversation .right .conversation-list.last-chat .conversation-list:before {\n    right: 0;\n    left: auto; }\n\n.chat-input-section {\n  border-top: 1px solid #edf1f5; }\n\n.chat-input {\n  border-radius: 30px;\n  background-color: #f9fafc !important;\n  border-color: #f9fafc !important;\n  padding-right: 120px; }\n\n.chat-input-links {\n  position: absolute;\n  right: 16px;\n  top: 50%;\n  transform: translateY(-50%); }\n  .chat-input-links li a {\n    font-size: 16px;\n    line-height: 36px;\n    padding: 0px 4px;\n    display: inline-block; }\n\n@media (max-width: 575.98px) {\n  .chat-send {\n    min-width: auto; } }\n\n.search-box .search-icon {\n  font-size: 16px;\n  position: absolute;\n  left: 13px;\n  top: 2px;\n  font-size: 15px;\n  line-height: 34px; }\n\n.search-box .form-control {\n  padding-left: 40px;\n  border-radius: 5px; }\n\n.counter-number {\n  font-size: 32px;\n  text-align: center; }\n  .counter-number span {\n    font-size: 16px;\n    display: block;\n    padding-top: 7px; }\n\n.coming-box {\n  float: left;\n  width: 21%;\n  padding: 14px 7px;\n  margin: 0px 12px 24px 12px;\n  background-color: #fff;\n  border-radius: 5px;\n  border-radius: 0.25rem;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n@media (max-width: 991.98px) {\n  .coming-box {\n    width: 40%; } }\n\n/************** vertical timeline **************/\n.timeline {\n  position: relative;\n  width: 100%;\n  padding: 30px 0; }\n\n.timeline .timeline-end,\n.timeline .timeline-start,\n.timeline .timeline-year {\n  position: relative;\n  width: 100%;\n  text-align: center;\n  z-index: 1; }\n\n.timeline .timeline-end p,\n.timeline .timeline-start p,\n.timeline .timeline-year p {\n  display: inline-block;\n  width: 80px;\n  height: 80px;\n  margin: 0;\n  padding: 30px 0;\n  text-align: center;\n  background: url(../images/user-img.png);\n  background-color: #525ce5;\n  background-repeat: no-repeat;\n  background-size: cover;\n  border-radius: 100px;\n  color: #fff;\n  text-transform: uppercase; }\n\n.timeline .timeline-year {\n  margin: 30px 0; }\n\n.timeline .timeline-continue {\n  position: relative;\n  width: 100%;\n  padding: 60px 0; }\n  .timeline .timeline-continue:after {\n    position: absolute;\n    content: \"\";\n    width: 1px;\n    height: 100%;\n    top: 0;\n    left: 50%;\n    margin-left: -1px;\n    background: #525ce5; }\n\n.timeline .timeline-date {\n  margin: 40px 10px 0 10px; }\n\n.timeline .row.timeline-left,\n.timeline .row.timeline-right .timeline-date {\n  text-align: right; }\n\n.timeline .row.timeline-right,\n.timeline .row.timeline-left .timeline-date {\n  text-align: left; }\n\n.timeline .timeline-date::after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 14px;\n  height: 14px;\n  top: 45px;\n  background: #525ce5;\n  border-radius: 15px;\n  z-index: 1; }\n\n.timeline .row.timeline-left .timeline-date::after {\n  left: -7px; }\n\n.timeline .row.timeline-right .timeline-date::after {\n  right: -7px; }\n\n.timeline .timeline-box,\n.timeline .timeline-launch {\n  position: relative;\n  display: inline-block;\n  margin: 15px;\n  padding: 20px;\n  border: 1px solid #edf1f5;\n  border-radius: 6px; }\n\n.timeline .timeline-launch {\n  width: 100%;\n  margin: 15px 0;\n  padding: 0;\n  border: none;\n  text-align: center;\n  background: transparent; }\n\n.timeline .timeline-box::after,\n.timeline .timeline-box::before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-style: solid; }\n\n.timeline .row.timeline-left .timeline-box::after,\n.timeline .row.timeline-left .timeline-box::before {\n  left: 100%; }\n\n.timeline .row.timeline-right .timeline-box::after,\n.timeline .row.timeline-right .timeline-box::before {\n  right: 100%; }\n\n.timeline .timeline-launch .timeline-box::after,\n.timeline .timeline-launch .timeline-box::before {\n  left: 50%;\n  margin-left: -10px; }\n\n.timeline .timeline-box::after {\n  top: 26px;\n  border-color: transparent transparent transparent #f9fafc;\n  border-width: 10px; }\n\n.timeline .timeline-box::before {\n  top: 25px;\n  border-color: transparent transparent transparent #edf1f5;\n  border-width: 11px; }\n\n.timeline .row.timeline-right .timeline-box::after {\n  border-color: transparent #f9fafc transparent transparent; }\n\n.timeline .row.timeline-right .timeline-box::before {\n  border-color: transparent #edf1f5 transparent transparent; }\n\n.timeline .timeline-launch .timeline-box::after {\n  top: -20px;\n  border-color: transparent transparent #edf1f5 transparent; }\n\n.timeline .timeline-launch .timeline-box::before {\n  top: -19px;\n  border-color: transparent transparent #f9fafc transparent;\n  border-width: 10px;\n  z-index: 1; }\n\n.timeline .timeline-launch .timeline-text {\n  width: 100%; }\n\n@media (max-width: 767px) {\n  .timeline .timeline-continue::after {\n    left: 40px; }\n  .timeline .timeline-end,\n  .timeline .timeline-start,\n  .timeline .timeline-year,\n  .timeline .row.timeline-left,\n  .timeline .row.timeline-right .timeline-date,\n  .timeline .row.timeline-right,\n  .timeline .row.timeline-left .timeline-date,\n  .timeline .timeline-launch {\n    text-align: left; }\n  .timeline .row.timeline-left .timeline-date::after,\n  .timeline .row.timeline-right .timeline-date::after {\n    left: 47px; }\n  .timeline .timeline-box,\n  .timeline .row.timeline-right .timeline-date,\n  .timeline .row.timeline-left .timeline-date {\n    margin-left: 55px; }\n  .timeline .timeline-launch .timeline-box {\n    margin-left: 0; }\n  .timeline .row.timeline-left .timeline-box::after {\n    left: -20px;\n    border-color: transparent #f9fafc transparent transparent; }\n  .timeline .row.timeline-left .timeline-box::before {\n    left: -22px;\n    border-color: transparent #edf1f5 transparent transparent; }\n  .timeline .timeline-launch .timeline-box::after,\n  .timeline .timeline-launch .timeline-box::before {\n    left: 30px;\n    margin-left: 0; } }\n\n.pricing-nav-tabs {\n  display: inline-block;\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  padding: 4px;\n  border-radius: 7px; }\n  .pricing-nav-tabs li {\n    display: inline-block; }\n\n.pricing-box .plan-features li {\n  padding: 7px 0px; }\n\n/*********************\r\n    Faqs\r\n**********************/\n.faq-nav-tabs .nav-item {\n  margin: 0px 8px; }\n\n.faq-nav-tabs .nav-link {\n  text-align: center;\n  margin-bottom: 8px;\n  border: 2px solid #edf1f5;\n  color: #495057; }\n  .faq-nav-tabs .nav-link .nav-icon {\n    font-size: 40px;\n    margin-bottom: 8px;\n    display: block; }\n  .faq-nav-tabs .nav-link.active {\n    border-color: #525ce5;\n    background-color: transparent;\n    color: #495057; }\n    .faq-nav-tabs .nav-link.active .nav-icon {\n      color: #525ce5; }\n\n.text-error {\n  font-size: 120px; }\n  @media (max-width: 575.98px) {\n    .text-error {\n      font-size: 86px; } }\n\n.error-text {\n  color: #f14e4e;\n  position: relative; }\n  .error-text .error-img {\n    position: absolute;\n    width: 120px;\n    left: -15px;\n    right: 0;\n    bottom: 47px; }\n    @media (max-width: 575.98px) {\n      .error-text .error-img {\n        width: 86px;\n        left: -12px;\n        bottom: 38px; } }\n", "//\r\n// Google font - Inter\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');", "// \r\n// _header.scss\r\n// \r\n\r\n#page-topbar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: 1001;\r\n    background-color: $header-bg;\r\n}\r\n\r\n.navbar-header {\r\n    display: flex;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    height: $header-height;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2) 0 0;\r\n    box-shadow: $box-shadow;\r\n\r\n    .dropdown.show {\r\n        .header-item {\r\n            background-color: $gray-100;\r\n        }\r\n    }\r\n}\r\n\r\n.navbar-brand-box {\r\n    padding: 0 1.5rem;\r\n    text-align: center;\r\n    width: $navbar-brand-box-width;\r\n}\r\n\r\n.logo {\r\n    line-height: 70px;\r\n\r\n    .logo-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.logo-dark {\r\n    display: $display-block;\r\n}\r\n\r\n.logo-light {\r\n    display: $display-none;\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .mdi-fullscreen::before {\r\n            content: '\\F0294';\r\n        }\r\n    }\r\n}\r\n\r\n// page-title-box\r\n\r\n\r\n.page-content-wrapper{\r\n    margin-top: -90px;\r\n}\r\n\r\n\r\n.page-title-box{\r\n    background: url(../images/title-img.png);\r\n    background-position: center;\r\n    background-color: $primary;\r\n    margin: 0 -24px 23px -24px;\r\n    padding: 24px 24px 92px 24px;\r\n    color: $white;\r\n    background-size: cover;\r\n}\r\n\r\n\r\n/* Search */\r\n.search-wrap {\r\n    background-color: lighten($card-bg, 4%);\r\n    color: $dark;\r\n    z-index: 9997;\r\n    position: absolute;\r\n    top: 0;\r\n    display: flex;\r\n    width: 100%;\r\n    right: 0;\r\n    height: 70px;\r\n    padding: 0 15px;\r\n    transform: translate3d(0, -100%, 0);\r\n    transition: .3s;\r\n  \r\n    form {\r\n      display: flex;\r\n      width: 100%;\r\n    }\r\n    .search-bar {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n    }\r\n    .search-input {\r\n      flex: 1 1;\r\n      border: none;\r\n      outline: none;\r\n      box-shadow: none;\r\n      background-color: transparent;\r\n    }\r\n    .close-search {\r\n      width: 36px;\r\n      height: 64px;\r\n      line-height: 64px;\r\n      text-align: center;\r\n      color: inherit;\r\n      font-size: 24px;\r\n  \r\n      &:hover {\r\n        color: $danger;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .search-wrap.open {\r\n    transform: translate3d(0, 0, 0);\r\n  }\r\n\r\n// Mega menu\r\n\r\n.megamenu-list {\r\n    li{\r\n        position: relative;\r\n        padding: 5px 0px;\r\n        a{\r\n            color: $dropdown-color;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n\r\n    #page-topbar {\r\n        left: 0;\r\n    }\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n\r\n    .logo {\r\n\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.page-content {\r\n    padding: calc(#{$header-height}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n}\r\n\r\n.header-item {\r\n    height: $header-height;\r\n    box-shadow: none !important;\r\n    color: $header-item-color;\r\n    border: 0;\r\n    border-radius: 0px;\r\n\r\n    &:hover {\r\n        color: $header-item-color;\r\n    }\r\n}\r\n\r\n.header-profile-user {\r\n    height: 36px;\r\n    width: 36px;\r\n    background-color: $gray-300;\r\n    padding: 3px;\r\n}\r\n\r\n.noti-icon {\r\n    i {\r\n        font-size: 24px;\r\n        color: $header-item-color;\r\n    }\r\n\r\n    .badge {\r\n        position: absolute;\r\n        top: 20px;\r\n        right: 6px;\r\n    }\r\n}\r\n\r\n.notification-item {\r\n    .media {\r\n        padding: 0.75rem 1rem;\r\n\r\n        &:hover {\r\n            background-color: $gray-300;\r\n        }\r\n    }\r\n}\r\n\r\n// Dropdown with Icons\r\n.dropdown-icon-item {\r\n    display: block;\r\n    border-radius: 3px;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    padding: 15px 0 9px;\r\n    display: block;\r\n    border: 1px solid transparent;\r\n    color: $gray-600;\r\n\r\n    img {\r\n        height: 24px;\r\n    }\r\n\r\n    span {\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n    }\r\n\r\n    &:hover {\r\n        border-color: $gray-200;\r\n    }\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .bx-fullscreen::before {\r\n            content: \"\\ea3f\";\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-topbar=\"dark\"] {\r\n    #page-topbar { \r\n        background-color: $header-dark-bg;\r\n    }\r\n    .navbar-header {\r\n        .dropdown.show {\r\n            .header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .title-tooltip{\r\n        li{\r\n           i{\r\n            color: $header-dark-item-color;\r\n           }\r\n        }\r\n    }\r\n\r\n  \r\n\r\n  \r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .navbar-brand-box {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .navbar-header {\r\n        .dropdown {\r\n            position: static;\r\n\r\n            .dropdown-menu {\r\n                left: 10px !important;\r\n                right: 10px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 380px) {\r\n\r\n  \r\n    .navbar-brand-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n    .page-content {\r\n        margin-top: $header-height;\r\n        padding: calc(36px + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n    }    \r\n}\r\n\r\n@media (max-width: 992px) { \r\n    body[data-layout=\"horizontal\"] {\r\n        .page-content {\r\n            margin-top: 10px;\r\n        }    \r\n    }\r\n}", "// Variables\n\n//\n// custom-variables\n//\n\n// Vertical Sidebar - Default Light\n$sidebar-bg: #ffffff;\n$sidebar-menu-item-color: #27303f;\n$sidebar-menu-sub-item-color: #27303f;\n$sidebar-menu-item-icon-color: #27303f;\n$sidebar-menu-item-hover-color: #525ce5;\n$sidebar-menu-item-active-color: #525ce5;\n$sidebar-menu-item-active-bg: #f5f7fa;\n$sidebar-width:  260px;\n$sidebar-collapsed-width:  70px;\n$sidebar-width-sm:  160px;\n\n// Vertical Sidebar - Dark\n$sidebar-dark-bg: #1f293f; //2c313a\n$sidebar-dark-menu-item-color: #8590a5;\n$sidebar-dark-menu-sub-item-color: #8590a5;\n$sidebar-dark-menu-item-icon-color: #8590a5;\n$sidebar-dark-menu-item-hover-color: #d7e4ec;\n$sidebar-dark-menu-item-active-color: #d7e4ec;\n$sidebar-dark-menu-item-active-bg: #2b364e;\n\n$dash-info-bg: #f9fafc;\n\n// Topbar - Deafult Light\n$header-height: 70px;\n$header-bg: #ffffff;\n$header-item-color: #636e75;\n\n// Topbar - Dark\n$header-dark-bg: #1f293f;\n$header-dark-item-color: #a3acc1;\n\n// Topbar - Colored\n$header-colored-bg: #556ee6;\n\n// Topbar Search\n$topbar-search-bg: #f1f5f7;\n\n// Footer\n$footer-height: 60px;\n$footer-bg: #fff;\n$footer-color: #74788d;\n\n// Horizontal nav\n$topnav-bg:   #fff;\n\n$menu-item-color: #74788d;\n$menu-item-active-color: #5664d2;\n\n// Right Sidebar\n$rightbar-width:  280px;\n\n// Display\n$display-none: none;\n$display-block: block;\n\n// Brand \n$navbar-brand-box-width: 260px;\n\n// Boxed layout width\n$boxed-layout-width:    1400px;\n$boxed-body-bg:       #f1f3f7;\n\n// Font Weight\n$font-weight-medium: 500;\n$font-weight-semibold: 600;\n\n// apex charts\n$apex-grid-color: #f8f9fa;\n\n// table\n$table-head-bg:               $gray-100;\n$table-dark-border-color:     tint-color($gray-800, 7.5%);\n\n\n\n// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n// scss-docs-start gray-color-variables\n$white:    #fff;\n$gray-100: #f9fafc;\n$gray-200: #edf1f5;\n$gray-300: #eaedf1 ;\n$gray-400: #ced4da;\n$gray-500: #adb5bd;\n$gray-600: #74788d;\n$gray-700: #495057;\n$gray-800: #343a40;\n$gray-900: #212529;\n$black:    #000;\n// scss-docs-end gray-color-variables\n\n// fusv-disable\n// scss-docs-start gray-colors-map\n$grays: (\n    \"100\": $gray-100,\n    \"200\": $gray-200,\n    \"300\": $gray-300,\n    \"400\": $gray-400,\n    \"500\": $gray-500,\n    \"600\": $gray-600,\n    \"700\": $gray-700,\n    \"800\": $gray-800,\n    \"900\": $gray-900\n);\n// scss-docs-end gray-colors-map\n// fusv-enable\n\n// scss-docs-start color-variables\n$blue:    #525ce5;\n$indigo:  #564ab1;\n$purple:  #6f42c1;\n$pink:    #e83e8c;\n$red:     #f14e4e;\n$orange:  #f1734f;\n$yellow:  #eeb148;\n$green:   #23c58f;\n$teal:    #050505;\n$cyan:    #5ba4e5;\n// scss-docs-end color-variables\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n);\n// scss-docs-end colors-map\n\n// scss-docs-start theme-color-variables\n$primary:       $blue;\n$secondary:     $gray-600;\n$success:       $green;\n$info:          $cyan;\n$warning:       $yellow;\n$danger:        $red;\n$light:         $gray-100;\n$dark:          $gray-800;\n// scss-docs-end theme-color-variables\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"pink\":       $pink,\n  \"light\":      $light,\n  \"dark\":       $dark\n);\n// scss-docs-end theme-colors-map\n\n// scss-docs-start theme-colors-rgb\n$theme-colors-rgb: map-loop($theme-colors, to-rgb, \"$value\");\n// scss-docs-end theme-colors-rgb\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   1.7;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black;\n$color-contrast-light:     $white;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%);\n$blue-200: tint-color($blue, 60%);\n$blue-300: tint-color($blue, 40%);\n$blue-400: tint-color($blue, 20%);\n$blue-500: $blue;\n$blue-600: shade-color($blue, 20%);\n$blue-700: shade-color($blue, 40%);\n$blue-800: shade-color($blue, 60%);\n$blue-900: shade-color($blue, 80%);\n\n$indigo-100: tint-color($indigo, 80%);\n$indigo-200: tint-color($indigo, 60%);\n$indigo-300: tint-color($indigo, 40%);\n$indigo-400: tint-color($indigo, 20%);\n$indigo-500: $indigo;\n$indigo-600: shade-color($indigo, 20%);\n$indigo-700: shade-color($indigo, 40%);\n$indigo-800: shade-color($indigo, 60%);\n$indigo-900: shade-color($indigo, 80%);\n\n$purple-100: tint-color($purple, 80%);\n$purple-200: tint-color($purple, 60%);\n$purple-300: tint-color($purple, 40%);\n$purple-400: tint-color($purple, 20%);\n$purple-500: $purple;\n$purple-600: shade-color($purple, 20%);\n$purple-700: shade-color($purple, 40%);\n$purple-800: shade-color($purple, 60%);\n$purple-900: shade-color($purple, 80%);\n\n$pink-100: tint-color($pink, 80%);\n$pink-200: tint-color($pink, 60%);\n$pink-300: tint-color($pink, 40%);\n$pink-400: tint-color($pink, 20%);\n$pink-500: $pink;\n$pink-600: shade-color($pink, 20%);\n$pink-700: shade-color($pink, 40%);\n$pink-800: shade-color($pink, 60%);\n$pink-900: shade-color($pink, 80%);\n\n$red-100: tint-color($red, 80%);\n$red-200: tint-color($red, 60%);\n$red-300: tint-color($red, 40%);\n$red-400: tint-color($red, 20%);\n$red-500: $red;\n$red-600: shade-color($red, 20%);\n$red-700: shade-color($red, 40%);\n$red-800: shade-color($red, 60%);\n$red-900: shade-color($red, 80%);\n\n$orange-100: tint-color($orange, 80%);\n$orange-200: tint-color($orange, 60%);\n$orange-300: tint-color($orange, 40%);\n$orange-400: tint-color($orange, 20%);\n$orange-500: $orange;\n$orange-600: shade-color($orange, 20%);\n$orange-700: shade-color($orange, 40%);\n$orange-800: shade-color($orange, 60%);\n$orange-900: shade-color($orange, 80%);\n\n$yellow-100: tint-color($yellow, 80%);\n$yellow-200: tint-color($yellow, 60%);\n$yellow-300: tint-color($yellow, 40%);\n$yellow-400: tint-color($yellow, 20%);\n$yellow-500: $yellow;\n$yellow-600: shade-color($yellow, 20%);\n$yellow-700: shade-color($yellow, 40%);\n$yellow-800: shade-color($yellow, 60%);\n$yellow-900: shade-color($yellow, 80%);\n\n\n$green-100: tint-color($green, 80%);\n$green-200: tint-color($green, 60%);\n$green-300: tint-color($green, 40%);\n$green-400: tint-color($green, 20%);\n$green-500: $green;\n$green-600: shade-color($green, 20%);\n$green-700: shade-color($green, 40%);\n$green-800: shade-color($green, 60%);\n$green-900: shade-color($green, 80%);\n\n\n$teal-100: tint-color($teal, 80%);\n$teal-200: tint-color($teal, 60%);\n$teal-300: tint-color($teal, 40%);\n$teal-400: tint-color($teal, 20%);\n$teal-500: $teal;\n$teal-600: shade-color($teal, 20%);\n$teal-700: shade-color($teal, 40%);\n$teal-800: shade-color($teal, 60%);\n$teal-900: shade-color($teal, 80%);\n\n$cyan-100: tint-color($cyan, 80%);\n$cyan-200: tint-color($cyan, 60%);\n$cyan-300: tint-color($cyan, 40%);\n$cyan-400: tint-color($cyan, 20%);\n$cyan-500: $cyan;\n$cyan-600: shade-color($cyan, 20%);\n$cyan-700: shade-color($cyan, 40%);\n$cyan-800: shade-color($cyan, 60%);\n$cyan-900: shade-color($cyan, 80%);\n$blues: (\n  \"blue-100\": $blue-100,\n  \"blue-200\": $blue-200,\n  \"blue-300\": $blue-300,\n  \"blue-400\": $blue-400,\n  \"blue-500\": $blue-500,\n  \"blue-600\": $blue-600,\n  \"blue-700\": $blue-700,\n  \"blue-800\": $blue-800,\n  \"blue-900\": $blue-900\n);\n\n$indigos: (\n  \"indigo-100\": $indigo-100,\n  \"indigo-200\": $indigo-200,\n  \"indigo-300\": $indigo-300,\n  \"indigo-400\": $indigo-400,\n  \"indigo-500\": $indigo-500,\n  \"indigo-600\": $indigo-600,\n  \"indigo-700\": $indigo-700,\n  \"indigo-800\": $indigo-800,\n  \"indigo-900\": $indigo-900\n);\n\n$purples: (\n  \"purple-100\": $purple-200,\n  \"purple-200\": $purple-100,\n  \"purple-300\": $purple-300,\n  \"purple-400\": $purple-400,\n  \"purple-500\": $purple-500,\n  \"purple-600\": $purple-600,\n  \"purple-700\": $purple-700,\n  \"purple-800\": $purple-800,\n  \"purple-900\": $purple-900\n);\n\n$pinks: (\n  \"pink-100\": $pink-100,\n  \"pink-200\": $pink-200,\n  \"pink-300\": $pink-300,\n  \"pink-400\": $pink-400,\n  \"pink-500\": $pink-500,\n  \"pink-600\": $pink-600,\n  \"pink-700\": $pink-700,\n  \"pink-800\": $pink-800,\n  \"pink-900\": $pink-900\n);\n\n$reds: (\n  \"red-100\": $red-100,\n  \"red-200\": $red-200,\n  \"red-300\": $red-300,\n  \"red-400\": $red-400,\n  \"red-500\": $red-500,\n  \"red-600\": $red-600,\n  \"red-700\": $red-700,\n  \"red-800\": $red-800,\n  \"red-900\": $red-900\n);\n\n$oranges: (\n  \"orange-100\": $orange-100,\n  \"orange-200\": $orange-200,\n  \"orange-300\": $orange-300,\n  \"orange-400\": $orange-400,\n  \"orange-500\": $orange-500,\n  \"orange-600\": $orange-600,\n  \"orange-700\": $orange-700,\n  \"orange-800\": $orange-800,\n  \"orange-900\": $orange-900\n);\n\n$yellows: (\n  \"yellow-100\": $yellow-100,\n  \"yellow-200\": $yellow-200,\n  \"yellow-300\": $yellow-300,\n  \"yellow-400\": $yellow-400,\n  \"yellow-500\": $yellow-500,\n  \"yellow-600\": $yellow-600,\n  \"yellow-700\": $yellow-700,\n  \"yellow-800\": $yellow-800,\n  \"yellow-900\": $yellow-900\n);\n\n$greens: (\n  \"green-100\": $green-100,\n  \"green-200\": $green-200,\n  \"green-300\": $green-300,\n  \"green-400\": $green-400,\n  \"green-500\": $green-500,\n  \"green-600\": $green-600,\n  \"green-700\": $green-700,\n  \"green-800\": $green-800,\n  \"green-900\": $green-900\n);\n\n$teals: (\n  \"teal-100\": $teal-100,\n  \"teal-200\": $teal-200,\n  \"teal-300\": $teal-300,\n  \"teal-400\": $teal-400,\n  \"teal-500\": $teal-500,\n  \"teal-600\": $teal-600,\n  \"teal-700\": $teal-700,\n  \"teal-800\": $teal-800,\n  \"teal-900\": $teal-900\n);\n\n$cyans: (\n  \"cyan-100\": $cyan-100,\n  \"cyan-200\": $cyan-200,\n  \"cyan-300\": $cyan-300,\n  \"cyan-400\": $cyan-400,\n  \"cyan-500\": $cyan-500,\n  \"cyan-600\": $cyan-600,\n  \"cyan-700\": $cyan-700,\n  \"cyan-800\": $cyan-800,\n  \"cyan-900\": $cyan-900\n);\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n);\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                false;\n$enable-rounded:              true;\n$enable-shadows:              false;\n$enable-gradients:            false;\n$enable-transitions:          true;\n$enable-reduced-motion:       true;\n$enable-smooth-scroll:        true;\n$enable-grid-classes:         true;\n$enable-cssgrid:              false;\n$enable-button-pointers:      true;\n$enable-rfs:                  true;\n$enable-validation-icons:     true;\n$enable-negative-margins:     true;\n$enable-deprecation-messages: true;\n$enable-important-utilities:  true;\n\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs-;\n\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n// scss-docs-start variable-gradient\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0));\n// scss-docs-end variable-gradient\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// scss-docs-start spacer-variables-maps\n$spacer: 1rem;\n$spacers: (\n    0: 0,\n    1: ($spacer * .25),\n    2: ($spacer * .5),\n    3: $spacer,\n    4: ($spacer * 1.5),\n    5: ($spacer * 3)\n);\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null);\n// scss-docs-end spacer-variables-maps\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n// scss-docs-start position-map\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n);\n// scss-docs-end position-map\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   #f5f7fa;\n$body-color:                $gray-700;\n$body-text-align:           null;\n\n// Utilities maps\n//\n// Extends the default `$theme-colors` maps to help create our utilities.\n\n// Come v6, we'll de-dupe these variables. Until then, for backward compatibility, we keep them to reassign.\n// scss-docs-start utilities-colors\n$utilities-colors: $theme-colors-rgb;\n// scss-docs-end utilities-colors\n\n// scss-docs-start utilities-text-colors\n$utilities-text: map-merge(\n  $utilities-colors,\n  (\n    \"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-color)\n  )\n);\n$utilities-text-colors: map-loop($utilities-text, rgba-css-var, \"$key\", \"text\");\n// scss-docs-end utilities-text-colors\n\n// scss-docs-start utilities-bg-colors\n$utilities-bg: map-merge(\n  $utilities-colors,\n  (\n    \"black\": to-rgb($black),\n    \"white\": to-rgb($white),\n    \"body\": to-rgb($body-bg)\n  )\n);\n$utilities-bg-colors: map-loop($utilities-bg, rgba-css-var, \"$key\", \"bg\");\n// scss-docs-end utilities-bg-colors\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary;\n$link-decoration:                         none;\n$link-shade-percentage:                   20%;\n$link-hover-color:                        darken($link-color, 15%);\n$link-hover-decoration:                   underline;\n\n$stretched-link-pseudo-element:           after;\n$stretched-link-z-index:                  1;\n\n\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n);\n// scss-docs-end grid-breakpoints\n\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n);\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12;\n$grid-gutter-width:           24px;\n$grid-row-columns:            6;\n\n$gutters: $spacers;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width / 2;\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n// scss-docs-start border-variables\n$border-width:                1px;\n$border-widths: (\n  0: 0,\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n);\n\n$border-color:                $gray-200;\n// scss-docs-end border-variables\n\n// scss-docs-start border-radius-variables\n$border-radius:               .25rem;\n$border-radius-lg:            .4rem;\n$border-radius-sm:            .2rem;\n$border-radius-pill:          50rem;\n// scss-docs-end border-radius-variables\n\n// scss-docs-start box-shadow-variables\n$box-shadow:                  0 .5rem 1rem rgba($black, .15);\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075);\n$box-shadow:                  0 2px 4px rgba(126,142,177, 0.1);\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175);\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075);\n// scss-docs-end box-shadow-variables\n\n$component-active-color:      $white;\n$component-active-bg:         $primary;\n\n// scss-docs-start caret-variables\n$caret-width:                 .3em;\n$caret-vertical-align:        $caret-width * .85;\n$caret-spacing:               $caret-width * .85;\n// scss-docs-end caret-variables\n\n$transition-base:             all .2s ease-in-out;\n$transition-fade:             opacity .15s linear;\n// scss-docs-start collapse-transition\n$transition-collapse:         height .35s ease;\n$transition-collapse-width:   width .35s ease;\n// scss-docs-end collapse-transition\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n);\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// scss-docs-start font-variables\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      'Inter', sans-serif;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif);\n$font-family-code:            var(--#{$variable-prefix}font-monospace);\n\n// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base effects the font size of the body text\n$font-size-root:              null;\n$font-size-base:              .875rem;\n$font-size-sm:                $font-size-base * .875;\n$font-size-lg:                $font-size-base * 1.25;\n\n\n$font-weight-lighter:         lighter;\n$font-weight-light:           300;\n$font-weight-normal:          400;\n$font-weight-bold:            600;\n$font-weight-bolder:          bolder;\n\n$font-weight-base:            $font-weight-normal;\n$line-height-base:            1.5;\n$line-height-sm:              1.25;\n$line-height-lg:              2;\n\n$h1-font-size:                $font-size-base * 2.5;\n$h2-font-size:                $font-size-base * 2;\n$h3-font-size:                $font-size-base * 1.75;\n$h4-font-size:                $font-size-base * 1.5;\n$h5-font-size:                $font-size-base * 1.25;\n$h6-font-size:                $font-size-base;\n// scss-docs-end font-variables\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n);\n// scss-docs-end font-sizes\n\n// scss-docs-start headings-variables\n$headings-margin-bottom:      $spacer / 2;\n$headings-font-family:        null;\n$headings-font-style:         null;\n$headings-font-weight:        500;\n$headings-line-height:        1.2;\n$headings-color:              null;\n// scss-docs-end headings-variables\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 6rem,\n  2: 5.5rem,\n  3: 4.5rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n);\n\n$display-font-weight: 300;\n$display-line-height: $headings-line-height;\n// scss-docs-end display-headings\n\n// scss-docs-start type-variables\n$lead-font-size:              $font-size-base * 1.25;\n$lead-font-weight:            300;\n\n$small-font-size:             80%;\n\n$sub-sup-font-size:           .75em;\n$text-muted:                  $gray-600;\n\n$initialism-font-size:        $small-font-size;\n\n$blockquote-margin-y:         $spacer;\n$blockquote-font-size:        $font-size-base * 1.25;\n$blockquote-footer-color:     $gray-600;\n$blockquote-footer-font-size: $small-font-size;\n\n$hr-margin-y:                 $spacer;\n$hr-color:                    rgba($black, .1);\n$hr-height:                   $border-width;\n$hr-opacity:                  .7;\n\n$legend-margin-bottom:        .5rem;\n$legend-font-size:            1.5rem;\n$legend-font-weight:          null;\n\n$mark-padding:                .2em;\n\n$dt-font-weight:              $font-weight-bold;\n\n$nested-kbd-font-weight:      $font-weight-bold;\n\n$list-inline-padding:         .5rem;\n\n$mark-bg:                     #fcf8e3;\n// scss-docs-end type-variables\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .75rem;\n$table-cell-padding-x:        .75rem;\n$table-cell-padding-y-sm:     .3rem;\n$table-cell-padding-x-sm:     .3rem;\n\n$table-cell-vertical-align:   top;\n\n$table-color:                 $body-color;\n$table-bg:                    null;\n$table-accent-bg:             transparent;\n\n$table-th-font-weight:        null;\n\n\n$table-striped-color:         $table-color;\n$table-striped-bg-factor:     .05;\n$table-striped-bg:            $gray-100;\n\n$table-active-color:          $table-color;\n$table-active-bg-factor:      .1;\n$table-active-bg:             $gray-100;\n\n$table-hover-color:           $table-color;\n$table-hover-bg-factor:       .075;\n$table-hover-bg:              $gray-100;\n\n$table-border-factor:         .1;\n$table-border-width:          $border-width;\n$table-border-color:          $border-color;\n\n$table-striped-order:         odd;\n$table-group-separator-color: $border-color;\n\n$table-caption-color:         $text-muted;\n\n$table-bg-scale:              -80%;\n// scss-docs-end table-variables\n\n// scss-docs-start table-loop\n$table-variants: (\n  \"primary\":    shift-color($primary, $table-bg-scale),\n  \"secondary\":  shift-color($secondary, $table-bg-scale),\n  \"success\":    shift-color($success, $table-bg-scale),\n  \"info\":       shift-color($info, $table-bg-scale),\n  \"warning\":    shift-color($warning, $table-bg-scale),\n  \"danger\":     shift-color($danger, $table-bg-scale),\n  \"light\":      $gray-100,\n  \"dark\":       $dark,\n);\n// scss-docs-end table-loop\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n// scss-docs-start input-btn-variables\n$input-btn-padding-y:         .47rem;\n$input-btn-padding-x:         .75rem;\n$input-btn-font-family:       null;\n$input-btn-font-size:         $font-size-base;\n$input-btn-line-height:       $line-height-base;\n\n$input-btn-focus-width:       .15rem;\n$input-btn-focus-color-opacity: .25;\n$input-btn-focus-color:         rgba($component-active-bg, .25);\n$input-btn-focus-blur:          0;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color;\n\n$input-btn-padding-y-sm:      .25rem;\n$input-btn-padding-x-sm:      .5rem;\n$input-btn-font-size-sm:      $font-size-sm;\n\n$input-btn-padding-y-lg:      .5rem;\n$input-btn-padding-x-lg:      1rem;\n$input-btn-font-size-lg:      $font-size-lg;\n\n$input-btn-border-width:      $border-width;\n// scss-docs-end input-btn-variables\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n// scss-docs-start btn-variables\n$btn-padding-y:               $input-btn-padding-y;\n$btn-padding-x:               $input-btn-padding-x;\n$btn-font-family:             $input-btn-font-family;\n$btn-font-size:               $input-btn-font-size;\n$btn-line-height:             $input-btn-line-height;\n$btn-white-space:             null; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm;\n$btn-padding-x-sm:            $input-btn-padding-x-sm;\n$btn-font-size-sm:            $input-btn-font-size-sm;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg;\n$btn-padding-x-lg:            $input-btn-padding-x-lg;\n$btn-font-size-lg:            $input-btn-font-size-lg;\n\n$btn-border-width:            $input-btn-border-width;\n\n$btn-font-weight:             $font-weight-normal;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075);\n$btn-focus-width:             $input-btn-focus-width;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow;\n$btn-disabled-opacity:        .65;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125);\n\n$btn-link-color:              $link-color;\n$btn-link-hover-color:        $link-hover-color;\n$btn-link-disabled-color:     $gray-600;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius;\n$btn-border-radius-sm:        $border-radius-sm;\n$btn-border-radius-lg:        $border-radius-lg;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$btn-hover-bg-shade-amount:       15%;\n$btn-hover-bg-tint-amount:        15%;\n$btn-hover-border-shade-amount:   20%;\n$btn-hover-border-tint-amount:    10%;\n$btn-active-bg-shade-amount:      20%;\n$btn-active-bg-tint-amount:       20%;\n$btn-active-border-shade-amount:  25%;\n$btn-active-border-tint-amount:   10%;\n// scss-docs-end btn-variables\n\n// Forms\n\n// scss-docs-start form-text-variables\n$form-text-margin-top:                  .25rem;\n$form-text-font-size:                   $small-font-size;\n$form-text-font-style:                  null;\n$form-text-font-weight:                 null;\n$form-text-color:                       $text-muted;\n// scss-docs-end form-text-variables\n\n// scss-docs-start form-label-variables\n$form-label-margin-bottom:              .5rem;\n$form-label-font-size:                  null;\n$form-label-font-style:                 null;\n$form-label-font-weight:                null;\n$form-label-color:                      null;\n// scss-docs-end form-label-variables\n\n// scss-docs-start form-input-variables\n$input-padding-y:                       $input-btn-padding-y;\n$input-padding-x:                       $input-btn-padding-x;\n$input-font-family:                     $input-btn-font-family;\n$input-font-size:                       $input-btn-font-size;\n$input-font-weight:                     $font-weight-base;\n$input-line-height:                     $input-btn-line-height;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm;\n$input-padding-x-sm:                    $input-btn-padding-x-sm;\n$input-font-size-sm:                    $input-btn-font-size-sm;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg;\n$input-padding-x-lg:                    $input-btn-padding-x-lg;\n$input-font-size-lg:                    $input-btn-font-size-lg;\n\n$input-bg:                              $white;\n$input-disabled-bg:                     $gray-200;\n$input-disabled-border-color:           null;\n\n$input-color:                           $gray-700;\n$input-border-color:                    $gray-400;\n$input-border-width:                    $input-btn-border-width;\n$input-box-shadow:                      $box-shadow-inset;\n\n$input-border-radius:                   $border-radius;\n$input-border-radius-lg:                $border-radius-lg;\n$input-border-radius-sm:                $border-radius-sm;\n\n$input-focus-bg:                        $input-bg;\n$input-focus-border-color:              darken($input-border-color, 10%);\n$input-focus-color:                     $input-color;\n$input-focus-width:                     $input-btn-focus-width;\n$input-focus-box-shadow:                none;\n\n$input-placeholder-color:               $gray-600;\n$input-plaintext-color:                 $body-color;\n\n$input-height-border:                   $input-border-width * 2;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2);\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y);\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2);\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false));\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false));\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false));\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$form-color-width:                      3rem;\n// scss-docs-end form-input-variables\n\n// scss-docs-start form-check-variables\n$form-check-input-width:                  1em;\n$form-check-min-height:                   $font-size-base * $line-height-base;\n$form-check-padding-start:                $form-check-input-width + .5em;\n$form-check-margin-bottom:                .125rem;\n$form-check-label-color:                  null;\n$form-check-label-cursor:                 null;\n$form-check-transition:                   background-color .15s ease-in-out, background-position .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$form-check-input-active-filter:          brightness(90%);\n\n$form-check-input-bg:                     $card-bg;\n$form-check-input-border:                 1px solid rgba(0, 0, 0, .25);\n$form-check-input-border-radius:          .25em;\n$form-check-radio-border-radius:          50%;\n$form-check-input-focus-border:           $input-focus-border-color;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow;\n\n$form-check-input-checked-color:          $component-active-color;\n$form-check-input-checked-bg-color:       $component-active-bg;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\");\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\");\n\n$form-check-input-indeterminate-color:          $component-active-color;\n$form-check-input-indeterminate-bg-color:       $component-active-bg;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\");\n\n$form-check-input-disabled-opacity:        .5;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity;\n\n$form-check-inline-margin-end:    1rem;\n// scss-docs-end form-check-variables\n// scss-docs-start form-switch-variables\n$form-switch-color:               rgba(0, 0, 0, .25);\n$form-switch-width:               2em;\n$form-switch-padding-start:       $form-switch-width + .5em;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\");\n$form-switch-border-radius:       $form-switch-width;\n$form-switch-transition:          background-position .15s ease-in-out;\n\n$form-switch-focus-color:         $input-focus-border-color;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\");\n\n$form-switch-checked-color:       $component-active-color;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\");\n$form-switch-checked-bg-position: right center;\n// scss-docs-end form-switch-variables\n$form-check-inline-margin-end:    1rem;\n\n// scss-docs-start input-group-variables\n$input-group-addon-padding-y:           $input-padding-y;\n$input-group-addon-padding-x:           $input-padding-x;\n$input-group-addon-font-weight:         $input-font-weight;\n$input-group-addon-color:               $input-color;\n$input-group-addon-bg:                  $gray-200;\n$input-group-addon-border-color:        $input-border-color;\n// scss-docs-end input-group-variables\n\n// scss-docs-start form-select-variables\n$form-select-padding-y:             $input-padding-y;\n$form-select-padding-x:             $input-padding-x;\n$form-select-font-family:           $input-font-family;\n$form-select-font-size:             $input-font-size;\n$form-select-indicator-padding:     1.75rem; // Extra padding to account for the presence of the background-image based indicator\n$form-select-font-weight:           $input-font-weight;\n$form-select-line-height:           $input-line-height;\n$form-select-color:                 $input-color;\n$form-select-disabled-color:        $gray-600;\n$form-select-bg:                    $input-bg;\n$form-select-disabled-bg:           $gray-200;\n$form-select-disabled-border-color: $input-disabled-border-color;\n$form-select-bg-position:           right $form-select-padding-x center;\n$form-select-bg-size:               16px 12px; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\");\n\n$form-select-feedback-icon-padding-end: add(1em * .75, (2 * $form-select-padding-y * .75) + $form-select-padding-x + $form-select-indicator-padding);\n$form-select-feedback-icon-position:    center right ($form-select-padding-x + $form-select-indicator-padding);\n$form-select-feedback-icon-size:        $input-height-inner-half $input-height-inner-half;\n\n$form-select-border-width:        $input-border-width;\n$form-select-border-color:        $input-border-color;\n$form-select-border-radius:       $border-radius;\n$form-select-box-shadow:          $box-shadow-inset;\n\n$form-select-focus-border-color:  $input-focus-border-color;\n$form-select-focus-width:         $input-focus-width;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color;\n\n$form-select-padding-y-sm:        $input-padding-y-sm;\n$form-select-padding-x-sm:        $input-padding-x-sm;\n$form-select-font-size-sm:        $input-font-size-sm;\n\n$form-select-padding-y-lg:        $input-padding-y-lg;\n$form-select-padding-x-lg:        $input-padding-x-lg;\n$form-select-font-size-lg:        $input-font-size-lg;\n\n$form-select-transition:          $input-transition;\n// scss-docs-end form-select-variables\n// scss-docs-start form-range-variables\n$form-range-track-width:          100%;\n$form-range-track-height:         .5rem;\n$form-range-track-cursor:         pointer;\n$form-range-track-bg:             $gray-300;\n$form-range-track-border-radius:  1rem;\n$form-range-track-box-shadow:     $box-shadow-inset;\n\n$form-range-thumb-width:                   1rem;\n$form-range-thumb-height:                  $form-range-thumb-width;\n$form-range-thumb-bg:                      $component-active-bg;\n$form-range-thumb-border:                  0;\n$form-range-thumb-border-radius:           1rem;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1);\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%);\n$form-range-thumb-disabled-bg:             $gray-500;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n// scss-docs-end form-range-variables\n\n// scss-docs-start form-file-variables\n$form-file-button-color:          $input-color;\n$form-file-button-bg:             $input-group-addon-bg;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%);\n// scss-docs-end form-file-variables\n\n// scss-docs-start form-floating-variables\n$form-floating-height:            add(3.5rem, $input-height-border);\n$form-floating-line-height:       1.25;\n$form-floating-padding-x:         $input-padding-x;\n$form-floating-padding-y:         1rem;\n$form-floating-input-padding-t:   1.625rem;\n$form-floating-input-padding-b:   .625rem;\n$form-floating-label-opacity:     .65;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem);\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out;\n// scss-docs-end form-floating-variables\n\n// Form validation\n\n// scss-docs-start form-feedback-variables\n$form-feedback-margin-top:          $form-text-margin-top;\n$form-feedback-font-size:           $form-text-font-size;\n$form-feedback-font-style:          $form-text-font-style;\n$form-feedback-valid-color:         $success;\n$form-feedback-invalid-color:       $danger;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\");\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\");\n// scss-docs-end form-feedback-variables\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n);\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000;\n$zindex-sticky:                     1020;\n$zindex-fixed:                      1030;\n$zindex-offcanvas-backdrop:         1040;\n$zindex-offcanvas:                  1045;\n$zindex-modal-backdrop:             1050;\n$zindex-modal:                      1055;\n$zindex-popover:                    1070;\n$zindex-tooltip:                    1080;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n// scss-docs-start nav-variables\n$nav-link-padding-y:                .5rem;\n$nav-link-padding-x:                1rem;\n$nav-link-font-size:                null;\n$nav-link-font-weight:              null;\n$nav-link-color:                    null;\n$nav-link-hover-color:              null;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out;\n$nav-link-disabled-color:           $gray-600;\n\n$nav-tabs-border-color:             $gray-400;\n$nav-tabs-border-width:             $border-width;\n$nav-tabs-border-radius:            $border-radius;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color;\n$nav-tabs-link-active-color:        $gray-700;\n$nav-tabs-link-active-bg:           $card-bg;\n$nav-tabs-link-active-border-color: $gray-400 $gray-400 $nav-tabs-link-active-bg;\n\n$nav-pills-border-radius:           $border-radius;\n$nav-pills-link-active-color:       $component-active-color;\n$nav-pills-link-active-bg:          $component-active-bg;\n// scss-docs-end nav-variables\n\n\n// Navbar\n\n// scss-docs-start navbar-variables\n$navbar-padding-y:                  $spacer / 2;\n$navbar-padding-x:                  null;\n\n$navbar-nav-link-padding-x:         .5rem;\n\n$navbar-brand-font-size:            $font-size-lg;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2;\n$navbar-brand-margin-end:           1rem;\n\n$navbar-toggler-padding-y:          .25rem;\n$navbar-toggler-padding-x:          .75rem;\n$navbar-toggler-font-size:          $font-size-lg;\n$navbar-toggler-border-radius:      $btn-border-radius;\n$navbar-toggler-focus-width:        $btn-focus-width;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out;\n// scss-docs-end navbar-variables\n\n// scss-docs-start navbar-theme-variables\n$navbar-dark-color:                 rgba($white, .55);\n$navbar-dark-hover-color:           rgba($white, .75);\n$navbar-dark-active-color:          $white;\n$navbar-dark-disabled-color:        rgba($white, .25);\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-dark-toggler-border-color:  rgba($white, .1);\n\n$navbar-light-color:                rgba($black, .55);\n$navbar-light-hover-color:          rgba($black, .7);\n$navbar-light-active-color:         rgba($black, .9);\n$navbar-light-disabled-color:       rgba($black, .3);\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\");\n$navbar-light-toggler-border-color: rgba($black, .1);\n\n$navbar-light-brand-color:                $navbar-light-active-color;\n$navbar-light-brand-hover-color:          $navbar-light-active-color;\n$navbar-dark-brand-color:                 $navbar-dark-active-color;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color;\n// scss-docs-end navbar-theme-variables\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n// scss-docs-start dropdown-variables\n$dropdown-min-width:                10rem;\n$dropdown-padding-x:                0;\n$dropdown-padding-y:                .5rem;\n$dropdown-spacer:                   .125rem;\n$dropdown-font-size:                $font-size-base;\n$dropdown-color:                    $body-color;\n$dropdown-bg:                       $white;\n$dropdown-border-color:             rgba($black, .15);\n$dropdown-border-radius:            $border-radius;\n$dropdown-border-width:             0;\n$dropdown-inner-border-radius:      calc(#{$dropdown-border-radius} - #{$dropdown-border-width});\n$dropdown-divider-bg:               $gray-200;\n$dropdown-divider-margin-y:         $spacer / 2;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175);\n\n$dropdown-link-color:               $gray-900;\n$dropdown-link-hover-color:         darken($gray-900, 5%);\n$dropdown-link-hover-bg:            $gray-100;\n\n$dropdown-link-active-color:        darken($gray-900, 5%);\n$dropdown-link-active-bg:           $gray-100;\n\n$dropdown-link-disabled-color:      $gray-600;\n\n$dropdown-item-padding-y:           .35rem;\n$dropdown-item-padding-x:           1.5rem;\n\n$dropdown-header-color:             $gray-600;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x;\n// scss-docs-end dropdown-variables\n\n// scss-docs-start dropdown-dark-variables\n$dropdown-dark-color:               $gray-300;\n$dropdown-dark-bg:                  $gray-800;\n$dropdown-dark-border-color:        $dropdown-border-color;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg;\n$dropdown-dark-box-shadow:          null;\n$dropdown-dark-link-color:          $dropdown-dark-color;\n$dropdown-dark-link-hover-color:    $white;\n$dropdown-dark-link-hover-bg:       rgba($white, .15);\n$dropdown-dark-link-active-color:   $dropdown-link-active-color;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg;\n$dropdown-dark-link-disabled-color: $gray-500;\n$dropdown-dark-header-color:        $gray-500;\n// scss-docs-end dropdown-dark-variables\n\n// Pagination\n\n// scss-docs-start pagination-variables\n$pagination-padding-y:              .5rem;\n$pagination-padding-x:              .75rem;\n$pagination-padding-y-sm:           .25rem;\n$pagination-padding-x-sm:           .5rem;\n$pagination-padding-y-lg:           .75rem;\n$pagination-padding-x-lg:           1.5rem;\n\n$pagination-color:                  $gray-600;\n$pagination-bg:                     $white;\n$pagination-border-width:           $border-width;\n$pagination-border-radius:          $border-radius;\n$pagination-margin-start:           -$pagination-border-width;\n$pagination-border-color:           $gray-400;\n\n$pagination-focus-color:            $link-hover-color;\n$pagination-focus-bg:               $gray-200;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow;\n$pagination-focus-outline:          0;\n\n$pagination-hover-color:            $link-hover-color;\n$pagination-hover-bg:               $gray-200;\n$pagination-hover-border-color:     $gray-400;\n\n$pagination-active-color:           $component-active-color;\n$pagination-active-bg:              $component-active-bg;\n$pagination-active-border-color:    $pagination-active-bg;\n\n$pagination-disabled-color:         $gray-400;\n$pagination-disabled-bg:            $white;\n$pagination-disabled-border-color:  $gray-400;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;\n\n$pagination-border-radius-sm:       $border-radius-sm;\n$pagination-border-radius-lg:       $border-radius-lg;\n// scss-docs-end pagination-variables\n\n\n// Placeholders\n\n// scss-docs-start placeholders\n$placeholder-opacity-max:           .5;\n$placeholder-opacity-min:           .2;\n// scss-docs-end placeholders\n\n// Cards\n\n// scss-docs-start card-variables\n$card-spacer-y:                     1.25rem;\n$card-spacer-x:                     1.25rem;\n$card-title-spacer-y:               $spacer / 2;\n$card-border-width:                 0;\n$card-border-color:                 $gray-300;\n$card-border-radius:                $border-radius;\n$card-box-shadow:                   null;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width);\n$card-cap-padding-y:                $card-spacer-y / 2;\n$card-cap-padding-x:                $card-spacer-x;\n$card-cap-bg:                       $gray-300;\n$card-cap-color:                    null;\n$card-height:                       null;\n$card-color:                        null;\n$card-bg:                           $white;\n$card-img-overlay-padding:          $spacer;\n\n$card-title-desc:                   $gray-600;\n\n$card-group-margin:                 $grid-gutter-width / 2;\n// scss-docs-end card-variables\n\n// Accordion\n// scss-docs-start accordion-variables\n$accordion-padding-y:                     1rem;\n$accordion-padding-x:                     1.25rem;\n$accordion-color:                         $body-color;\n$accordion-bg:                            transparent;\n$accordion-border-width:                  $border-width;\n$accordion-border-color:                  rgba($black, .125);\n$accordion-border-radius:                 $border-radius;\n$accordion-inner-border-radius:           subtract($accordion-border-radius, $accordion-border-width);\n\n$accordion-body-padding-y:                $accordion-padding-y;\n$accordion-body-padding-x:                $accordion-padding-x;\n\n$accordion-button-padding-y:              $accordion-padding-y;\n$accordion-button-padding-x:              $accordion-padding-x;\n$accordion-button-color:                  $accordion-color;\n$accordion-button-bg:                     $accordion-bg;\n$accordion-transition:                    $btn-transition, border-radius .15s ease;\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%);\n$accordion-button-active-color:           shade-color($primary, 10%);\n\n$accordion-button-focus-border-color:     $input-focus-border-color;\n$accordion-button-focus-box-shadow:       none;\n\n$accordion-icon-width:                    16px;\n$accordion-icon-color:                    $accordion-color;\n$accordion-icon-active-color:             $accordion-button-active-color;\n$accordion-icon-transition:               transform .2s ease-in-out;\n$accordion-icon-transform:                rotate(180deg);\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\");\n// scss-docs-end accordion-variables\n\n// Tooltips\n\n// scss-docs-start tooltip-variables\n$tooltip-font-size:                 $font-size-sm;\n$tooltip-max-width:                 200px;\n$tooltip-color:                     $white;\n$tooltip-bg:                        $black;\n$tooltip-border-radius:             $border-radius;\n$tooltip-opacity:                   .9;\n$tooltip-padding-y:                 .4rem;\n$tooltip-padding-x:                 .7rem;\n$tooltip-margin:                    0;\n\n$tooltip-arrow-width:               .8rem;\n$tooltip-arrow-height:              .4rem;\n$tooltip-arrow-color:               $tooltip-bg;\n// scss-docs-end tooltip-variables\n\n// Form tooltips must come after regular tooltips\n// scss-docs-start tooltip-feedback-variables\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x;\n$form-feedback-tooltip-font-size:     $tooltip-font-size;\n$form-feedback-tooltip-line-height:   $line-height-base;\n$form-feedback-tooltip-opacity:       $tooltip-opacity;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius;\n// scss-docs-end tooltip-feedback-variables\n\n\n// Popovers\n\n// scss-docs-start popover-variables\n$popover-font-size:                 $font-size-sm;\n$popover-bg:                        $white;\n$popover-max-width:                 276px;\n$popover-border-width:              $border-width;\n$popover-border-color:              $gray-200;\n$popover-border-radius:             $border-radius-lg;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width);\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2);\n\n$popover-header-bg:                 darken($popover-bg, 3%);\n$popover-header-color:              $headings-color;\n$popover-header-padding-y:          .5rem;\n$popover-header-padding-x:          .75rem;\n\n$popover-body-color:                $body-color;\n$popover-body-padding-y:            $popover-header-padding-y;\n$popover-body-padding-x:            $popover-header-padding-x;\n\n$popover-arrow-width:               1rem;\n$popover-arrow-height:              .5rem;\n$popover-arrow-color:               $popover-bg;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05);\n// scss-docs-end popover-variables\n\n\n// Toasts\n\n// scss-docs-start toast-variables\n$toast-max-width:                   350px;\n$toast-padding-x:                   .75rem;\n$toast-padding-y:                   .25rem;\n$toast-font-size:                   .875rem;\n$toast-color:                       null;\n$toast-background-color:            rgba($white, .85);\n$toast-border-width:                1px;\n$toast-border-color:                rgba(0, 0, 0, .1);\n$toast-border-radius:               .25rem;\n$toast-box-shadow:                  $box-shadow;\n$toast-spacing:                     $container-padding-x;\n\n$toast-header-color:                $gray-600;\n$toast-header-background-color:     rgba($white, .85);\n$toast-header-border-color:         rgba(0, 0, 0, .05);\n// scss-docs-end toast-variables\n\n\n// Badges\n\n// scss-docs-start badge-variables\n$badge-font-size:                   75%;\n$badge-font-weight:                 $font-weight-medium;\n$badge-color:                       $white;\n$badge-padding-y:                   .25em;\n$badge-padding-x:                   .4em;\n$badge-border-radius:               $border-radius;\n// scss-docs-end badge-variables\n\n\n// Modals\n\n// scss-docs-start modal-variables\n$modal-inner-padding:               1rem;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem;\n$modal-dialog-margin:               .5rem;\n$modal-dialog-margin-y-sm-up:       1.75rem;\n\n$modal-title-line-height:           $line-height-base;\n\n$modal-content-color:               null;\n$modal-content-bg:                  $white;\n$modal-content-border-color:        $gray-300;\n$modal-content-border-width:        $border-width;\n$modal-content-border-radius:       $border-radius-lg;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width);\n$modal-content-box-shadow-xs:       $box-shadow-sm;\n$modal-content-box-shadow-sm-up:    $box-shadow;\n\n\n$modal-backdrop-bg:                 $black;\n$modal-backdrop-opacity:            .5;\n$modal-header-border-color:         $border-color;\n$modal-footer-border-color:         $modal-header-border-color;\n$modal-header-border-width:         $modal-content-border-width;\n$modal-footer-border-width:         $modal-header-border-width;\n$modal-header-padding-y:            $modal-inner-padding;\n$modal-header-padding-x:            $modal-inner-padding;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x; // Keep this for backwards compatibility\n\n$modal-sm:                          300px;\n$modal-md:                          500px;\n$modal-lg:                          800px;\n$modal-xl:                          1140px;\n\n$modal-fade-transform:              translate(0, -50px);\n$modal-show-transform:              none;\n$modal-transition:                  transform .3s ease-out;\n$modal-scale-transform:             scale(1.02);\n// scss-docs-end modal-variables\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n// scss-docs-start alert-variables\n$alert-padding-y:                   .75rem;\n$alert-padding-x:                   1.25rem;\n$alert-margin-bottom:               1rem;\n$alert-border-radius:               $border-radius;\n$alert-link-font-weight:            $font-weight-bold;\n$alert-border-width:                $border-width;\n\n$alert-bg-scale:                    -80%;\n$alert-border-scale:                -70%;\n$alert-color-scale:                 40%;\n\n$alert-dismissible-padding-r:       $alert-padding-x * 3; // 3x covers width of x plus default padding on either side\n// scss-docs-end alert-variables\n\n// Progress bars\n\n// scss-docs-start progress-variables\n$progress-height:                   .625rem;\n$progress-font-size:                $font-size-base * .75;\n$progress-bg:                       $gray-300;\n$progress-border-radius:            $border-radius;\n$progress-box-shadow:               $box-shadow-inset;\n$progress-bar-color:                $white;\n$progress-bar-bg:                   $primary;\n$progress-bar-animation-timing:     1s linear infinite;\n$progress-bar-transition:           width .6s ease;\n// scss-docs-end progress-variables\n\n\n// List group\n\n// scss-docs-start list-group-variables\n$list-group-color:                  null;\n$list-group-bg:                     $white;\n$list-group-border-color:           $border-color;\n$list-group-border-width:           $border-width;\n$list-group-border-radius:          $border-radius;\n\n$list-group-item-padding-y:         .75rem;\n$list-group-item-padding-x:         1.25rem;\n$list-group-item-bg-scale:          -80%;\n$list-group-item-color-scale:       40%;\n\n$list-group-hover-bg:               $gray-100;\n$list-group-active-color:           $component-active-color;\n$list-group-active-bg:              $component-active-bg;\n$list-group-active-border-color:    $list-group-active-bg;\n\n$list-group-disabled-color:         $gray-600;\n$list-group-disabled-bg:            $list-group-bg;\n\n$list-group-action-color:           $gray-700;\n$list-group-action-hover-color:     $list-group-action-color;\n\n$list-group-action-active-color:    $body-color;\n$list-group-action-active-bg:       $gray-200;\n// scss-docs-end list-group-variables\n\n\n// Image thumbnails\n\n// scss-docs-start thumbnail-variables\n$thumbnail-padding:                 .25rem;\n$thumbnail-bg:                      $body-bg;\n$thumbnail-border-width:            $border-width;\n$thumbnail-border-color:            $gray-300;\n$thumbnail-border-radius:           $border-radius;\n$thumbnail-box-shadow:              $box-shadow-inset;\n// scss-docs-end thumbnail-variables\n\n\n// Figures\n\n// scss-docs-start figure-variables\n$figure-caption-font-size:          90%;\n$figure-caption-color:              $gray-600;\n// scss-docs-end figure-variables\n\n\n// Breadcrumbs\n\n// scss-docs-start breadcrumb-variables\n$breadcrumb-font-size:              null;\n$breadcrumb-padding-y:              .75rem;\n$breadcrumb-padding-x:              1rem;\n$breadcrumb-item-padding:           .5rem;\n\n$breadcrumb-margin-bottom:          1rem;\n\n$breadcrumb-bg:                     $gray-200;\n$breadcrumb-divider-color:          $gray-600;\n$breadcrumb-active-color:           $gray-300;\n$breadcrumb-divider:                quote(\"/\");\n$breadcrumb-divider-flipped:        $breadcrumb-divider;\n$breadcrumb-border-radius:          $border-radius;\n// scss-docs-end breadcrumb-variables\n\n// Carousel\n\n// scss-docs-start carousel-variables\n$carousel-control-color:             $white;\n$carousel-control-width:             15%;\n$carousel-control-opacity:           .5;\n$carousel-control-hover-opacity:     .9;\n$carousel-control-transition:        opacity .15s ease;\n\n$carousel-indicator-width:           30px;\n$carousel-indicator-height:          3px;\n$carousel-indicator-hit-area-height: 10px;\n$carousel-indicator-spacer:          3px;\n$carousel-indicator-opacity:         .5;\n$carousel-indicator-active-bg:       $white;\n$carousel-indicator-active-opacity:  1;\n$carousel-indicator-transition:      opacity .6s ease;\n\n$carousel-caption-width:             70%;\n$carousel-caption-color:             $white;\n$carousel-caption-padding-y:         1.25rem;\n$carousel-caption-spacer:            1.25rem;\n\n$carousel-control-icon-width:        2rem;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\");\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\");\n\n$carousel-transition-duration:       .6s;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black;\n$carousel-dark-caption-color:        $black;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100);\n// scss-docs-end carousel-variables\n\n\n// Spinners\n\n// scss-docs-start spinner-variables\n$spinner-width:           2rem;\n$spinner-height:          $spinner-width;\n$spinner-vertical-align:  -.125em;\n$spinner-border-width:    .25em;\n$spinner-animation-speed: .75s;\n\n$spinner-width-sm:        1rem;\n$spinner-height-sm:       $spinner-width-sm;\n$spinner-border-width-sm: .2em;\n// scss-docs-end spinner-variables\n\n\n// Close\n\n// scss-docs-start close-variables\n$btn-close-width:            1em;\n$btn-close-height:           $btn-close-width;\n$btn-close-padding-x:        .25em;\n$btn-close-padding-y:        $btn-close-padding-x;\n$btn-close-color:            $black;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\");\n$btn-close-bg-dark:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$black}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\");\n$btn-close-focus-shadow:     none;\n$btn-close-opacity:          .5;\n$btn-close-hover-opacity:    .75;\n$btn-close-focus-opacity:    1;\n$btn-close-disabled-opacity: .25;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%);\n// scss-docs-end close-variables\n\n// Offcanvas\n\n// scss-docs-start offcanvas-variables\n$offcanvas-padding-y:               $modal-inner-padding;\n$offcanvas-padding-x:               $modal-inner-padding;\n$offcanvas-horizontal-width:        400px;\n$offcanvas-vertical-height:         30vh;\n$offcanvas-transition-duration:     .3s;\n$offcanvas-border-color:            $modal-content-border-color;\n$offcanvas-border-width:            $modal-content-border-width;\n$offcanvas-title-line-height:       $modal-title-line-height;\n$offcanvas-bg-color:                $modal-content-bg;\n$offcanvas-color:                   $modal-content-color;\n$offcanvas-box-shadow:              $modal-content-box-shadow-xs;\n$offcanvas-backdrop-bg:             $modal-backdrop-bg;\n$offcanvas-backdrop-opacity:        $modal-backdrop-opacity;\n// scss-docs-end offcanvas-variables\n\n// Code\n\n$code-font-size:                    87.5%;\n$code-color:                        $pink;\n\n$kbd-padding-y:                     .2rem;\n$kbd-padding-x:                     .4rem;\n$kbd-font-size:                     $code-font-size;\n$kbd-color:                         $white;\n$kbd-bg:                            $gray-900;\n\n$pre-color:                         $gray-900;\n\n\n", "// \r\n// Page-title\r\n// \r\n\r\n.page-title-box {\r\n\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: 0;\r\n    }\r\n\r\n    h4 {\r\n        color: $white;\r\n        text-transform: uppercase;\r\n        font-weight: 500;\r\n        font-size: 16px !important;\r\n    }\r\n}\r\n\r\n.topbar-social-icon{\r\n    padding: calc(#{$header-height - 32px} / 2) 0;\r\n}\r\n\r\n.title-tooltip{\r\n    li{\r\n       i{\r\n        font-size: 20px;\r\n        margin-left: 10px;\r\n        color: $header-item-color;\r\n       }\r\n    }\r\n}", "// \r\n// _footer.scss\r\n// \r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 20px calc(#{$grid-gutter-width} / 2);\r\n    position: absolute;\r\n    right: 0;\r\n    border-top: 1px solid $gray-200;\r\n    color: $footer-color;\r\n    left: $sidebar-width;\r\n    height: $footer-height;\r\n    box-shadow: $box-shadow;\r\n    background-color: $footer-bg;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .footer {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n    .footer {\r\n        left: $sidebar-collapsed-width;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .footer {\r\n        left: 0 !important;\r\n    }  \r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $card-bg;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $gray-200;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\r\n// _menu.scss\r\n// \r\n\r\n.metismenu {\r\n    margin: 0;\r\n\r\n    li {\r\n        display: block;\r\n        width: 100%;\r\n    }\r\n\r\n    .mm-collapse {\r\n        display: none;\r\n\r\n        &:not(.mm-show) {\r\n            display: none;\r\n        }\r\n\r\n        &.mm-show {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n\r\n.vertical-menu {\r\n    width: $sidebar-width;\r\n    z-index: 1001;\r\n    background: $sidebar-bg;\r\n    bottom: 0;\r\n    margin-top: 0;\r\n    position: fixed;\r\n    top: $header-height;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.user-sidebar{\r\n    position: relative;\r\n    text-align: center;\r\n    background: url(../images/user-img.png);\r\n    background-color: $primary;\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center;\r\n    padding: 20px 0;\r\n    .user-img{\r\n        position: relative;\r\n        img{\r\n            width: 60px;\r\n            height: 60px;\r\n            border: 3px solid $success;\r\n            padding: 5px;\r\n        }\r\n        .avatar-online{\r\n            position: absolute;\r\n    bottom: 4px;\r\n    width: 10px;\r\n    height: 10px;\r\n    z-index: 1;\r\n    border: 2px solid transparent;\r\n    border-radius: 50%;\r\n    margin-left: -15px;\r\n        }\r\n    }\r\n}\r\n\r\n.main-content {\r\n    margin-left: $sidebar-width;\r\n    overflow: hidden;\r\n\r\n    .content {\r\n        padding: 0 15px 10px 15px;\r\n        margin-top: $header-height;\r\n    }\r\n}\r\n\r\n\r\n#sidebar-menu {\r\n    padding: 0px 0 30px 0;\r\n\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(90deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .has-arrow {\r\n        &:after {\r\n            content: \"\\F0142\";\r\n            font-family: 'Material Design Icons';\r\n            display: block;\r\n            float: right;\r\n            transition: transform .2s;\r\n            font-size: 1rem;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: .625rem 1.2rem;\r\n                color: $sidebar-menu-item-color;\r\n                position: relative;\r\n                font-size: 14.5px;\r\n                transition: all .4s;\r\n                margin: 0px 17px;\r\n                border-radius: 3px;\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    min-width: 1.75rem;\r\n                    padding-bottom: .125em;\r\n                    font-size: 16px;\r\n                    line-height: 1.40625rem;\r\n                    vertical-align: middle;\r\n                    color: $sidebar-menu-item-icon-color;\r\n                    transition: all .4s;\r\n                }\r\n\r\n                &:hover {\r\n                    color: $sidebar-menu-item-hover-color;\r\n\r\n                    i {\r\n                        color: $sidebar-menu-item-hover-color;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                margin-top: 5px;\r\n            }\r\n\r\n            ul.sub-menu {\r\n                padding: 0;\r\n\r\n                li {\r\n\r\n                    a {\r\n                        padding: .4rem 1.5rem .4rem 2.8rem;\r\n                        font-size: 14px;\r\n                        color: $sidebar-menu-sub-item-color;\r\n                        background-color: transparent !important;\r\n                        &:before{\r\n                            content: \"\\F09DF\";\r\n                            font-family: 'Material Design Icons';\r\n                            font-size: 20px;\r\n                            line-height: 10px;\r\n                            padding-right: 2px;\r\n                            vertical-align: middle;\r\n                            display: inline-block;\r\n                        }\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        padding: 0;\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 1.5rem .4rem 4rem;\r\n                                font-size: 14px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.menu-title {\r\n    padding: 12px 20px !important;\r\n    letter-spacing: .05em;\r\n    pointer-events: none;\r\n    cursor: default;\r\n    font-size: 11px;\r\n    text-transform: uppercase;\r\n    color: $sidebar-menu-item-icon-color;\r\n    font-weight: $font-weight-semibold;\r\n}\r\n\r\n.mm-active {\r\n    color: $sidebar-menu-item-active-color !important;\r\n    > a{\r\n        color: $sidebar-menu-item-active-color !important;\r\n        background-color: $sidebar-menu-item-active-bg !important;\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    > i {\r\n        color: $sidebar-menu-item-active-color !important;\r\n    }\r\n    .active {\r\n        color: $sidebar-menu-item-active-color !important;\r\n        background-color: $sidebar-menu-item-active-bg !important;\r\n\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .vertical-menu {\r\n        display: none;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    body.sidebar-enable {\r\n        .vertical-menu {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n\r\n    .user-sidebar{\r\n        display: none;\r\n    }\r\n\r\n\r\n    .main-content {\r\n        margin-left: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        width: $sidebar-collapsed-width !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    // Side menu\r\n    .vertical-menu {\r\n        position: absolute;\r\n        width: $sidebar-collapsed-width !important;\r\n        z-index: 5;\r\n\r\n        .simplebar-mask,\r\n        .simplebar-content-wrapper {\r\n            overflow: visible !important;\r\n        }\r\n\r\n        .simplebar-scrollbar {\r\n            display: none !important;\r\n        }\r\n\r\n        .simplebar-offset {\r\n            bottom: 0 !important;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            .menu-title,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n\r\n            .has-arrow {\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 55px;\r\n                        transition: none;\r\n                        margin: 0;\r\n                        \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $sidebar-menu-item-hover-color;\r\n                        }\r\n\r\n                        i {\r\n                            font-size: 1.15rem;\r\n                            margin-left: 4px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$sidebar-collapsed-width});\r\n                            background-color: $sidebar-menu-item-active-bg;\r\n                            transition: none;\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        >ul {\r\n                            display: block;\r\n                            left: $sidebar-collapsed-width;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n                            }\r\n\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                color: $sidebar-menu-sub-item-color;\r\n                                margin: 0;\r\n\r\n                                &:hover {\r\n                                    color: $sidebar-menu-item-hover-color;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $sidebar-bg;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                height: auto !important;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li.active {\r\n                        a {\r\n                            color: $gray-100;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n\r\n    .user-sidebar{\r\n        background: none;\r\n    }\r\n\r\n\r\n    .vertical-menu {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    #sidebar-menu {\r\n    \r\n        ul {\r\n            li {\r\n                a {\r\n                    color: $sidebar-dark-menu-item-color;\r\n\r\n                    i {\r\n                        color: $sidebar-dark-menu-item-icon-color;\r\n                    }\r\n    \r\n                    &:hover {\r\n                        color: $sidebar-dark-menu-item-hover-color;\r\n\r\n                        i {\r\n                            color: $sidebar-dark-menu-item-hover-color;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n\r\n                        a {\r\n                            color: $sidebar-dark-menu-sub-item-color;\r\n\r\n                            &:hover {\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        min-height: 1400px;\r\n\r\n        // Side menu\r\n        .vertical-menu {\r\n\r\n            // Sidebar Menu\r\n            #sidebar-menu {\r\n\r\n                > ul {\r\n                    > li {\r\n                        \r\n                        &:hover {\r\n                            > a {\r\n                                background: lighten($sidebar-dark-bg, 2%);\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                                i{\r\n                                    color: $sidebar-dark-menu-item-hover-color;\r\n                                }\r\n                            }\r\n\r\n                            >ul {\r\n                                a{\r\n                                    color: $sidebar-dark-menu-sub-item-color;\r\n                                    &:hover{\r\n                                        color: $sidebar-menu-item-hover-color;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    ul{\r\n                        background-color: lighten($card-bg, 1%);\r\n                    }\r\n                    \r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $sidebar-dark-menu-item-active-color !important;\r\n        > a{\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            background-color: $sidebar-dark-menu-item-active-bg !important;\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n        }\r\n        .active {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            background-color: $sidebar-dark-menu-item-active-bg !important;\r\n\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $sidebar-dark-menu-item-icon-color;\r\n    }\r\n}\r\n\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n// Compact Sidebar\r\n\r\nbody[data-sidebar-size=\"small\"] {\r\n    .navbar-brand-box{\r\n        width: $sidebar-width-sm;\r\n    }\r\n    .vertical-menu{\r\n        width: $sidebar-width-sm;\r\n        text-align: center;\r\n\r\n        .has-arrow:after,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n    }\r\n    .main-content {\r\n        margin-left: $sidebar-width-sm;\r\n    }\r\n    .footer {\r\n        left: $sidebar-width-sm;\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul li {\r\n            a{\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n            ul.sub-menu {\r\n                li{\r\n                    a{\r\n                        padding-left: 1.5rem;\r\n                        &:before{\r\n                            display: none;\r\n                        }\r\n                    }\r\n                   \r\n                    \r\n                    ul.sub-menu {\r\n                        li {\r\n                            a{\r\n                                padding-left: 1.5rem;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.vertical-collpsed {\r\n        .main-content {\r\n            margin-left: $sidebar-collapsed-width;\r\n        }\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: left;\r\n                >ul{\r\n                    >li{\r\n                        >a {\r\n                            i{\r\n                                display: inline-block;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .footer {\r\n            left: $sidebar-collapsed-width;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// RTL\r\n[dir=\"rtl\"] \r\n#sidebar-menu {\r\n    .has-arrow{\r\n        &:after{\r\n            content: \"\\F0141\";\r\n            transition: transform .2s;\r\n        }\r\n    }\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(90deg);\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// _horizontal.scss\r\n// \r\n// \r\n// _horizontal.scss\r\n// \r\n\r\n.topnav {\r\n    background: $topnav-bg;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2);\r\n    box-shadow: $box-shadow;\r\n    margin-top: $header-height;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    \r\n    .topnav-menu {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .navbar-nav {\r\n        \r\n        .nav-link {\r\n            font-size: 15px;\r\n            position: relative;\r\n            padding: 1.2rem 1.5rem;\r\n            color: $sidebar-menu-item-color;\r\n            i{\r\n                font-size: 15px;\r\n                top: 2px;\r\n                position: relative;\r\n            }\r\n            &:focus, &:hover{\r\n                color: $sidebar-menu-item-active-color;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        \r\n        .dropdown-item{\r\n            color: $sidebar-menu-item-color;\r\n            &.active, &:hover{\r\n                color: $sidebar-menu-item-active-color;\r\n                background: transparent;\r\n            }\r\n        }\r\n        \r\n        .nav-item{\r\n            .nav-link.active{\r\n                color: $sidebar-menu-item-active-color;\r\n            }\r\n        }\r\n\r\n        .dropdown{\r\n            &.active{\r\n              >a {\r\n                    color: $sidebar-menu-item-active-color;\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n    body[data-layout=\"horizontal\"] {\r\n        .container-fluid,\r\n        .navbar-header {\r\n            max-width: 85%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            padding: .5rem 1.5rem;\r\n            min-width: 180px;\r\n        }\r\n\r\n        .dropdown {\r\n            &.mega-dropdown{\r\n                // position: static;\r\n                .mega-dropdown-menu{\r\n                    left: 0px;\r\n                    right: auto;\r\n                }\r\n            }\r\n            .dropdown-menu {\r\n                margin-top: 0;\r\n                border-radius: 0 0 $dropdown-border-radius $dropdown-border-radius;\r\n\r\n                .arrow-down {\r\n                    &::after {\r\n                        right: 15px;\r\n                        transform: rotate(-135deg) translateY(-50%);\r\n                        position: absolute;\r\n                    }\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu {\r\n                        position: absolute;\r\n                        top: 0 !important;\r\n                        left: 100%;\r\n                        display: none\r\n                    }\r\n                }\r\n            }\r\n\r\n            &:hover {\r\n                >.dropdown-menu {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown:hover>.dropdown-menu>.dropdown:hover>.dropdown-menu {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .navbar-toggle {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.arrow-down {\r\n    display: inline-block;\r\n\r\n    &:after {\r\n        border-color: initial;\r\n        border-style: solid;\r\n        border-width: 0 0 1px 1px;\r\n        content: \"\";\r\n        height: .4em;\r\n        display: inline-block;\r\n        right: 5px;\r\n        top: 50%;\r\n        margin-left: 10px;\r\n        transform: rotate(-45deg) translateY(-50%);\r\n        transform-origin: top;\r\n        transition: all .3s ease-out;\r\n        width: .4em;\r\n    }\r\n}\r\n\r\n\r\n\r\n@include media-breakpoint-down(xl) {\r\n    .topnav-menu {\r\n        .navbar-nav {\r\n            li {\r\n                &:last-of-type {\r\n                    .dropdown {\r\n                        .dropdown-menu {\r\n                            right: 100%;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(lg) {\r\n\r\n    .navbar-brand-box{\r\n        .logo-dark {\r\n            display: $display-block;\r\n            span.logo-sm{\r\n                display: $display-block;\r\n            }\r\n        }\r\n    \r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n    \r\n    .topnav {\r\n        max-height: 360px;\r\n        overflow-y: auto;\r\n        padding: 0;\r\n        .navbar-nav {\r\n            .nav-link {\r\n                padding: 0.75rem 1.1rem;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                background-color: transparent;\r\n                border: none;\r\n                box-shadow: none;\r\n                padding-left: 20px;\r\n                &.dropdown-mega-menu-xl{\r\n                    width: auto;\r\n    \r\n                    .row{\r\n                        margin: 0px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .dropdown-item {\r\n                position: relative;\r\n                background-color: transparent;\r\n\r\n                &.active,\r\n                &:active {\r\n                    color: $sidebar-menu-item-active-color;\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-down {\r\n            &::after {\r\n                right: 15px;\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n        .navbar-brand-box{\r\n            .logo-dark {\r\n                display: $display-block;\r\n            }\r\n        \r\n            .logo-light {\r\n                display: $display-none;\r\n            }\r\n        }\r\n        .topnav{\r\n            background-color: #141b2d;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.5);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                    >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n//  Topbar \r\n\r\nbody[data-layout=\"horizontal\"]{\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n}\r\n\r\n\r\n\r\nbody[data-topbar=\"colored\"] {\r\n    #page-topbar { \r\n        background-color: tint-color($header-colored-bg, 20%);\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .title-tooltip{\r\n        li{\r\n           i{\r\n            color:  rgba($white,0.8);\r\n           }\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color:  rgba($white,0.5);\r\n    \r\n        &:hover {\r\n            color: $white;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color:  rgba($white,0.5);\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}", "// Bootstrap functions\n//\n// Utility mixins and functions for evaluating source code across our variables, maps, and mixins.\n\n// Ascending\n// Used to evaluate Sass maps like our grid breakpoints.\n@mixin _assert-ascending($map, $map-name) {\n  $prev-key: null;\n  $prev-num: null;\n  @each $key, $num in $map {\n    @if $prev-num == null or unit($num) == \"%\" or unit($prev-num) == \"%\" {\n      // Do nothing\n    } @else if not comparable($prev-num, $num) {\n      @warn \"Potentially invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} whose unit makes it incomparable to #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    } @else if $prev-num >= $num {\n      @warn \"Invalid value for #{$map-name}: This map must be in ascending order, but key '#{$key}' has value #{$num} which isn't greater than #{$prev-num}, the value of the previous key '#{$prev-key}' !\";\n    }\n    $prev-key: $key;\n    $prev-num: $num;\n  }\n}\n\n// Starts at zero\n// Used to ensure the min-width of the lowest breakpoint starts at 0.\n@mixin _assert-starts-at-zero($map, $map-name: \"$grid-breakpoints\") {\n  @if length($map) > 0 {\n    $values: map-values($map);\n    $first-value: nth($values, 1);\n    @if $first-value != 0 {\n      @warn \"First breakpoint in #{$map-name} must start at 0, but starts at #{$first-value}.\";\n    }\n  }\n}\n\n// Colors\n@function to-rgb($value) {\n  @return red($value), green($value), blue($value);\n}\n\n@function rgba-css-var($identifier, $target) {\n  @return rgba(var(--#{$variable-prefix}#{$identifier}-rgb), var(--#{$variable-prefix}#{$target}-opacity));\n}\n\n// stylelint-disable scss/dollar-variable-pattern\n@function map-loop($map, $func, $args...) {\n  $_map: ();\n\n  @each $key, $value in $map {\n    // allow to pass the $key and $value of the map as an function argument\n    $_args: ();\n    @each $arg in $args {\n      $_args: append($_args, if($arg == \"$key\", $key, if($arg == \"$value\", $value, $arg)));\n    }\n\n    $_map: map-merge($_map, ($key: call(get-function($func), $_args...)));\n  }\n\n  @return $_map;\n}\n// stylelint-enable scss/dollar-variable-pattern\n\n@function varify($list) {\n  $result: null;\n  @each $entry in $list {\n    $result: append($result, var(--#{$variable-prefix}#{$entry}), space);\n  }\n  @return $result;\n}\n\n// Internal Bootstrap function to turn maps into its negative variant.\n// It prefixes the keys with `n` and makes the value negative.\n@function negativify-map($map) {\n  $result: ();\n  @each $key, $value in $map {\n    @if $key != 0 {\n      $result: map-merge($result, (\"n\" + $key: (-$value)));\n    }\n  }\n  @return $result;\n}\n\n// Get multiple keys from a sass map\n@function map-get-multiple($map, $values) {\n  $result: ();\n  @each $key, $value in $map {\n    @if (index($values, $key) != null) {\n      $result: map-merge($result, ($key: $value));\n    }\n  }\n  @return $result;\n}\n\n// Merge multiple maps\n@function map-merge-multiple($maps...) {\n  $merged-maps: ();\n\n  @each $map in $maps {\n    $merged-maps: map-merge($merged-maps, $map);\n  }\n  @return $merged-maps;\n}\n\n// Replace `$search` with `$replace` in `$string`\n// Used on our SVG icon backgrounds for custom forms.\n//\n// <AUTHOR> Giraudel\n// @param {String} $string - Initial string\n// @param {String} $search - Substring to replace\n// @param {String} $replace ('') - New value\n// @return {String} - Updated string\n@function str-replace($string, $search, $replace: \"\") {\n  $index: str-index($string, $search);\n\n  @if $index {\n    @return str-slice($string, 1, $index - 1) + $replace + str-replace(str-slice($string, $index + str-length($search)), $search, $replace);\n  }\n\n  @return $string;\n}\n\n// See https://codepen.io/kevinweber/pen/dXWoRw\n//\n// Requires the use of quotes around data URIs.\n\n@function escape-svg($string) {\n  @if str-index($string, \"data:image/svg+xml\") {\n    @each $char, $encoded in $escaped-characters {\n      // Do not escape the url brackets\n      @if str-index($string, \"url(\") == 1 {\n        $string: url(\"#{str-replace(str-slice($string, 6, -3), $char, $encoded)}\");\n      } @else {\n        $string: str-replace($string, $char, $encoded);\n      }\n    }\n  }\n\n  @return $string;\n}\n\n// Color contrast\n// See https://github.com/twbs/bootstrap/pull/30168\n\n// A list of pre-calculated numbers of pow(divide((divide($value, 255) + .055), 1.055), 2.4). (from 0 to 255)\n// stylelint-disable-next-line scss/dollar-variable-default, scss/dollar-variable-pattern\n$_luminance-list: .0008 .001 .0011 .0013 .0015 .0017 .002 .0022 .0025 .0027 .003 .0033 .0037 .004 .0044 .0048 .0052 .0056 .006 .0065 .007 .0075 .008 .0086 .0091 .0097 .0103 .011 .0116 .0123 .013 .0137 .0144 .0152 .016 .0168 .0176 .0185 .0194 .0203 .0212 .0222 .0232 .0242 .0252 .0262 .0273 .0284 .0296 .0307 .0319 .0331 .0343 .0356 .0369 .0382 .0395 .0409 .0423 .0437 .0452 .0467 .0482 .0497 .0513 .0529 .0545 .0561 .0578 .0595 .0612 .063 .0648 .0666 .0685 .0704 .0723 .0742 .0762 .0782 .0802 .0823 .0844 .0865 .0887 .0908 .0931 .0953 .0976 .0999 .1022 .1046 .107 .1095 .1119 .1144 .117 .1195 .1221 .1248 .1274 .1301 .1329 .1356 .1384 .1413 .1441 .147 .15 .1529 .1559 .159 .162 .1651 .1683 .1714 .1746 .1779 .1812 .1845 .1878 .1912 .1946 .1981 .2016 .2051 .2086 .2122 .2159 .2195 .2232 .227 .2307 .2346 .2384 .2423 .2462 .2502 .2542 .2582 .2623 .2664 .2705 .2747 .2789 .2831 .2874 .2918 .2961 .3005 .305 .3095 .314 .3185 .3231 .3278 .3325 .3372 .3419 .3467 .3515 .3564 .3613 .3663 .3712 .3763 .3813 .3864 .3916 .3968 .402 .4072 .4125 .4179 .4233 .4287 .4342 .4397 .4452 .4508 .4564 .4621 .4678 .4735 .4793 .4851 .491 .4969 .5029 .5089 .5149 .521 .5271 .5333 .5395 .5457 .552 .5583 .5647 .5711 .5776 .5841 .5906 .5972 .6038 .6105 .6172 .624 .6308 .6376 .6445 .6514 .6584 .6654 .6724 .6795 .6867 .6939 .7011 .7084 .7157 .7231 .7305 .7379 .7454 .7529 .7605 .7682 .7758 .7835 .7913 .7991 .807 .8148 .8228 .8308 .8388 .8469 .855 .8632 .8714 .8796 .8879 .8963 .9047 .9131 .9216 .9301 .9387 .9473 .956 .9647 .9734 .9823 .9911 1;\n\n@function color-contrast($background, $color-contrast-dark: $color-contrast-dark, $color-contrast-light: $color-contrast-light, $min-contrast-ratio: $min-contrast-ratio) {\n  $foregrounds: $color-contrast-light, $color-contrast-dark, $white, $black;\n  $max-ratio: 0;\n  $max-ratio-color: null;\n\n  @each $color in $foregrounds {\n    $contrast-ratio: contrast-ratio($background, $color);\n    @if $contrast-ratio > $min-contrast-ratio {\n      @return $color;\n    } @else if $contrast-ratio > $max-ratio {\n      $max-ratio: $contrast-ratio;\n      $max-ratio-color: $color;\n    }\n  }\n\n  @warn \"Found no color leading to #{$min-contrast-ratio}:1 contrast ratio against #{$background}...\";\n\n  @return $max-ratio-color;\n}\n\n@function contrast-ratio($background, $foreground: $color-contrast-light) {\n  $l1: luminance($background);\n  $l2: luminance(opaque($background, $foreground));\n\n  @return if($l1 > $l2, divide($l1 + .05, $l2 + .05), divide($l2 + .05, $l1 + .05));\n}\n\n// Return WCAG2.0 relative luminance\n// See https://www.w3.org/WAI/GL/wiki/Relative_luminance\n// See https://www.w3.org/TR/WCAG20-TECHS/G17.html#G17-tests\n@function luminance($color) {\n  $rgb: (\n    \"r\": red($color),\n    \"g\": green($color),\n    \"b\": blue($color)\n  );\n\n  @each $name, $value in $rgb {\n    $value: if(divide($value, 255) < .03928, divide(divide($value, 255), 12.92), nth($_luminance-list, $value + 1));\n    $rgb: map-merge($rgb, ($name: $value));\n  }\n\n  @return (map-get($rgb, \"r\") * .2126) + (map-get($rgb, \"g\") * .7152) + (map-get($rgb, \"b\") * .0722);\n}\n\n// Return opaque color\n// opaque(#fff, rgba(0, 0, 0, .5)) => #808080\n@function opaque($background, $foreground) {\n  @return mix(rgba($foreground, 1), $background, opacity($foreground) * 100);\n}\n\n// scss-docs-start color-functions\n// Tint a color: mix a color with white\n@function tint-color($color, $weight) {\n  @return mix(white, $color, $weight);\n}\n\n// Shade a color: mix a color with black\n@function shade-color($color, $weight) {\n  @return mix(black, $color, $weight);\n}\n\n// Shade the color if the weight is positive, else tint it\n@function shift-color($color, $weight) {\n  @return if($weight > 0, shade-color($color, $weight), tint-color($color, -$weight));\n}\n// scss-docs-end color-functions\n\n// Return valid calc\n@function add($value1, $value2, $return-calc: true) {\n  @if $value1 == null {\n    @return $value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 + $value2;\n  }\n\n  @return if($return-calc == true, calc(#{$value1} + #{$value2}), $value1 + unquote(\" + \") + $value2);\n}\n\n@function subtract($value1, $value2, $return-calc: true) {\n  @if $value1 == null and $value2 == null {\n    @return null;\n  }\n\n  @if $value1 == null {\n    @return -$value2;\n  }\n\n  @if $value2 == null {\n    @return $value1;\n  }\n\n  @if type-of($value1) == number and type-of($value2) == number and comparable($value1, $value2) {\n    @return $value1 - $value2;\n  }\n\n  @if type-of($value2) != number {\n    $value2: unquote(\"(\") + $value2 + unquote(\")\");\n  }\n\n  @return if($return-calc == true, calc(#{$value1} - #{$value2}), $value1 + unquote(\" - \") + $value2);\n}\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n", "// \r\n// _layouts.scss\r\n// \r\n\r\nbody[data-layout-size=\"boxed\"] {\r\n    background-color: $boxed-body-bg;\r\n    #layout-wrapper {\r\n        background-color: $body-bg;\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n        box-shadow: $box-shadow;\r\n    }\r\n\r\n    #page-topbar {\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .footer {\r\n        margin: 0 auto;\r\n        max-width: calc(#{$boxed-layout-width} - #{$sidebar-width});\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .footer {\r\n            max-width: calc(#{$boxed-layout-width} - #{$sidebar-collapsed-width});\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Horizontal Boxed Layout\r\n\r\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"]{\r\n    #page-topbar, #layout-wrapper, .footer {\r\n        max-width: 100%;\r\n    }\r\n    .container-fluid, .navbar-header {\r\n        max-width: $boxed-layout-width;\r\n    }\r\n}", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\n// avatar.scss\n//\n\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 2.5rem;\n  width: 2.5rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.mini-stat-icon{\n\n  width: 46px;\n  height: 46px;\n\n}\n\n.avatar-title {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .card {\r\n        + .card {\r\n            margin-top: 0.5rem;\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0415\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .card-header{\r\n        border-radius: 7px;\r\n    }\r\n}\r\n\r\n.custom-accordion-arrow{\r\n    .card{\r\n        border: 1px solid $border-color;\r\n        box-shadow: none;\r\n    }\r\n    .card-header{\r\n        padding-left: 45px;\r\n        position: relative;\r\n\r\n        .accor-arrow-icon{\r\n            position: absolute;\r\n            display: inline-block;\r\n            width: 24px;\r\n            height: 24px;\r\n            line-height: 24px;\r\n            font-size: 16px;\r\n            background-color: $primary;\r\n            color: $white;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            left: 10px;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-arrow-icon {\r\n                &:before {\r\n                    content: \"\\F0142\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "//\n// _helper.scss\n//\n\n\n.font-size-10 {\n    font-size: 10px !important;\n}\n\n.font-size-11 {\n    font-size: 11px !important;\n}\n\n.font-size-12 {\n    font-size: 12px !important;\n}\n\n.font-size-13 {\n    font-size: 13px !important;\n}\n\n.font-size-14 {\n    font-size: 14px !important;\n}\n\n.font-size-15 {\n    font-size: 15px !important;\n}\n\n.font-size-16 {\n    font-size: 16px !important;\n}\n\n.font-size-17 {\n    font-size: 17px !important;\n}\n\n.font-size-18 {\n    font-size: 18px !important;\n}\n\n.font-size-20 {\n    font-size: 20px !important;\n}\n\n.font-size-22 {\n    font-size: 22px !important;\n}\n\n.font-size-24 {\n    font-size: 24px !important;\n}\n\n\n// media\n\n.media{\n    display: flex;\n    align-items: flex-start;\n  }\n  \n  .media-body {\n    flex: 1;\n  }\n  \n\n\n\n// Social\n\n.social-list-item {\n    height: 2rem;\n    width: 2rem;\n    line-height: calc(2rem - 2px);\n    display: block;\n    border: 1px solid $gray-500;\n    border-radius: 50%;\n    color: $gray-500;\n    text-align: center;\n    transition: all 0.4s;\n\n    &:hover {\n        color: $gray-600;\n        background-color: $gray-200;\n    }\n}\n\n\n.w-xs {\n    min-width: 80px;\n}\n\n.w-sm {\n    min-width: 95px;\n}\n\n.w-md {\n    min-width: 110px;\n}\n\n.w-lg {\n    min-width: 140px;\n}\n\n.w-xl {\n    min-width: 160px;\n}\n\n// overlay\n\n.bg-overlay {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    top: 0;\n    opacity: 0.7;\n    background-color: $black;\n}\n\n// flex-1\n\n.flex-1{\n    flex: 1;\n}\n\n\n\n// alert\n\n.alert-dismissible {\n    .btn-close {\n        font-size: 10px;\n        padding: $alert-padding-y * 1.4 $alert-padding-x;\n        background: transparent escape-svg($btn-close-bg-dark) center / $btn-close-width auto no-repeat;\n    }\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: $card-bg;\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    width: 40px;\r\n    height: 40px;\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner-chase {\r\n    margin: 0 auto;\r\n    width: 40px;\r\n    height: 40px;\r\n    position: relative;\r\n    animation: spinner-chase 2.5s infinite linear both;\r\n}\r\n\r\n.chase-dot {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0; \r\n    animation: chase-dot 2.0s infinite ease-in-out both; \r\n    &:before {\r\n        content: '';\r\n        display: block;\r\n        width: 25%;\r\n        height: 25%;\r\n        background-color: $primary;\r\n        border-radius: 100%;\r\n        animation: chase-dot-before 2.0s infinite ease-in-out both; \r\n    }\r\n\r\n    &:nth-child(1) { \r\n        animation-delay: -1.1s; \r\n        &:before{\r\n            animation-delay: -1.1s;\r\n        }\r\n    }\r\n    &:nth-child(2) { \r\n        animation-delay: -1.0s;\r\n        &:before{\r\n            animation-delay: -1.0s;\r\n        }\r\n    }\r\n    &:nth-child(3) { \r\n        animation-delay: -0.9s;\r\n        &:before{\r\n            animation-delay: -0.9s;\r\n        } \r\n    }\r\n    &:nth-child(4) { \r\n        animation-delay: -0.8s; \r\n        &:before{\r\n            animation-delay: -0.8s;\r\n        } \r\n    }\r\n    &:nth-child(5) { \r\n        animation-delay: -0.7s; \r\n        &:before{\r\n            animation-delay: -0.7s;\r\n        } \r\n    }\r\n    &:nth-child(6) { \r\n        animation-delay: -0.6s; \r\n        &:before{\r\n            animation-delay: -0.6s;\r\n        }\r\n    }\r\n}\r\n\r\n@keyframes spinner-chase {\r\n    100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot {\r\n    80%, 100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot-before {\r\n    50% {\r\n        transform: scale(0.4); \r\n    } \r\n    100%, 0% {\r\n        transform: scale(1.0); \r\n    } \r\n}", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n// checkbox input right\r\n\r\n.form-check-right{\r\n  padding-left: 0;\r\n  display: inline-block;\r\n  padding-right: $form-check-padding-start;;\r\n  .form-check-input{\r\n    float: right;\r\n    margin-left: 0;\r\n    margin-right: $form-check-padding-start * -1;\r\n  }\r\n  .form-check-label{\r\n    display: block;\r\n  }\r\n}\r\n\r\n.form-check{\r\n  position: relative;\r\n  text-align: left /*rtl: right*/;\r\n}\r\n\r\n\r\n.form-check-label{\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}", "// \r\n// Widgets.scss\r\n// \r\n\r\n// \r\n// Widgets.scss\r\n// \r\n\r\n.dash-summary{\r\n    border-top: 1px solid $gray-300;\r\n}\r\n\r\n.dash-main-border{\r\n    border-bottom: 1px solid $gray-300;\r\n}\r\n\r\n.dash-info-widget{\r\n    background: $dash-info-bg;\r\n}\r\n\r\n.dash-goal{\r\n    border-left: 1px solid $gray-300;\r\n}\r\n@media (max-width: 768px) {\r\n    .dash-goal{\r\n        border-left: none;\r\n    }\r\n}\r\n\r\n.carousel-indicators {\r\n    bottom: -20px;\r\n    button{\r\n        background-color: $primary !important;\r\n        width: 10px !important;\r\n        height: 10px !important;\r\n        border-radius: 50% !important;\r\n        margin: 5px;\r\n        opacity: 0.5;\r\n    }\r\n}\r\n\r\n.mini-stats-wid{\r\n    .mini-stat-icon{\r\n        overflow: hidden;\r\n        position: relative;\r\n        &:before, &:after{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 8px;\r\n            height: 54px;\r\n            background-color: rgba($white,.1);\r\n            left: 16px;\r\n            transform: rotate(32deg);\r\n            top: -5px;\r\n            transition: all 0.4s;\r\n        }\r\n\r\n        &::after{\r\n            left: -12px;\r\n            width: 12px;\r\n            transition: all 0.2s;\r\n        }\r\n    }\r\n\r\n    &:hover{\r\n        .mini-stat-icon{\r\n            &::after{\r\n                left: 60px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Inbox widget\r\n\r\n.inbox-wid{\r\n    .inbox-list-item{\r\n        a{\r\n            color: $body-color;\r\n            display: block;\r\n            padding: 11px 0px;\r\n            border-bottom: 1px solid $border-color;\r\n        }\r\n\r\n        &:first-child{\r\n            a{\r\n                padding-top: 0px;\r\n            }\r\n        }\r\n\r\n        &:last-child{\r\n            a{\r\n                border-bottom: 0px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// activity widget\r\n\r\n.activity-border{\r\n    &:before{\r\n        content: \"\";\r\n        position: absolute;\r\n        height: 38px;\r\n        border-left: 2px dashed $gray-300;\r\n        top: 40px;\r\n        left: 0px;\r\n    }\r\n}\r\n\r\n.activity-wid{\r\n    margin-left: 16px;\r\n\r\n    .activity-list{\r\n        \r\n        position: relative;\r\n        padding: 0 0 33px 30px;\r\n     \r\n        .activity-icon{\r\n            position: absolute;\r\n            left: -20px;\r\n            top: 0px;\r\n            z-index: 2;\r\n        }\r\n\r\n        &:last-child{\r\n            padding-bottom: 0px;\r\n        }\r\n    }\r\n\r\n \r\n   \r\n}\r\n", "// \r\n// _demos.scss\r\n// \r\n\r\n// Demo Only\r\n.button-items {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n// Lightbox \r\n\r\n.mfp-popup-form {\r\n    max-width: 1140px;\r\n}\r\n\r\n// Modals\r\n\r\n.bs-example-modal {\r\n    position: relative;\r\n    top: auto;\r\n    right: auto;\r\n    bottom: auto;\r\n    left: auto;\r\n    z-index: 1;\r\n    display: block;\r\n  }\r\n\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icon-demo-content {\r\n  text-align: center;\r\n  color: $gray-500;\r\n\r\n  i{\r\n    display: block;\r\n    font-size: 24px;\r\n    color: $gray-600;\r\n    width: 48px;\r\n    height: 48px;\r\n    line-height: 46px;\r\n    margin: 0px auto;\r\n    margin-bottom: 16px;\r\n    border-radius: 4px;\r\n    border: 1px solid $border-color;\r\n    transition: all 0.4s;\r\n  }\r\n\r\n  .col-lg-4 {\r\n    margin-top: 24px;\r\n\r\n    &:hover {\r\n      i {\r\n        background-color: $primary;\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n\r\n// card radio\r\n\r\n.card-radio{\r\n  background-color: $card-bg;\r\n  border: 2px solid $card-border-color;\r\n  border-radius: $border-radius;\r\n  padding: 1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n\r\n  &:hover{\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.card-radio-label{\r\n  display: block;\r\n}\r\n\r\n\r\n.card-radio-input{\r\n  display: none;\r\n  &:checked + .card-radio {\r\n    border-color: $primary !important;\r\n  }\r\n}\r\n\r\n.navs-carousel{\r\n  .owl-nav{\r\n      margin-top: 16px;\r\n      button{\r\n          width: 30px;\r\n          height: 30px;\r\n          line-height: 28px !important;\r\n          font-size: 20px !important;\r\n          border-radius: 50% !important;\r\n          background-color: rgba($primary, 0.25) !important;\r\n          color: $primary !important;\r\n          margin: 4px 8px !important;\r\n      }\r\n  }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .vertical-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-header,\r\n    .footer {\r\n        display: none !important;\r\n    }\r\n    .card-body,\r\n    .main-content,\r\n    .right-bar,\r\n    .page-content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .card{\r\n        border: 0;\r\n    }\r\n}", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.fc-toolbar {\r\n  h2 {\r\n      font-size: 16px;\r\n      line-height: 30px;\r\n      text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.fc {\r\n  th.fc-widget-header {\r\n      background: $light;\r\n      font-size: 13px;\r\n      line-height: 20px;\r\n      padding: 10px 0;\r\n      text-transform: uppercase;\r\n      font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n.fc-unthemed{\r\n  .fc-content, \r\n  .fc-divider, \r\n  .fc-list-heading td, \r\n  .fc-list-view, \r\n  .fc-popover, \r\n  .fc-row, \r\n  tbody, \r\n  td, \r\n  th, \r\n  thead{\r\n      border-color: $light;\r\n  }\r\n  td.fc-today {\r\n      background: lighten($gray-200, 4%);\r\n  }\r\n}\r\n\r\n.fc-button {\r\n  background: $card-bg;\r\n  border-color: $border-color;\r\n  color: $gray-700;\r\n  text-transform: capitalize;\r\n  box-shadow: none;\r\n  padding: 6px 12px !important;\r\n  height: auto !important;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n  background-color: $primary;\r\n  color: $white;\r\n  text-shadow: none;\r\n}\r\n\r\n.fc-event {\r\n  border-radius: 2px;\r\n  border: none;\r\n  cursor: move;\r\n  font-size: 0.8125rem;\r\n  margin: 5px 7px;\r\n  padding: 5px 5px;\r\n  text-align: center;\r\n}\r\n\r\n#external-events .external-event {\r\n  text-align: left!important;\r\n  padding: 8px 16px;\r\n}\r\n\r\n.fc-event, .fc-event-dot{\r\n  background-color: $primary;\r\n}\r\n\r\n.fc-event .fc-content{\r\n  color: $white;\r\n}\r\n\r\n.fc {\r\n  .table-bordered {\r\n    td, th {\r\n      border-color: $table-group-separator-color;\r\n    }\r\n  }\r\n  \r\n  .fc-toolbar {\r\n    @media (max-width: 575.98px) {\r\n      display: block;\r\n    }\r\n    \r\n      h2 {\r\n          font-size: 16px;\r\n          line-height: 30px;\r\n          text-transform: uppercase;\r\n      }\r\n\r\n      @media (max-width: 767.98px) {\r\n\r\n          .fc-left,\r\n          .fc-right,\r\n          .fc-center {\r\n              float: none;\r\n              display: block;\r\n              text-align: center;\r\n              clear: both;\r\n              margin: 10px 0;\r\n          }\r\n\r\n          >*>* {\r\n              float: none;\r\n          }\r\n\r\n          .fc-today-button {\r\n              display: none;\r\n          }\r\n      }\r\n      \r\n      .btn {\r\n          text-transform: capitalize;\r\n      }\r\n\r\n  }\r\n}\r\n.fc-bootstrap .fc-today.alert-info{\r\n  background-color: $gray-300;\r\n}\r\n\r\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\r\n  background-color: $black !important;\r\n}\r\n\r\n// RTL\r\n[dir=\"rtl\"] .fc-header-toolbar {\r\n  direction: ltr !important;\r\n}\r\n\r\n[dir=\"rtl\"] .fc-toolbar>*>:not(:first-child) {\r\n  margin-left: .75em;\r\n}\r\n\r\n\r\n\r\n", "\r\n//\r\n// colorpicker.scss\r\n//\r\n\r\n.sp-container{\r\n  background-color: $dropdown-bg;\r\n  z-index: 1000;\r\n  button{\r\n    padding: .25rem .5rem;\r\n      font-size: .71094rem;\r\n      border-radius: .2rem;\r\n      font-weight: 400;\r\n      color: $dark;\r\n  \r\n      &.sp-palette-toggle{\r\n        background-color: $light;\r\n      }\r\n      \r\n      &.sp-choose{\r\n        background-color: $success;\r\n        margin-left: 5px;\r\n        margin-right: 0;\r\n      }\r\n  }\r\n}\r\n\r\n.sp-palette-container{\r\n  border-right: 1px solid $border-color;\r\n}\r\n\r\n.sp-input{\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color !important;\r\n  color: $input-color;\r\n  &:focus{\r\n    outline: none;\r\n  }\r\n}\r\n\r\n\r\n[dir=\"rtl\"]{\r\n\r\n  .sp-alpha{\r\n    direction: rtl;\r\n  }\r\n\r\n  .sp-original-input-container {\r\n    .sp-add-on{\r\n      border-top-right-radius: 0!important;\r\n      border-bottom-right-radius: 0!important;\r\n      border-top-left-radius: 4px!important;\r\n      border-bottom-left-radius: 4px!important\r\n    }\r\n  } \r\n\r\n  input.spectrum.with-add-on{\r\n    border: 1px solid $input-border-color;\r\n    border-left: 0;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n\r\n  }\r\n}", "//\r\n// session-timeout.scss\r\n//\r\n\r\n#session-timeout-dialog {\r\n    .close {\r\n        display: none;\r\n    }\r\n\r\n    .countdown-holder {\r\n        color: $danger;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $danger;\r\n        box-shadow: none;\r\n    }\r\n}", "//\r\n// Range slider\r\n//\r\n\r\n.irs {\r\n    font-family: $font-family-base;\r\n}\r\n\r\n.irs--round {\r\n\r\n    .irs-bar,\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        background: $primary !important;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        &:before {\r\n            display: none;\r\n        }\r\n    }\r\n\r\n    .irs-line {\r\n        background: $gray-300;\r\n        border-color: $gray-300;\r\n    }\r\n\r\n    .irs-grid-text {\r\n        font-size: 11px;\r\n        color: $gray-500;\r\n    }\r\n\r\n    .irs-min,\r\n    .irs-max {\r\n        color: $gray-500;\r\n        background: $gray-300;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-handle {\r\n        border: 2px solid $primary;\r\n        width: 10px;\r\n        height: 16px;\r\n        top: 29px;\r\n        background-color: $card-bg !important;\r\n    }\r\n}", "\r\n//\r\n//  Sweetalert2\r\n//\r\n\r\n.swal2-container {\r\n  .swal2-title{\r\n    font-size: 24px;\r\n    font-weight: $font-weight-medium;\r\n  }  \r\n}\r\n\r\n.swal2-content{\r\n  font-size: 16px;\r\n}\r\n\r\n.swal2-icon{\r\n  &.swal2-question{\r\n    border-color: $info;\r\n    color: $info;\r\n  }\r\n  &.swal2-success {\r\n    [class^=swal2-success-line]{\r\n      background-color: $success;\r\n    }\r\n\r\n    .swal2-success-ring{\r\n      border-color: rgba($success, 0.3);\r\n    }\r\n  }\r\n  &.swal2-warning{\r\n    border-color: $warning;\r\n    color: $warning;\r\n  }\r\n}\r\n\r\n.swal2-styled{\r\n  &:focus{\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.swal2-progress-steps {\r\n  .swal2-progress-step{\r\n    background: $primary;\r\n    &.swal2-active-progress-step{\r\n      background: $primary;\r\n      &~.swal2-progress-step, &~.swal2-progress-step-line{\r\n        background: rgba($primary, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .swal2-progress-step-line{\r\n    background: $primary;\r\n  }\r\n}\r\n\r\n.swal2-loader{\r\n  border-color: $primary transparent $primary transparent;\r\n}\r\n", "\r\n//\r\n// Rating\r\n//\r\n\r\n.symbol{\r\n  border-color: $card-bg;\r\n}\r\n\r\n.rating-symbol-background, .rating-symbol-foreground {\r\n  font-size: 24px;\r\n}\r\n\r\n.rating-symbol-foreground {\r\n  top: 0px;\r\n}\r\n\r\n.rating-star{\r\n  > span{\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    &.badge{\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Parsley\r\n//\r\n\r\n.error {\r\n  color: $danger;\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-errors-list {\r\n  display: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  &.filled {\r\n    display: block;\r\n  }\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "\r\n//\r\n// Select 2\r\n//\r\n\r\n.select2-container {\r\n  display: block;\r\n  .select2-selection--single {\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color;\r\n    height: 38px;\r\n    &:focus{\r\n      outline: none;\r\n    }\r\n\r\n    .select2-selection__rendered {\r\n      line-height: 36px;\r\n      padding-left: 12px;\r\n      color: $input-color;\r\n    }\r\n\r\n    .select2-selection__arrow {\r\n      height: 34px;\r\n      width: 34px;\r\n      right: 3px;\r\n\r\n      b{\r\n        border-color: $gray-500 transparent transparent transparent;\r\n        border-width: 6px 6px 0 6px;\r\n      }\r\n    }\r\n\r\n    .select2-selection__placeholder{\r\n      color: $body-color;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--open {\r\n  .select2-selection--single {\r\n\r\n    .select2-selection__arrow {\r\n\r\n      b{\r\n        border-color: transparent transparent $gray-500 transparent !important;\r\n        border-width: 0 6px 6px 6px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default {\r\n  .select2-search--dropdown {\r\n      padding: 10px;\r\n      background-color: $dropdown-bg;\r\n      .select2-search__field {\r\n          border: 1px solid  $input-border-color;\r\n          background-color: $input-bg;\r\n          color: $gray-600;\r\n          outline: none;\r\n      }\r\n  }\r\n  .select2-results__option--highlighted[aria-selected] {\r\n      background-color: $primary;\r\n  }\r\n  .select2-results__option[aria-selected=true] {\r\n      background-color: $dropdown-link-active-bg;\r\n      color: $dropdown-link-active-color;\r\n      &:hover {\r\n          background-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\n.select2-results__option {\r\n  padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n  border: 1px solid $dropdown-border-color;\r\n  background-color: $dropdown-bg;\r\n  box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-search {\r\n  input{\r\n    border: 1px solid $gray-300;\r\n  }\r\n}\r\n\r\n.select2-container {\r\n  .select2-selection--multiple {\r\n    min-height: 38px;\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color !important;\r\n  \r\n    .select2-selection__rendered {\r\n      padding: 2px 10px;\r\n    }\r\n    .select2-search__field {\r\n      border: 0;\r\n      color: $input-color;\r\n      &::placeholder{\r\n          color: $input-color;\r\n      }\r\n  }\r\n    .select2-selection__choice {\r\n      background-color: $gray-200;\r\n      border: 1px solid $gray-300;\r\n      border-radius: 1px;\r\n      padding: 0 7px;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default{\r\n  &.select2-container--focus {\r\n    .select2-selection--multiple{\r\n      border-color: $gray-400;\r\n    }\r\n  }\r\n\r\n  .select2-results__group{\r\n    font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n// ajax select\r\n\r\n.select2-result-repository__avatar{\r\n    float: left;\r\n    width: 60px;\r\n    margin-right: 10px;\r\n  img{\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.select2-result-repository__statistics{\r\n  margin-top: 7px;\r\n}\r\n\r\n.select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  display: inline-block;\r\n  font-size: 11px;\r\n  margin-right: 1em;\r\n  color: $gray-500;\r\n\r\n  .fa{\r\n    margin-right: 4px;\r\n\r\n    &.fa-flash{\r\n      &::before{\r\n        content: \"\\f0e7\";\r\n        font-family: 'Font Awesome 5 Free';\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-results__option--highlighted{\r\n  .select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  color: rgba($white, 0.8);\r\n}\r\n}\r\n\r\n.select2-result-repository__meta{\r\n  overflow: hidden;\r\n}\r\n\r\n\r\n// templating-select\r\n\r\n.img-flag{\r\n  margin-right: 7px;\r\n  height: 15px;\r\n  width: 18px;\r\n}\r\n\r\n", "//\r\n//  Sweetalert2\r\n//\r\n\r\n/* CSS Switch */\r\ninput[switch] {\r\n  display: none;\r\n  + label {\r\n    font-size: 1em;\r\n    line-height: 1;\r\n    width: 56px;\r\n    height: 24px;\r\n    background-color: $gray-400;\r\n    background-image: none;\r\n    border-radius: 2rem;\r\n    padding: 0.16667rem;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    text-align: center;\r\n    position: relative;\r\n    font-weight: $font-weight-medium;\r\n    transition: all 0.1s ease-in-out;\r\n    &:before {\r\n      color: $dark;\r\n      content: attr(data-off-label);\r\n      display: block;\r\n      font-family: inherit;\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n      line-height: 21px;\r\n      position: absolute;\r\n      right: 1px;\r\n      margin: 3px;\r\n      top: -2px;\r\n      text-align: center;\r\n      min-width: 1.66667rem;\r\n      overflow: hidden;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 3px;\r\n      background-color: $gray-200;\r\n      box-shadow: none;\r\n      border-radius: 2rem;\r\n      height: 20px;\r\n      width: 20px;\r\n      top: 2px;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n  }\r\n\r\n  &:checked + label {\r\n    background-color: $primary;\r\n  }\r\n}\r\n\r\ninput[switch]:checked + label {\r\n  background-color: $primary;\r\n  &:before {\r\n    color: $white;\r\n    content: attr(data-on-label);\r\n    right: auto;\r\n    left: 3px;\r\n  }\r\n\r\n  &:after {\r\n    left: 33px;\r\n    background-color: $gray-200;\r\n  }\r\n}\r\n\r\ninput[switch=\"bool\"] + label {\r\n  background-color: $danger;\r\n}\r\ninput[switch=\"bool\"] + label:before,input[switch=\"bool\"]:checked + label:before,\r\ninput[switch=\"default\"]:checked + label:before{\r\n  color: $white;\r\n}\r\n\r\ninput[switch=\"bool\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"default\"]:checked + label {\r\n  background-color: #a2a2a2;\r\n}\r\n\r\ninput[switch=\"primary\"]:checked + label {\r\n  background-color: $primary;\r\n}\r\n\r\ninput[switch=\"success\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"info\"]:checked + label {\r\n  background-color: $info;\r\n}\r\n\r\ninput[switch=\"warning\"]:checked + label {\r\n  background-color: $warning;\r\n}\r\n\r\ninput[switch=\"danger\"]:checked + label {\r\n  background-color: $danger;\r\n}\r\n\r\ninput[switch=\"dark\"]:checked + label {\r\n  background-color: $dark;\r\n}\r\n\r\n.square-switch{\r\n  margin-right: 7px;\r\n  input[switch]+label, input[switch]+label:after{\r\n    border-radius: 4px;\r\n  }\r\n}", "\r\n//\r\n// Datepicker\r\n//\r\n\r\n.datepicker {\r\n  border: 1px solid $gray-100;\r\n  padding: 8px;\r\n  z-index: 999 !important;\r\n  table{\r\n    tr{\r\n      th{\r\n        font-weight: 500;\r\n      }\r\n      td{\r\n        &.active, &.active:hover, .active.disabled, &.active.disabled:hover,\r\n        &.today,  &.today:hover, &.today.disabled, &.today.disabled:hover, \r\n        &.selected, &.selected:hover, &.selected.disabled, &.selected.disabled:hover{\r\n          background-color: $primary !important;\r\n          background-image: none;\r\n          box-shadow: none;\r\n          color: $white !important;\r\n        }\r\n\r\n        &.day.focused,\r\n        &.day:hover,\r\n        span.focused,\r\n        span:hover {\r\n            background: $gray-200;\r\n        }\r\n\r\n        &.new,\r\n        &.old,\r\n        span.new,\r\n        span.old {\r\n            color: $gray-500;\r\n            opacity: 0.6;\r\n        }\r\n\r\n        &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n            background-color: $gray-300;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-condensed{\r\n  >thead>tr>th, >tbody>tr>td {\r\n    padding: 7px;\r\n  }\r\n}", "\r\n//\r\n// Bootstrap touchspin\r\n//\r\n\r\n\r\n.bootstrap-touchspin{\r\n    &.input-group{\r\n      &>.input-group-prepend{\r\n        &>.btn, &>.input-group-text{\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  \r\n    &.input-group{\r\n      &>.input-group-append{\r\n        &>.btn, &>.input-group-text{\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .bootstrap-touchspin{\r\n    .input-group-btn-vertical{\r\n      right: 0 !important;\r\n    left: 100% !important;\r\n    }\r\n    .bootstrap-touchspin-up{\r\n      border-top-right-radius: 4px !important;\r\n      border-bottom-right-radius: 0 !important;\r\n      border-top-left-radius: 0 !important;\r\n      border-bottom-left-radius: 0 !important;\r\n    }\r\n    .bootstrap-touchspin-down{\r\n      border-top-right-radius: 0 !important;\r\n    border-bottom-right-radius: 4px !important;\r\n    border-top-left-radius: 0 !important;\r\n    border-bottom-left-radius: 0 !important;\r\n    }\r\n  }", "//\r\n// datatable.scss\r\n\r\n.table-bordered {\r\n  border: $table-border-width solid $table-border-color;\r\n}\r\n\r\n\r\ndiv.dataTables_wrapper {\r\n  div.dataTables_filter{\r\n    text-align: right;\r\n    input{\r\n      margin-left: 0.5em;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}", "//\r\n// Form editors.scss\r\n//\r\n\r\n// Tinymce \r\n\r\n.tox-tinymce {\r\n    border: 2px solid $gray-300 !important;\r\n}\r\n\r\n.tox {\r\n    .tox-statusbar {\r\n        border-top: 1px solid $gray-300 !important;\r\n    }\r\n\r\n    .tox-menubar,\r\n    .tox-edit-area__iframe,\r\n    .tox-statusbar {\r\n        background-color: $card-bg !important;\r\n        background: none !important;\r\n    }\r\n\r\n    .tox-mbtn {\r\n        color: $gray-700 !important;\r\n\r\n        &:hover:not(:disabled):not(.tox-mbtn--active) {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-tbtn {\r\n        &:hover {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-toolbar__primary {\r\n        border-color: $gray-300 !important;\r\n    }\r\n\r\n    .tox-toolbar,\r\n    .tox-toolbar__overflow,\r\n    .tox-toolbar__primary {\r\n        background: $gray-300 !important;\r\n    }\r\n\r\n    .tox-tbtn {\r\n        color: $gray-700 !important;\r\n\r\n        svg {\r\n            fill: $gray-700 !important;\r\n        }\r\n    }\r\n\r\n    .tox-edit-area__iframe {\r\n        background-color: $card-bg !important;\r\n    }\r\n\r\n    .tox-statusbar a,\r\n    .tox-statusbar__path-item,\r\n    .tox-statusbar__wordcount {\r\n        color: $gray-700 !important;\r\n    }\r\n\r\n    &:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\r\n        border-right: 1px solid darken($gray-300, 5%) !important;\r\n    }\r\n}\r\n.tox-tinymce-aux {\r\n    z-index: 1000 !important;\r\n}\r\n", "\r\n//\r\n// Form-Upload\r\n//\r\n\r\n/* Dropzone */\r\n.dropzone {\r\n  min-height: 230px;\r\n  border: 2px dashed $gray-400;\r\n  background: $card-bg;\r\n  border-radius: 6px;\r\n\r\n  .dz-message {\r\n    font-size: 24px;\r\n    width: 100%;\r\n  }\r\n}", "//\r\n// Form Wizard\r\n//\r\n// twitter-bs-wizard\r\n\r\n.twitter-bs-wizard {\r\n\r\n    .twitter-bs-wizard-nav {\r\n        position: relative;\r\n\r\n      \r\n\r\n       .wizard-border{\r\n        &:before {\r\n            content: \"\";\r\n            width: 189px;\r\n            height: 2px;\r\n            background: rgba($primary, 0.2);\r\n            position: absolute;\r\n            top: 26px;\r\n            margin-left: 100px;\r\n\r\n           \r\n        }\r\n       }\r\n\r\n        .step-number {\r\n            display: inline-block;\r\n            border-radius: 30px;\r\n            padding: 4px 0px;\r\n            width: 200px;\r\n            line-height: 34px;\r\n            color: $primary;\r\n            text-align: center;\r\n            position: relative;\r\n            background-color: rgba($primary, 0.2);\r\n\r\n            @media (max-width: 991.98px) {\r\n                display: block;\r\n                margin: 0 auto 8px !important;\r\n                width: 170px;\r\n            }\r\n        }\r\n\r\n        .nav-link {\r\n            .step-title {\r\n                display: block;\r\n                margin-top: 8px;\r\n                font-weight: $font-weight-bold;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            &.active {\r\n                background-color: transparent;\r\n                color: $gray-700;\r\n\r\n                .step-number {\r\n                    background-color: $primary;\r\n                    color: $white;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .twitter-bs-wizard-pager-link {\r\n        padding-top: 24px;\r\n        padding-left: 0;\r\n        list-style: none;\r\n        margin-bottom: 0;\r\n\r\n        li {\r\n            display: inline-block;\r\n\r\n            a {\r\n                display: inline-block;\r\n                padding: .47rem .75rem;\r\n                background-color: $primary;\r\n                color: $white;\r\n                border-radius: .25rem;\r\n            }\r\n\r\n            &.disabled {\r\n                a {\r\n                    cursor: not-allowed;\r\n                    background-color: lighten($primary, 8%);\r\n                }\r\n            }\r\n\r\n            &.next {\r\n                float: right;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard-tab-content {\r\n    padding-top: 24px;\r\n    min-height: 262px;\r\n}\r\n\r\n\r\n@media (max-width:  1024px) {\r\n    .twitter-bs-wizard {\r\n        .twitter-bs-wizard-nav{\r\n               .wizard-border{\r\n                &:before {\r\n                    background: transparent !important; \r\n                }\r\n              }\r\n        }\r\n\r\n    }\r\n}", "\r\n//\r\n// Responsive Table\r\n//\r\n\r\n.table-rep-plugin {\r\n  .btn-toolbar {\r\n    display: block;\r\n  }\r\n  .table-responsive {\r\n    border: none !important;\r\n  }\r\n  .btn-group{\r\n    .btn-default {\r\n      background-color: $secondary;\r\n      color: $light;\r\n      border: 1px solid $secondary;\r\n      &.btn-primary {\r\n          background-color: $primary;\r\n          border-color: $primary;\r\n          color: $white;\r\n          box-shadow: 0 0 0 2px rgba($primary, .5);\r\n      }\r\n  }\r\n    &.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        right: 0;\r\n        transform: none !important;\r\n        top: 100% !important;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    th {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n\r\n  .checkbox-row {\r\n    padding-left: 40px;\r\n    color: $dropdown-color !important;\r\n\r\n    &:hover{\r\n      background-color: lighten($gray-200, 2%) !important;\r\n    }\r\n\r\n    label {\r\n      display: inline-block;\r\n      padding-left: 5px;\r\n      position: relative;\r\n      &::before {\r\n        -o-transition: 0.3s ease-in-out;\r\n        -webkit-transition: 0.3s ease-in-out;\r\n        background-color: $white;\r\n        border-radius: 3px;\r\n        border: 1px solid $gray-300;\r\n        content: \"\";\r\n        display: inline-block;\r\n        height: 17px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        position: absolute;\r\n        transition: 0.3s ease-in-out;\r\n        width: 17px;\r\n        outline: none !important;\r\n      }\r\n      &::after {\r\n        color: $gray-200;\r\n        display: inline-block;\r\n        font-size: 11px;\r\n        height: 16px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        padding-left: 3px;\r\n        padding-top: 1px;\r\n        position: absolute;\r\n        top: -1px;\r\n        width: 16px;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      z-index: 1;\r\n      outline: none !important;\r\n\r\n      &:disabled + label {\r\n        opacity: 0.65;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:focus + label {\r\n      &::before {\r\n        outline-offset: -2px;\r\n        outline: none;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::after {\r\n        content: \"\\f00c\";\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 900;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:disabled + label {\r\n      &::before {\r\n        background-color: $gray-100;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::before {\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n      &::after {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .fixed-solution {\r\n    .sticky-table-header{\r\n      top: $header-height !important;\r\n      background-color: $primary;\r\n      table{\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  table.focus-on tbody tr.focused th,\r\n  table.focus-on tbody tr.focused td,\r\n  .sticky-table-header {\r\n      background: $primary;\r\n      border-color: $primary;\r\n      color: $white;\r\n\r\n      table {\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n  @media (min-width: 992px) {\r\n    .fixed-solution {\r\n      .sticky-table-header{\r\n        top: $header-height + 78px !important;;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-striped{\r\n  >tbody{\r\n    >tr{\r\n      &:nth-of-type(odd).focused{\r\n        box-shadow: none !important;\r\n        td, th {\r\n          box-shadow: none !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Table editable\r\n//\r\n\r\n.table-edits{\r\n  input, select{\r\n    height: $input-height-sm;\r\n    padding: $input-padding-y-sm $input-padding-x-sm;\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    color: $input-color;\r\n    border-radius: $input-border-radius;\r\n    &:focus{\r\n      outline: none;\r\n      border-color: $input-focus-border-color;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// apexcharts.scss\r\n//\r\n.apex-charts {\r\n    min-height: 10px !important;\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n    .apexcharts-canvas {\r\n        margin: 0 auto;\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip-title,\r\n.apexcharts-tooltip-text {\r\n    font-family: $font-family-base !important;\r\n}\r\n\r\n.apexcharts-legend-series {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n.apexcharts-gridline {\r\n    pointer-events: none;\r\n    stroke: $apex-grid-color;\r\n}\r\n\r\n.apexcharts-legend-text {\r\n    color: $gray-600 !important;\r\n    font-family: $font-family-base !important;\r\n    font-size: 13px !important;\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: $white !important;\r\n}\r\n\r\n.apexcharts-yaxis,\r\n.apexcharts-xaxis {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n}", "//\r\n// chartist.scss\r\n//\r\n\r\n.ct-golden-section:before {\r\n    float: none;\r\n}\r\n\r\n.ct-chart {\r\n    max-height: 300px;\r\n   \r\n    .ct-label {\r\n        fill: $gray-500;\r\n        color: $gray-500;\r\n        font-size: 12px;\r\n        line-height: 1;\r\n    }\r\n}\r\n\r\n.ct-chart.simple-pie-chart-chartist {\r\n    .ct-label {\r\n        color: $white;\r\n        fill: $white;\r\n        font-size: 16px;\r\n    }\r\n}\r\n\r\n.ct-grid {\r\n    stroke: rgba($dark, 0.1);\r\n}\r\n\r\n.ct-chart {\r\n    .ct-series {\r\n        &.ct-series-a {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $primary;\r\n            }\r\n        }\r\n        &.ct-series-b {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $success;\r\n            }\r\n        }\r\n        &.ct-series-c {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $danger;\r\n            }\r\n        }\r\n        &.ct-series-d {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $info;\r\n            }\r\n        }\r\n        &.ct-series-e {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $success;\r\n            }\r\n        }\r\n        &.ct-series-f {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $dark;\r\n            }\r\n        }\r\n        &.ct-series-g {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $purple;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.ct-series-a {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $primary;\r\n    }\r\n}\r\n\r\n.ct-series-b {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $success;\r\n    }\r\n}\r\n\r\n.ct-series-c {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $warning;\r\n    }\r\n}\r\n\r\n.ct-series-d {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $success;\r\n    }\r\n}\r\n\r\n.ct-area {\r\n    fill-opacity: .33;\r\n}\r\n\r\n.chartist-tooltip {\r\n    position: absolute;\r\n    display: inline-block;\r\n    opacity: 0;\r\n    min-width: 10px;\r\n    padding: 2px 10px;\r\n    border-radius: 3px;\r\n    background: $dark;\r\n    color: $gray-300;\r\n    text-align: center;\r\n    pointer-events: none;\r\n    z-index: 1;\r\n    transition: opacity .2s linear;\r\n    &.tooltip-show {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.ct-line {\r\n    stroke-width: 3px;\r\n}\r\n\r\n.ct-point {\r\n    stroke-width: 7px;\r\n}", "\r\n\r\n/* Flot chart */\r\n\r\n.flotTip {\r\n  padding: 8px 12px !important;\r\n  background-color: $gray-800 !important;\r\n  border: $border-width solid $gray-800 !important;\r\n  box-shadow: $box-shadow;\r\n  z-index: 100;\r\n  color: $gray-200;\r\n  opacity: 1;\r\n  border-radius: 3px !important;\r\n  font-size: 14px !important;\r\n}\r\n\r\n.legend {\r\n  div {\r\n      background-color: transparent !important;\r\n  }\r\n  tr {\r\n      height: 30px;\r\n  }\r\n}\r\n\r\n.legendLabel {\r\n  padding-left: 5px;\r\n  line-height: 10px;\r\n  padding-right: 10px;\r\n  font-size: 13px;\r\n  font-weight: $font-weight-medium;\r\n  color: $gray-500;\r\n}\r\n\r\n.legendColorBox {\r\n  div {\r\n      border-radius: 3px;\r\n      div {\r\n          border-radius: 3px;\r\n      }\r\n  }\r\n}\r\n\r\n.float-lable-box {\r\n  table {\r\n      margin: 0 auto;\r\n  }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(sm) {\r\n  .legendLabel {\r\n      display: none;\r\n  }\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "\r\n//\r\n// Google map\r\n//\r\n\r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n  \r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $gray-800;\r\n    color: $gray-100;\r\n    font-family: $font-family-base;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// x editable.scss\r\n//\r\n\r\n.editable-input{\r\n    .form-control{\r\n      display: inline-block;\r\n    }\r\n  }\r\n  \r\n  .editable-buttons{\r\n    margin-left: 7px;\r\n    .editable-cancel{\r\n      margin-left: 7px;\r\n    }\r\n  }", "// \r\n// authentication.scss\r\n//\r\n\r\n.home-btn {\r\n    position: absolute;\r\n    top: 15px;\r\n    right: 25px;\r\n}\r\n\r\n\r\n\r\n.home-center {\r\n    display: table;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .home-desc-center {\r\n    display: table-cell;\r\n    vertical-align: middle;\r\n  }\r\n\r\n.authentication-bg {\r\n    background-image: url(../images/title-img.png);\r\n    height: 100vh;\r\n    background-size: cover;\r\n    background-position: center;\r\n}\r\n\r\n.authentication-bg .bg-overlay {\r\n    background-color: $primary;\r\n}\r\n\r\n\r\n// Erorr\r\n\r\n\r\n.error-page {\r\n    text-transform: uppercase;\r\n    background: repeating-linear-gradient(45deg, $primary, $primary 10px, $success 10px, $success 20px);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    font-size: 120px;\r\n    line-height: .8;\r\n    position: relative;\r\n}\r\n\r\n\r\n// FAQS\r\n\r\n\r\n.faq-icon{\r\n    i {\r\n        width: 30px;\r\n        height: 30px;\r\n        line-height: 28px;\r\n        border: 1px solid;\r\n        border-radius: 50%;\r\n        text-align: center;\r\n        float: right;\r\n        font-size: 16px;\r\n        display: inline-block;\r\n    }\r\n    &:after{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 30px;\r\n            height: 30px;\r\n            opacity: 0.2;\r\n            right: 50px;\r\n            margin-top: -10px;\r\n            border-radius: 50%;\r\n            background: $primary;\r\n        }\r\n}\r\n\r\n", "// \r\n// ecommerce.scss\r\n//\r\n\r\n// product\r\n\r\n.search-box{\r\n    .form-control{\r\n        border-radius: 30px;\r\n        padding-left: 40px;\r\n        border: 1px solid $gray-300;\r\n    }\r\n    .search-icon{\r\n        font-size: 16px;    \r\n        position: absolute;\r\n        left: 13px;\r\n        top: 0;\r\n        line-height: 38px;\r\n    }\r\n}\r\n\r\n\r\n.categories-group-list{\r\n    display: block;\r\n    color: $dark;\r\n    font-weight: $font-weight-medium;\r\n    padding: 8px 16px;\r\n\r\n    &[aria-expanded=\"true\"]{\r\n        background-color: $gray-300;\r\n    }\r\n\r\n    &:last-child{\r\n        border: 0;\r\n    }\r\n\r\n    &:hover{\r\n        color: $dark;\r\n    }\r\n}\r\n\r\n.categories-list{\r\n    padding: 8px 0px;\r\n    li{\r\n        a{\r\n            display: block;\r\n            padding: 4px 16px;\r\n            color: $body-color;\r\n        }\r\n\r\n        &.active{\r\n            a{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n// Product Details\r\n\r\n.product-detai-imgs{\r\n    .nav{\r\n        .nav-link{\r\n            margin: 7px 0px;\r\n\r\n            &.active{\r\n                background-color: $gray-300;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-color{\r\n    a{\r\n        display: inline-block;\r\n        text-align: center;\r\n        color: $body-color;\r\n\r\n        .product-color-item{\r\n            margin: 7px;\r\n            border: 2px solid $border-color;\r\n            border-radius: 4px;\r\n        }\r\n        &.active, &:hover{\r\n            color: $primary;\r\n            .product-color-item{\r\n                border-color: $primary !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-track{\r\n    border: 1px solid $border-color;\r\n}\r\n\r\n.ecommerce-sortby-list{\r\n    li{\r\n        color: $dark;\r\n        a{\r\n            color: $body-color;\r\n            padding: 4px;\r\n        }\r\n\r\n        &.active{\r\n            a{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-img{\r\n    position: relative;\r\n    \r\n    .product-ribbon{\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0px;\r\n        padding: 6px 8px;\r\n        border-radius: 50% 50% 25% 75%/44% 68% 32% 56%;\r\n        width: 62px;\r\n        height: 60px;\r\n        color: $white;\r\n        font-size: 15px;\r\n        text-align: center;\r\n    }\r\n\r\n    .product-like{\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        a{\r\n            display: inline-block;\r\n            width: 40px;\r\n            height: 40px;\r\n            border: 2px solid $gray-300;\r\n            line-height: 38px;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            color: $gray-500;\r\n        }\r\n    }\r\n}\r\n\r\n.product-detail{\r\n    .nav-pills{\r\n        .nav-link{\r\n            margin-bottom: 7px;\r\n            &.active{\r\n                background-color: $gray-300;\r\n            }\r\n\r\n\r\n            .tab-img{\r\n                width: 5rem;\r\n            }\r\n        }\r\n    }\r\n\r\n    .product-img{\r\n        border: 1px solid $border-color;\r\n        padding: 24px;\r\n    }\r\n}\r\n\r\n.product-desc-list{\r\n    li{\r\n        padding: 4px 0px;\r\n    }\r\n}\r\n\r\n.product-review-link{\r\n    .list-inline-item{\r\n        a{\r\n            color: $gray-600;\r\n        }\r\n        &:not(:last-child){\r\n            margin-right: 14px;\r\n        }\r\n    }           \r\n}\r\n\r\n// ecommerce cart\r\n\r\n.product-cart-touchspin{\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    border-radius: $border-radius;\r\n    .form-control{\r\n        border-color: transparent;\r\n        height: 32px\r\n    }\r\n    \r\n    .input-group-btn .btn{\r\n        background-color: transparent !important;\r\n        border-color: transparent !important;\r\n        color: $primary !important;\r\n        font-size: 16px;\r\n        padding: 3px 12px;\r\n        box-shadow: none;\r\n    }\r\n\r\n}\r\n\r\n// ecommerce checkout\r\n\r\n.shipping-address{\r\n    box-shadow: none;\r\n    &.active{\r\n        border-color: $primary !important;\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard {\r\n.chackout-border{\r\n    &:before {\r\n        content: \"\";\r\n        width: 139px;\r\n        height: 2px;\r\n        background: rgba($primary, 0.2);\r\n        position: absolute;\r\n        top: 26px;\r\n        margin-left: 100px;\r\n    }\r\n   }\r\n\r\n   .add-product-border{\r\n    &:before {\r\n        content: \"\";\r\n        width: 324px;\r\n        height: 2px;\r\n        background: rgba($primary, 0.2);\r\n        position: absolute;\r\n        top: 26px;\r\n        margin-left: 100px;\r\n    }\r\n   }\r\n}\r\n\r\n@media (max-width:  1024px) {\r\n    .twitter-bs-wizard {\r\n\r\n           .chackout-border, .add-product-border{\r\n            width: 180px;\r\n            &:before {\r\n                background: transparent !important; \r\n            }\r\n          }\r\n    }\r\n}", "/* ==============\r\n  Email\r\n===================*/\r\n.email-leftbar {\r\n  width: 236px;\r\n  float: left;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.email-rightbar {\r\n  margin-left: 260px;\r\n}\r\n\r\n.chat-user-box {\r\n  p.user-title {\r\n    color: $dark;\r\n    font-weight: 600;\r\n  }\r\n  p {\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .email-leftbar {\r\n    float: none;\r\n    width: 100%;\r\n  }\r\n  .email-rightbar {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.mail-list {\r\n  a {\r\n    display: block;\r\n    color: $gray-600;\r\n    line-height: 24px;\r\n    padding: 8px 5px;\r\n    &.active {\r\n      color: $danger;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: $gray-600;\r\n    }\r\n\r\n    &:hover {\r\n      background: $gray-300;\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n    &.active,\r\n    &.active:hover {\r\n      box-shadow: inset 3px 0 0 $primary;\r\n    }\r\n\r\n    \r\n  &.unread  {\r\n    background-color: $gray-300;\r\n    font-weight: 500;\r\n    color: darken($dark,5%);\r\n      a{\r\n        color: darken($dark,5%);\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    box-shadow: inset 0 0 0 1px $gray-400;\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      height: 20px;\r\n      width: 20px;\r\n      left: 0;\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      margin-bottom: 0;\r\n      transition-duration: .05s;\r\n      top: 0;\r\n      &:before {\r\n        content: \"\\F012C\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 0;\r\n        height: 20px;\r\n        color: darken($dark,5%);\r\n        width: 20px;\r\n        position: absolute;\r\n        margin-top: -16px;\r\n        left: 4px;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 575.98px) { \r\n  .message-list li .col-mail-1 {\r\n      width: 200px;\r\n  }\r\n}", "// \r\n// Chat.scss\r\n//\r\n\r\n.chat-leftsidebar{\r\n    @media (min-width: 992px) {\r\n      min-width: 380px;\r\n    }\r\n  \r\n  \r\n    .chat-leftsidebar-nav{\r\n      .nav{\r\n        background-color: $card-bg;\r\n      }\r\n  \r\n     \r\n    }\r\n  }\r\n  \r\n  .chat-noti-dropdown{\r\n    &.active{\r\n      &:before{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: $danger;\r\n        border-radius: 50%;\r\n        right: 0;\r\n      }\r\n    }\r\n  \r\n    .btn{\r\n      padding: 6px;\r\n      box-shadow: none;\r\n      font-size: 20px;\r\n    }\r\n  }\r\n  \r\n  .chat-list{\r\n    margin: 0;\r\n    li{\r\n      &.active{\r\n        a{\r\n          background-color: $card-bg;\r\n          box-shadow: $box-shadow;\r\n        }\r\n      }\r\n      a{\r\n        display: block;\r\n        padding: 14px 16px;\r\n        color: $gray-600;\r\n        transition: all 0.4s;\r\n        border: 1px solid $border-color;\r\n        border-radius: 4px;\r\n        margin-top: 10px;\r\n        &:hover{\r\n          background-color: $card-bg;\r\n          box-shadow: $box-shadow;\r\n  \r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  \r\n  .user-chat-nav{\r\n    .dropdown{\r\n      .nav-btn{\r\n        height: 40px;\r\n        width: 40px;\r\n        line-height: 34px;\r\n        box-shadow: none;\r\n        padding: 0;\r\n        font-size: 16px;\r\n        background-color: $light;\r\n        border-radius: 50%;\r\n      }\r\n  \r\n      .dropdown-menu{\r\n        box-shadow: $box-shadow;\r\n        border: 1px solid $border-color\r\n      }\r\n    }\r\n  }\r\n  \r\n  \r\n  .chat-conversation{\r\n    li{\r\n      clear: both;\r\n    }\r\n  \r\n    .chat-day-title{\r\n      position: relative;\r\n      text-align: center;\r\n      margin-bottom: 24px;\r\n  \r\n      .title{\r\n        background-color: $card-bg;\r\n        position: relative;\r\n        z-index: 1;\r\n        padding: 6px 24px;\r\n      }\r\n  \r\n      &:before{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 100%;\r\n        height: 1px;\r\n        left: 0;\r\n        right: 0;\r\n        background-color: $border-color;\r\n        top: 10px;\r\n      }\r\n      .badge{\r\n        font-size: 12px;\r\n      }\r\n    }\r\n    .conversation-list{\r\n      margin-bottom: 24px;\r\n      display: inline-block;\r\n      position: relative;\r\n      .arrow-left{\r\n        position: relative;\r\n        &:before{\r\n          content: \"\";\r\n       position: absolute;\r\n       top: 10px;\r\n       right: 100%;\r\n       border: 7px solid transparent;\r\n       border-right: 7px solid rgba($primary,0.1);\r\n         }\r\n      }\r\n  \r\n      .ctext-wrap{\r\n        padding: 12px 24px;\r\n        background-color: rgba($primary,0.1);\r\n        border-radius: 8px 8px 8px 0px;\r\n        overflow: hidden;\r\n       \r\n  \r\n        .conversation-name{\r\n          font-weight: 500;\r\n          color: $primary;\r\n          margin-bottom: 4px;\r\n          position: relative;\r\n      \r\n        }\r\n      }\r\n  \r\n      .dropdown{\r\n        float: right;\r\n        .dropdown-toggle{\r\n          font-size: 18px;\r\n          padding: 4px;\r\n          color: $gray-600;\r\n          @media (max-width: 575.98px) {\r\n            display: none;\r\n          }\r\n        }\r\n  \r\n        .dropdown-menu{\r\n          box-shadow: $box-shadow;\r\n          border: 1px solid $border-color\r\n        }\r\n      }\r\n  \r\n      .chat-time{\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  \r\n    .right{\r\n      .conversation-list{\r\n        float: right;\r\n        .arrow-right{\r\n          position: relative;\r\n          &:before{\r\n            content: \"\";\r\n         position: absolute;\r\n         top: 10px;\r\n         left: 100%;\r\n         border: 7px solid transparent;\r\n         border-left: 7px solid $light;\r\n           }\r\n        }\r\n        .ctext-wrap{\r\n          background-color: $light;\r\n          text-align: right;\r\n          border-radius: 8px 8px 0px 8px;\r\n        }\r\n        .dropdown{\r\n          float: left;\r\n        }\r\n  \r\n        &.last-chat{\r\n          .conversation-list{\r\n            &:before{\r\n              right: 0;\r\n              left: auto;\r\n            }\r\n          }\r\n        }\r\n      }\r\n  \r\n    }\r\n  \r\n  \r\n  }\r\n  \r\n  .chat-input-section{\r\n    border-top: 1px solid $border-color;\r\n  }\r\n  \r\n  .chat-input{\r\n    border-radius: 30px;\r\n    background-color: $light !important;\r\n    border-color:  $light !important;\r\n    padding-right: 120px;\r\n  }\r\n  \r\n  .chat-input-links{\r\n    position: absolute;\r\n    right: 16px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    li{\r\n      a{\r\n        font-size: 16px;\r\n        line-height: 36px;\r\n        padding: 0px 4px;\r\n        display: inline-block;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .chat-send{\r\n    @media (max-width: 575.98px) {\r\n      min-width: auto;\r\n    }\r\n  }\r\n  \r\n  \r\n  .search-box .search-icon {\r\n    font-size: 16px;\r\n    position: absolute;\r\n    left: 13px;\r\n    top: 2px;\r\n    font-size: 15px;\r\n    line-height: 34px;\r\n   \r\n  }\r\n  \r\n  .search-box .form-control {\r\n    padding-left: 40px;\r\n    border-radius: 5px;\r\n  }\r\n  \r\n  ", "// \r\n// coming-soon.scss\r\n//\r\n\r\n\r\n\r\n.counter-number {\r\n    font-size: 32px;\r\n    text-align: center;\r\n    span {\r\n        font-size: 16px;\r\n        display: block;\r\n        padding-top: 7px;\r\n    }\r\n}\r\n\r\n.coming-box {\r\n    float: left;\r\n    width: 21%;\r\n    padding: 14px 7px;\r\n    margin: 0px $grid-gutter-width/2 $grid-gutter-width $grid-gutter-width/2;\r\n    background-color: $white;\r\n    border-radius: 5px;\r\n    border-radius: $card-inner-border-radius;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n@media (max-width: 991.98px) { \r\n    .coming-box {\r\n        width: 40%;\r\n    }\r\n }", "// \r\n// timeline.scss\r\n//\r\n/************** vertical timeline **************/ \r\n\r\n\r\n.timeline {\r\n    position: relative;\r\n    width: 100%;\r\n    padding: 30px 0;\r\n  }\r\n  \r\n  .timeline .timeline-end,\r\n  .timeline .timeline-start,\r\n  .timeline .timeline-year {\r\n    position: relative;\r\n    width: 100%;\r\n    text-align: center;\r\n    z-index: 1;\r\n  }\r\n  \r\n  .timeline .timeline-end p,\r\n  .timeline .timeline-start p,\r\n  .timeline .timeline-year p {\r\n    display: inline-block;\r\n    width: 80px;\r\n    height: 80px;\r\n    margin: 0;\r\n    padding: 30px 0;\r\n    text-align: center;\r\n    background: url(../images/user-img.png);\r\n    background-color: $primary;\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    border-radius: 100px;\r\n    color: $white;\r\n    text-transform: uppercase;\r\n  }\r\n  \r\n  .timeline {\r\n    .timeline-year {\r\n      margin: 30px 0;\r\n    }\r\n    .timeline-continue {\r\n      position: relative;\r\n      width: 100%;\r\n      padding: 60px 0;\r\n      &:after {\r\n        position: absolute;\r\n        content: \"\";\r\n        width: 1px;\r\n        height: 100%;\r\n        top: 0;\r\n        left: 50%;\r\n        margin-left: -1px;\r\n        background: $primary;\r\n      }\r\n    }\r\n    .timeline-date {\r\n      margin: 40px 10px 0 10px;\r\n    }\r\n  }\r\n  \r\n  .timeline .row.timeline-left,\r\n  .timeline .row.timeline-right .timeline-date {\r\n    text-align: right;\r\n  }\r\n  \r\n  .timeline .row.timeline-right,\r\n  .timeline .row.timeline-left .timeline-date {\r\n    text-align: left;\r\n  }\r\n  \r\n  .timeline .timeline-date::after {\r\n    content: \"\";\r\n    display: block;\r\n    position: absolute;\r\n    width: 14px;\r\n    height: 14px;\r\n    top: 45px;\r\n    background: $primary;\r\n    border-radius: 15px;\r\n    z-index: 1;\r\n  }\r\n  \r\n  .timeline .row.timeline-left .timeline-date::after {\r\n    left: -7px;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-date::after {\r\n    right: -7px;\r\n  }\r\n  \r\n  .timeline .timeline-box,\r\n  .timeline .timeline-launch {\r\n    position: relative;\r\n    display: inline-block;\r\n    margin: 15px;\r\n    padding: 20px;\r\n    border: 1px solid $gray-200;\r\n    border-radius: 6px;\r\n  }\r\n  \r\n  .timeline .timeline-launch {\r\n    width: 100%;\r\n    margin: 15px 0;\r\n    padding: 0;\r\n    border: none;\r\n    text-align: center;\r\n    background: transparent;\r\n  }\r\n  \r\n  .timeline .timeline-box::after,\r\n  .timeline .timeline-box::before {\r\n    content: \"\";\r\n    display: block;\r\n    position: absolute;\r\n    width: 0;\r\n    height: 0;\r\n    border-style: solid;\r\n  }\r\n  \r\n  .timeline .row.timeline-left .timeline-box::after,\r\n  .timeline .row.timeline-left .timeline-box::before {\r\n    left: 100%;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-box::after,\r\n  .timeline .row.timeline-right .timeline-box::before {\r\n    right: 100%;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-box::after,\r\n  .timeline .timeline-launch .timeline-box::before {\r\n    left: 50%;\r\n    margin-left: -10px;\r\n  }\r\n  \r\n  .timeline .timeline-box::after {\r\n    top: 26px;\r\n    border-color: transparent transparent transparent $light;\r\n    border-width: 10px;\r\n  }\r\n  \r\n  .timeline .timeline-box::before {\r\n    top: 25px;\r\n    border-color: transparent transparent transparent $gray-200;\r\n    border-width: 11px;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-box::after {\r\n    border-color: transparent $light transparent transparent;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-box::before {\r\n    border-color: transparent $gray-200 transparent transparent;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-box::after {\r\n    top: -20px;\r\n    border-color: transparent transparent $gray-200 transparent;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-box::before {\r\n    top: -19px;\r\n    border-color: transparent transparent $light transparent;\r\n    border-width: 10px;\r\n    z-index: 1;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-text {\r\n    width: 100%;\r\n  }\r\n  \r\n  @media (max-width: 767px) {\r\n    .timeline .timeline-continue::after {\r\n      left: 40px;\r\n    }\r\n  \r\n    .timeline .timeline-end,\r\n    .timeline .timeline-start,\r\n    .timeline .timeline-year,\r\n    .timeline .row.timeline-left,\r\n    .timeline .row.timeline-right .timeline-date,\r\n    .timeline .row.timeline-right,\r\n    .timeline .row.timeline-left .timeline-date,\r\n    .timeline .timeline-launch {\r\n      text-align: left;\r\n    }\r\n  \r\n    .timeline .row.timeline-left .timeline-date::after,\r\n    .timeline .row.timeline-right .timeline-date::after {\r\n      left: 47px;\r\n    }\r\n  \r\n    .timeline .timeline-box,\r\n    .timeline .row.timeline-right .timeline-date,\r\n    .timeline .row.timeline-left .timeline-date {\r\n      margin-left: 55px;\r\n    }\r\n  \r\n    .timeline .timeline-launch .timeline-box {\r\n      margin-left: 0;\r\n    }\r\n  \r\n    .timeline .row.timeline-left .timeline-box::after {\r\n      left: -20px;\r\n      border-color: transparent $light transparent transparent;\r\n    }\r\n  \r\n    .timeline .row.timeline-left .timeline-box::before {\r\n      left: -22px;\r\n      border-color: transparent $gray-200 transparent transparent;\r\n    }\r\n  \r\n    .timeline .timeline-launch .timeline-box::after,\r\n    .timeline .timeline-launch .timeline-box::before {\r\n      left: 30px;\r\n      margin-left: 0;\r\n    }\r\n  }\r\n  ", "// \r\n// Extras pages.scss\r\n//\r\n\r\n\r\n// pricing\r\n\r\n.pricing-nav-tabs{\r\n    display: inline-block;\r\n    background-color: $card-bg;\r\n    box-shadow: $box-shadow;\r\n    padding: 4px;\r\n    border-radius: 7px;\r\n    li{\r\n        display: inline-block;\r\n    }\r\n}\r\n\r\n\r\n.pricing-box{\r\n    .plan-features{\r\n        li{\r\n            padding: 7px 0px;\r\n        }\r\n    }\r\n}\r\n\r\n/*********************\r\n    Faqs\r\n**********************/ \r\n\r\n.faq-nav-tabs{\r\n    .nav-item{\r\n        margin: 0px 8px;\r\n    }\r\n    .nav-link{\r\n        text-align: center;\r\n        margin-bottom: 8px;\r\n        border: 2px solid $border-color;\r\n        color: $body-color;\r\n        .nav-icon{\r\n            font-size: 40px;\r\n            margin-bottom: 8px;\r\n            display: block;\r\n        }\r\n\r\n        &.active{\r\n            border-color: $primary;\r\n            background-color: transparent;\r\n            color: $body-color;\r\n\r\n            .nav-icon{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.text-error{\r\n    font-size: 120px;\r\n    @media (max-width: 575.98px) {\r\n        font-size: 86px;\r\n    }\r\n}\r\n\r\n.error-text{\r\n    color: $danger;\r\n    position: relative;\r\n\r\n    .error-img{\r\n        position: absolute;\r\n        width: 120px;\r\n        left: -15px;\r\n        right: 0;\r\n        bottom: 47px;\r\n\r\n        @media (max-width: 575.98px) {\r\n            width: 86px;\r\n            left: -12px;\r\n            bottom: 38px;\r\n        }\r\n    }\r\n}"]}