from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.conf import settings
from django.http import HttpResponse
import os
import datetime
import subprocess
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a backup of the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            type=str,
            default='json',
            choices=['json', 'sql'],
            help='Backup format (json or sql)'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output filename (optional)'
        )

    def handle(self, *args, **options):
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_format = options['format']
        
        # Create backups directory if it doesn't exist
        backup_dir = os.path.join(settings.BASE_DIR, 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        if options['output']:
            filename = options['output']
        else:
            filename = f'hall_system_backup_{timestamp}.{backup_format}'
        
        backup_path = os.path.join(backup_dir, filename)
        
        try:
            if backup_format == 'json':
                # Django's dumpdata command
                with open(backup_path, 'w') as f:
                    call_command('dumpdata', 
                               '--natural-foreign', 
                               '--natural-primary',
                               '--indent', '2',
                               stdout=f)
                self.stdout.write(
                    self.style.SUCCESS(f'Database backup created: {backup_path}')
                )
            
            elif backup_format == 'sql':
                # PostgreSQL dump
                db_settings = settings.DATABASES['default']
                
                env = os.environ.copy()
                env['PGPASSWORD'] = db_settings['PASSWORD']
                
                cmd = [
                    'pg_dump',
                    '-h', db_settings['HOST'],
                    '-p', str(db_settings['PORT']),
                    '-U', db_settings['USER'],
                    '-d', db_settings['NAME'],
                    '-f', backup_path,
                    '--verbose'
                ]
                
                result = subprocess.run(cmd, env=env, capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.stdout.write(
                        self.style.SUCCESS(f'PostgreSQL backup created: {backup_path}')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f'Backup failed: {result.stderr}')
                    )
                    return
                    
            # Log the backup
            logger.info(f'Database backup created: {filename}')
            
            # Print file size
            file_size = os.path.getsize(backup_path) / (1024 * 1024)  # MB
            self.stdout.write(f'Backup size: {file_size:.2f} MB')
            
            return backup_path
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating backup: {str(e)}')
            )
            logger.error(f'Backup error: {str(e)}')
            raise 