from .models import UserProfile

def user_role(request):
    """
    Make user role available in all templates
    """
    is_admin = False
    user_role = None
    
    if request.user.is_authenticated:
        if request.user.is_superuser:
            is_admin = True
            user_role = 'superuser'
        else:
            try:
                profile = UserProfile.objects.get(user=request.user)
                user_role = profile.role
                is_admin = profile.role == UserProfile.ADMIN
            except UserProfile.DoesNotExist:
                # Create profile if it doesn't exist
                profile = UserProfile.objects.create(user=request.user)
                user_role = profile.role
                is_admin = False
    
    return {
        'is_admin': is_admin,
        'user_role': user_role,
    } 