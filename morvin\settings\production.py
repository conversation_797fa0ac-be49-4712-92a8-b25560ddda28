from .base import *
import os
import dj_database_url
import mimetypes

# Override SECRET_KEY for production security
SECRET_KEY = os.environ.get('SECRET_KEY', SECRET_KEY)

# Add proper MIME types for static files
mimetypes.add_type("text/css", ".css", True)
mimetypes.add_type("application/javascript", ".js", True)

DEBUG = False

# More explicit ALLOWED_HOSTS for Railway deployment
ALLOWED_HOSTS = [
    '*',  # Allow all hosts for production (you can restrict this later)
    '.railway.app',
    '*.railway.app', 
    'healthcheck.railway.app',  # Railway health check
    'software.sultanatmarquee.com',  # Custom domain
    'sultanatmarquee.com',  # Root domain
    '.sultanatmarquee.com',  # All subdomains
    '.vercel.app',
    '.now.sh',
    'localhost',
    '127.0.0.1'
]

# Database Configuration - Always prioritize DATABASE_URL (Railway PostgreSQL)
USE_SQLITE = os.environ.get('USE_SQLITE', 'false').lower() == 'true'

# Force PostgreSQL if DATABASE_URL is available (Railway PostgreSQL)
if 'DATABASE_URL' in os.environ:
    try:
        # Database configuration with Railway PostgreSQL
        database_url = os.environ.get('DATABASE_URL')

        # If using internal Railway URL and it fails, try with public connection
        if 'postgres.railway.internal' in database_url:
            print(f"⚠️  Using internal Railway PostgreSQL URL: {database_url[:50]}...")
            print("💡 If connection fails, update DATABASE_URL to use public Railway PostgreSQL URL")

        DATABASES = {
            'default': dj_database_url.parse(
                database_url,
                conn_max_age=600,
                conn_health_checks=True,
            )
        }
        print(f"✅ Using Railway PostgreSQL: {database_url[:50]}...")
        USE_SQLITE = False  # Override USE_SQLITE when DATABASE_URL is available

    except Exception as e:
        print(f"⚠️  Error parsing DATABASE_URL: {e}")
        print("🔄 Falling back to manual PostgreSQL configuration...")
        # Fall through to manual PostgreSQL config

elif not USE_SQLITE:
    # Use PostgreSQL with manual configuration (fallback)
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.environ.get('PGDATABASE', 'railway'),
            'USER': os.environ.get('PGUSER', 'postgres'),
            'PASSWORD': os.environ.get('PGPASSWORD', 'iNfIUYWESOQtgmTGwanOTJKASxaPntvh'),
            'HOST': os.environ.get('PGHOST', 'postgres.railway.internal'),
            'PORT': os.environ.get('PGPORT', '5432'),
            'OPTIONS': {
                'connect_timeout': 10,
            },
            'CONN_MAX_AGE': 600,
            'CONN_HEALTH_CHECKS': True,
        }
    }
    print("✅ Using PostgreSQL with manual configuration")

else:
    # SQLite Database Configuration (only if explicitly requested)
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR.parent / 'persistent_data' / 'db_data',
            'OPTIONS': {
                'timeout': 20,
                'check_same_thread': False,
            }
        }
    }

    # SQLite backup settings
    DBBACKUP_STORAGE_OPTIONS = {'location': BASE_DIR.parent / 'persistent_data' / 'db_backups'}

    # Ensure directories exist
    os.makedirs(BASE_DIR.parent / 'persistent_data', exist_ok=True)
    os.makedirs(BASE_DIR.parent / 'persistent_data' / 'db_backups', exist_ok=True)
    print("⚠️  Using SQLite (not recommended for production)")

# ===== FIXED STATIC FILES CONFIGURATION ONLY =====
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR.parent, 'staticfiles')

# Static files directories - where Django will look for static files
STATICFILES_DIRS = [
    os.path.join(BASE_DIR.parent, 'static'),
    os.path.join(BASE_DIR.parent, 'assets'),
]

# Ensure static directories exist
for static_dir in STATICFILES_DIRS:
    os.makedirs(static_dir, exist_ok=True)

# FIXED: Use basic storage to avoid Bootstrap source map errors
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Static files finders
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# WhiteNoise settings for better static file serving
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = False
WHITENOISE_MAX_AGE = 31536000  # 1 year cache
WHITENOISE_SKIP_COMPRESS_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'zip', 'gz', 'tgz', 'bz2', 'tbz', 'xz', 'br']

# More comprehensive MIME type configuration
WHITENOISE_MIMETYPES = {
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.svg': 'image/svg+xml',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.ico': 'image/x-icon',
    '.pdf': 'application/pdf',
    '.zip': 'application/zip',
}

# Ensure static directory exists
os.makedirs(STATIC_ROOT, exist_ok=True)

# Media files for Railway
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Ensure media directory exists
os.makedirs(MEDIA_ROOT, exist_ok=True)

# Database backup settings
DBBACKUP_STORAGE = 'django.core.files.storage.FileSystemStorage'
DBBACKUP_STORAGE_OPTIONS = {
    'location': os.path.join(BASE_DIR, 'backups'),
}

# Create backups directory if it doesn't exist
BACKUP_DIR = os.path.join(BASE_DIR, 'backups')
os.makedirs(BACKUP_DIR, exist_ok=True)

# Security settings for production
SECURE_SSL_REDIRECT = False  # Let Railway handle SSL
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Session configuration
SESSION_COOKIE_SECURE = False  # Set to True when you have proper SSL
CSRF_COOKIE_SECURE = False     # Set to True when you have proper SSL
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True

# Force HTTPS for cookies and sessions (when ready)
CSRF_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_SAMESITE = 'Lax'

# CSRF Trusted Origins for custom domain
CSRF_TRUSTED_ORIGINS = [
    'https://software.sultanatmarquee.com',
    'https://*.railway.app',
    'https://*.vercel.app',
    'https://localhost',
    'http://localhost',
    'http://127.0.0.1',
]

# Logging configuration for production debugging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'whitenoise': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Debug static files configuration (remove after fixing)
print("=== PRODUCTION SETTINGS DEBUG ===")
print(f"BASE_DIR: {BASE_DIR}")
print(f"STATIC_ROOT: {STATIC_ROOT}")
print(f"STATICFILES_DIRS: {STATICFILES_DIRS}")
print(f"USE_SQLITE: {USE_SQLITE}")
print(f"DATABASE_URL set: {'Yes' if os.environ.get('DATABASE_URL') else 'No'}")
for i, static_dir in enumerate(STATICFILES_DIRS):
    exists = os.path.exists(static_dir)
    print(f"STATICFILES_DIRS[{i}] exists: {exists} - {static_dir}")
    if exists:
        try:
            files = os.listdir(static_dir)[:5]  # Show first 5 files
            print(f"  Sample files: {files}")
        except:
            print(f"  Could not list files in {static_dir}")
print("=== END DEBUG ===")

# Note: Auto-import moved to apps.py to avoid "Apps aren't loaded yet" error