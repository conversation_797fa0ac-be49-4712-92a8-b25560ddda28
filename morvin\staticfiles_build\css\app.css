/*
Template Name: <PERSON><PERSON> -  <PERSON><PERSON> & Dashboard Template
Author: Themesdesign
Version: 1.0.0
Contact: <EMAIL>
File: Main Css File
*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap");
#page-topbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1001;
  background-color: #ffffff; }

.navbar-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  -webkit-box-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 auto;
  height: 70px;
  padding: 0 calc(24px / 2) 0 0;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }
  .navbar-header .dropdown.show .header-item {
    background-color: #f9fafc; }

.navbar-brand-box {
  padding: 0 1.5rem;
  text-align: center;
  width: 260px; }

.logo {
  line-height: 70px; }
  .logo .logo-sm {
    display: none; }

.logo-dark {
  display: block; }

.logo-light {
  display: none; }

.fullscreen-enable [data-toggle="fullscreen"] .mdi-fullscreen::before {
  content: '\F0294'; }

.page-content-wrapper {
  margin-top: -90px; }

.page-title-box {
  background: url(../images/title-img.png);
  background-position: center;
  background-color: #525ce5;
  margin: 0 -24px 23px -24px;
  padding: 24px 24px 92px 24px;
  color: #fff;
  background-size: cover; }

/* Search */
.search-wrap {
  background-color: white;
  color: #343a40;
  z-index: 9997;
  position: absolute;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  right: 0;
  height: 70px;
  padding: 0 15px;
  -webkit-transform: translate3d(0, -100%, 0);
          transform: translate3d(0, -100%, 0);
  -webkit-transition: .3s;
  transition: .3s; }
  .search-wrap form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%; }
  .search-wrap .search-bar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    width: 100%; }
  .search-wrap .search-input {
    -webkit-box-flex: 1;
        -ms-flex: 1 1;
            flex: 1 1;
    border: none;
    outline: none;
    -webkit-box-shadow: none;
            box-shadow: none;
    background-color: transparent; }
  .search-wrap .close-search {
    width: 36px;
    height: 64px;
    line-height: 64px;
    text-align: center;
    color: inherit;
    font-size: 24px; }
    .search-wrap .close-search:hover {
      color: #f14e4e; }

.search-wrap.open {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0); }

.megamenu-list li {
  position: relative;
  padding: 5px 0px; }
  .megamenu-list li a {
    color: #495057; }

@media (max-width: 992px) {
  #page-topbar {
    left: 0; }
  .navbar-brand-box {
    width: auto; }
  .logo span.logo-lg {
    display: none; }
  .logo span.logo-sm {
    display: inline-block; } }

.page-content {
  padding: calc(70px) calc(24px / 2) 60px calc(24px / 2); }

.header-item {
  height: 70px;
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  color: #636e75;
  border: 0;
  border-radius: 0px; }
  .header-item:hover {
    color: #636e75; }

.header-profile-user {
  height: 36px;
  width: 36px;
  background-color: #eaedf1;
  padding: 3px; }

.noti-icon i {
  font-size: 24px;
  color: #636e75; }

.noti-icon .badge {
  position: absolute;
  top: 20px;
  right: 6px; }

.notification-item .media {
  padding: 0.75rem 1rem; }
  .notification-item .media:hover {
    background-color: #eaedf1; }

.dropdown-icon-item {
  display: block;
  border-radius: 3px;
  line-height: 34px;
  text-align: center;
  padding: 15px 0 9px;
  display: block;
  border: 1px solid transparent;
  color: #74788d; }
  .dropdown-icon-item img {
    height: 24px; }
  .dropdown-icon-item span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
  .dropdown-icon-item:hover {
    border-color: #edf1f5; }

.fullscreen-enable [data-toggle="fullscreen"] .bx-fullscreen::before {
  content: "\ea3f"; }

body[data-topbar="dark"] #page-topbar {
  background-color: #1f293f; }

body[data-topbar="dark"] .navbar-header .dropdown.show .header-item {
  background-color: rgba(255, 255, 255, 0.05); }

body[data-topbar="dark"] .navbar-header .waves-effect .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

body[data-topbar="dark"] .header-item {
  color: #a3acc1; }
  body[data-topbar="dark"] .header-item:hover {
    color: #a3acc1; }

body[data-topbar="dark"] .header-profile-user {
  background-color: rgba(255, 255, 255, 0.25); }

body[data-topbar="dark"] .noti-icon i {
  color: #a3acc1; }

body[data-topbar="dark"] .title-tooltip li i {
  color: #a3acc1; }

body[data-topbar="dark"] .app-search .form-control {
  background-color: rgba(241, 245, 247, 0.07);
  color: #fff; }

body[data-topbar="dark"] .app-search span,
body[data-topbar="dark"] .app-search input.form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

body[data-sidebar="dark"] .navbar-brand-box {
  background: #1f293f; }

body[data-sidebar="dark"] .logo-dark {
  display: none; }

body[data-sidebar="dark"] .logo-light {
  display: block; }

@media (max-width: 600px) {
  .navbar-header .dropdown {
    position: static; }
    .navbar-header .dropdown .dropdown-menu {
      left: 10px !important;
      right: 10px !important; } }

@media (max-width: 380px) {
  .navbar-brand-box {
    display: none; } }

body[data-layout="horizontal"] .navbar-brand-box {
  width: auto; }

body[data-layout="horizontal"] .page-content {
  margin-top: 70px;
  padding: calc(36px + 24px) calc(24px / 2) 60px calc(24px / 2); }

@media (max-width: 992px) {
  body[data-layout="horizontal"] .page-content {
    margin-top: 10px; } }

.page-title-box .breadcrumb {
  background-color: transparent;
  padding: 0; }

.page-title-box h4 {
  color: #fff;
  text-transform: uppercase;
  font-weight: 500;
  font-size: 16px !important; }

.topbar-social-icon {
  padding: calc(38px / 2) 0; }

.title-tooltip li i {
  font-size: 20px;
  margin-left: 10px;
  color: #636e75; }

.footer {
  bottom: 0;
  padding: 20px calc(24px / 2);
  position: absolute;
  right: 0;
  border-top: 1px solid #edf1f5;
  color: #74788d;
  left: 260px;
  height: 60px;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  background-color: #fff; }

@media (max-width: 992px) {
  .footer {
    left: 0; } }

.vertical-collpsed .footer {
  left: 70px; }

body[data-layout="horizontal"] .footer {
  left: 0 !important; }

.right-bar {
  background-color: #fff;
  -webkit-box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
          box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  display: block;
  position: fixed;
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  width: 280px;
  z-index: 9999;
  float: right !important;
  right: -290px;
  top: 0;
  bottom: 0; }
  .right-bar .right-bar-toggle {
    background-color: #444c54;
    height: 24px;
    width: 24px;
    line-height: 24px;
    color: #edf1f5;
    text-align: center;
    border-radius: 50%; }
    .right-bar .right-bar-toggle:hover {
      background-color: #4b545c; }

.rightbar-overlay {
  background-color: rgba(52, 58, 64, 0.55);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: none;
  z-index: 9998;
  -webkit-transition: all .2s ease-out;
  transition: all .2s ease-out; }

.right-bar-enabled .right-bar {
  right: 0; }

.right-bar-enabled .rightbar-overlay {
  display: block; }

@media (max-width: 767.98px) {
  .right-bar {
    overflow: auto; }
    .right-bar .slimscroll-menu {
      height: auto !important; } }

.metismenu {
  margin: 0; }
  .metismenu li {
    display: block;
    width: 100%; }
  .metismenu .mm-collapse {
    display: none; }
    .metismenu .mm-collapse:not(.mm-show) {
      display: none; }
    .metismenu .mm-collapse.mm-show {
      display: block; }
  .metismenu .mm-collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-timing-function: ease;
            transition-timing-function: ease;
    -webkit-transition-duration: .35s;
            transition-duration: .35s;
    -webkit-transition-property: height, visibility;
    transition-property: height, visibility; }

.vertical-menu {
  width: 260px;
  z-index: 1001;
  background: #ffffff;
  bottom: 0;
  margin-top: 0;
  position: fixed;
  top: 70px;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }

.user-sidebar {
  position: relative;
  text-align: center;
  background: url(../images/user-img.png);
  background-color: #525ce5;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  padding: 20px 0; }
  .user-sidebar .user-img {
    position: relative; }
    .user-sidebar .user-img img {
      width: 60px;
      height: 60px;
      border: 3px solid #23c58f;
      padding: 5px; }
    .user-sidebar .user-img .avatar-online {
      position: absolute;
      bottom: 4px;
      width: 10px;
      height: 10px;
      z-index: 1;
      border: 2px solid transparent;
      border-radius: 50%;
      margin-left: -15px; }

.main-content {
  margin-left: 260px;
  overflow: hidden; }
  .main-content .content {
    padding: 0 15px 10px 15px;
    margin-top: 70px; }

#sidebar-menu {
  padding: 0px 0 30px 0; }
  #sidebar-menu .mm-active > .has-arrow:after {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg); }
  #sidebar-menu .has-arrow:after {
    content: "\F0142";
    font-family: 'Material Design Icons';
    display: block;
    float: right;
    -webkit-transition: -webkit-transform .2s;
    transition: -webkit-transform .2s;
    transition: transform .2s;
    transition: transform .2s, -webkit-transform .2s;
    font-size: 1rem; }
  #sidebar-menu ul li a {
    display: block;
    padding: .625rem 1.2rem;
    color: #27303f;
    position: relative;
    font-size: 14.5px;
    -webkit-transition: all .4s;
    transition: all .4s;
    margin: 0px 17px;
    border-radius: 3px; }
    #sidebar-menu ul li a i {
      display: inline-block;
      min-width: 1.75rem;
      padding-bottom: .125em;
      font-size: 16px;
      line-height: 1.40625rem;
      vertical-align: middle;
      color: #27303f;
      -webkit-transition: all .4s;
      transition: all .4s; }
    #sidebar-menu ul li a:hover {
      color: #525ce5; }
      #sidebar-menu ul li a:hover i {
        color: #525ce5; }
  #sidebar-menu ul li .badge {
    margin-top: 5px; }
  #sidebar-menu ul li ul.sub-menu {
    padding: 0; }
    #sidebar-menu ul li ul.sub-menu li a {
      padding: .4rem 1.5rem .4rem 2.8rem;
      font-size: 14px;
      color: #27303f;
      background-color: transparent !important; }
      #sidebar-menu ul li ul.sub-menu li a:before {
        content: "\F09DF";
        font-family: 'Material Design Icons';
        font-size: 20px;
        line-height: 10px;
        padding-right: 2px;
        vertical-align: middle;
        display: inline-block; }
    #sidebar-menu ul li ul.sub-menu li ul.sub-menu {
      padding: 0; }
      #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {
        padding: .4rem 1.5rem .4rem 4rem;
        font-size: 14px; }

.menu-title {
  padding: 12px 20px !important;
  letter-spacing: .05em;
  pointer-events: none;
  cursor: default;
  font-size: 11px;
  text-transform: uppercase;
  color: #27303f;
  font-weight: 600; }

.mm-active {
  color: #525ce5 !important; }
  .mm-active > a {
    color: #525ce5 !important;
    background-color: #f5f7fa !important; }
    .mm-active > a i {
      color: #525ce5 !important; }
  .mm-active > i {
    color: #525ce5 !important; }
  .mm-active .active {
    color: #525ce5 !important;
    background-color: #f5f7fa !important; }
    .mm-active .active i {
      color: #525ce5 !important; }

@media (max-width: 992px) {
  .vertical-menu {
    display: none; }
  .main-content {
    margin-left: 0 !important; }
  body.sidebar-enable .vertical-menu {
    display: block; } }

.vertical-collpsed .user-sidebar {
  display: none; }

.vertical-collpsed .main-content {
  margin-left: 70px; }

.vertical-collpsed .navbar-brand-box {
  width: 70px !important; }

.vertical-collpsed .logo span.logo-lg {
  display: none; }

.vertical-collpsed .logo span.logo-sm {
  display: block; }

.vertical-collpsed .vertical-menu {
  position: absolute;
  width: 70px !important;
  z-index: 5; }
  .vertical-collpsed .vertical-menu .simplebar-mask,
  .vertical-collpsed .vertical-menu .simplebar-content-wrapper {
    overflow: visible !important; }
  .vertical-collpsed .vertical-menu .simplebar-scrollbar {
    display: none !important; }
  .vertical-collpsed .vertical-menu .simplebar-offset {
    bottom: 0 !important; }
  .vertical-collpsed .vertical-menu #sidebar-menu .menu-title,
  .vertical-collpsed .vertical-menu #sidebar-menu .badge,
  .vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {
    display: none !important; }
  .vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {
    height: inherit !important; }
  .vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {
    display: none; }
  .vertical-collpsed .vertical-menu #sidebar-menu > ul > li {
    position: relative;
    white-space: nowrap; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {
      padding: 15px 20px;
      min-height: 55px;
      -webkit-transition: none;
      transition: none;
      margin: 0; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {
        color: #525ce5; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {
        font-size: 1.15rem;
        margin-left: 4px; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {
        display: none;
        padding-left: 25px; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {
      position: relative;
      width: calc(190px + 70px);
      background-color: #f5f7fa;
      -webkit-transition: none;
      transition: none; }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {
        display: inline; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {
      display: block;
      left: 70px;
      position: absolute;
      width: 190px;
      height: auto !important;
      -webkit-box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1);
              box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {
        -webkit-box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1);
                box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }
      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {
        -webkit-box-shadow: none;
                box-shadow: none;
        padding: 8px 20px;
        position: relative;
        width: 190px;
        z-index: 6;
        color: #27303f;
        margin: 0; }
        .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {
          color: #525ce5; }
  .vertical-collpsed .vertical-menu #sidebar-menu > ul ul {
    padding: 5px 0;
    z-index: 9999;
    display: none;
    background-color: #ffffff; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {
      display: block;
      left: 190px;
      height: auto !important;
      margin-top: -36px;
      position: absolute;
      width: 190px; }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {
      position: absolute;
      right: 20px;
      top: 12px;
      -webkit-transform: rotate(270deg);
              transform: rotate(270deg); }
    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {
      color: #f9fafc; }

body[data-sidebar="dark"] .user-sidebar {
  background: none; }

body[data-sidebar="dark"] .vertical-menu {
  background: #1f293f; }

body[data-sidebar="dark"] #sidebar-menu ul li a {
  color: #8590a5; }
  body[data-sidebar="dark"] #sidebar-menu ul li a i {
    color: #8590a5; }
  body[data-sidebar="dark"] #sidebar-menu ul li a:hover {
    color: #d7e4ec; }
    body[data-sidebar="dark"] #sidebar-menu ul li a:hover i {
      color: #d7e4ec; }

body[data-sidebar="dark"] #sidebar-menu ul li ul.sub-menu li a {
  color: #8590a5; }
  body[data-sidebar="dark"] #sidebar-menu ul li ul.sub-menu li a:hover {
    color: #d7e4ec; }

body[data-sidebar="dark"].vertical-collpsed {
  min-height: 1400px; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {
    background: #222d46;
    color: #d7e4ec; }
    body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {
      color: #d7e4ec; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {
    color: #8590a5; }
    body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {
      color: #525ce5; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {
    background-color: white; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {
    color: #d7e4ec !important; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {
    color: #525ce5 !important; }
  body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar="dark"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {
    color: #525ce5 !important; }

body[data-sidebar="dark"] .mm-active {
  color: #d7e4ec !important; }
  body[data-sidebar="dark"] .mm-active > a {
    color: #d7e4ec !important;
    background-color: #2b364e !important; }
    body[data-sidebar="dark"] .mm-active > a i {
      color: #d7e4ec !important; }
  body[data-sidebar="dark"] .mm-active > i {
    color: #d7e4ec !important; }
  body[data-sidebar="dark"] .mm-active .active {
    color: #d7e4ec !important;
    background-color: #2b364e !important; }
    body[data-sidebar="dark"] .mm-active .active i {
      color: #d7e4ec !important; }

body[data-sidebar="dark"] .menu-title {
  color: #8590a5; }

body[data-layout="horizontal"] .main-content {
  margin-left: 0 !important; }

body[data-sidebar-size="small"] .navbar-brand-box {
  width: 160px; }

body[data-sidebar-size="small"] .vertical-menu {
  width: 160px;
  text-align: center; }
  body[data-sidebar-size="small"] .vertical-menu .has-arrow:after,
  body[data-sidebar-size="small"] .vertical-menu .badge {
    display: none !important; }

body[data-sidebar-size="small"] .main-content {
  margin-left: 160px; }

body[data-sidebar-size="small"] .footer {
  left: 160px; }

body[data-sidebar-size="small"] #sidebar-menu ul li a i {
  display: block; }

body[data-sidebar-size="small"] #sidebar-menu ul li ul.sub-menu li a {
  padding-left: 1.5rem; }
  body[data-sidebar-size="small"] #sidebar-menu ul li ul.sub-menu li a:before {
    display: none; }

body[data-sidebar-size="small"] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {
  padding-left: 1.5rem; }

body[data-sidebar-size="small"].vertical-collpsed .main-content {
  margin-left: 70px; }

body[data-sidebar-size="small"].vertical-collpsed .vertical-menu #sidebar-menu {
  text-align: left; }
  body[data-sidebar-size="small"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {
    display: inline-block; }

body[data-sidebar-size="small"].vertical-collpsed .footer {
  left: 70px; }

[dir="rtl"]
#sidebar-menu .has-arrow:after {
  content: "\F0141";
  -webkit-transition: -webkit-transform .2s;
  transition: -webkit-transform .2s;
  transition: transform .2s;
  transition: transform .2s, -webkit-transform .2s; }

[dir="rtl"]
#sidebar-menu .mm-active > .has-arrow:after {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg); }

.topnav {
  background: #fff;
  padding: 0 calc(24px / 2);
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  margin-top: 70px;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 100; }
  .topnav .topnav-menu {
    margin: 0;
    padding: 0; }
  .topnav .navbar-nav .nav-link {
    font-size: 15px;
    position: relative;
    padding: 1.2rem 1.5rem;
    color: #27303f; }
    .topnav .navbar-nav .nav-link i {
      font-size: 15px;
      top: 2px;
      position: relative; }
    .topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {
      color: #525ce5;
      background-color: transparent; }
  .topnav .navbar-nav .dropdown-item {
    color: #27303f; }
    .topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {
      color: #525ce5;
      background: transparent; }
  .topnav .navbar-nav .nav-item .nav-link.active {
    color: #525ce5; }
  .topnav .navbar-nav .dropdown.active > a {
    color: #525ce5;
    background-color: transparent; }

@media (min-width: 1200px) {
  body[data-layout="horizontal"] .container-fluid,
  body[data-layout="horizontal"] .navbar-header {
    max-width: 85%; } }

@media (min-width: 992px) {
  .topnav .navbar-nav .nav-item:first-of-type .nav-link {
    padding-left: 0; }
  .topnav .dropdown-item {
    padding: .5rem 1.5rem;
    min-width: 180px; }
  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {
    left: 0px;
    right: auto; }
  .topnav .dropdown .dropdown-menu {
    margin-top: 0;
    border-radius: 0 0 0.25rem 0.25rem; }
    .topnav .dropdown .dropdown-menu .arrow-down::after {
      right: 15px;
      -webkit-transform: rotate(-135deg) translateY(-50%);
              transform: rotate(-135deg) translateY(-50%);
      position: absolute; }
    .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {
      position: absolute;
      top: 0 !important;
      left: 100%;
      display: none; }
  .topnav .dropdown:hover > .dropdown-menu {
    display: block; }
  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {
    display: block; }
  .navbar-toggle {
    display: none; } }

.arrow-down {
  display: inline-block; }
  .arrow-down:after {
    border-color: initial;
    border-style: solid;
    border-width: 0 0 1px 1px;
    content: "";
    height: .4em;
    display: inline-block;
    right: 5px;
    top: 50%;
    margin-left: 10px;
    -webkit-transform: rotate(-45deg) translateY(-50%);
            transform: rotate(-45deg) translateY(-50%);
    -webkit-transform-origin: top;
            transform-origin: top;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
    width: .4em; }

@media (max-width: 1199.98px) {
  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {
    right: 100%;
    left: auto; } }

@media (max-width: 991.98px) {
  .navbar-brand-box .logo-dark {
    display: block; }
    .navbar-brand-box .logo-dark span.logo-sm {
      display: block; }
  .navbar-brand-box .logo-light {
    display: none; }
  .topnav {
    max-height: 360px;
    overflow-y: auto;
    padding: 0; }
    .topnav .navbar-nav .nav-link {
      padding: 0.75rem 1.1rem; }
    .topnav .dropdown .dropdown-menu {
      background-color: transparent;
      border: none;
      -webkit-box-shadow: none;
              box-shadow: none;
      padding-left: 20px; }
      .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {
        width: auto; }
        .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {
          margin: 0px; }
    .topnav .dropdown .dropdown-item {
      position: relative;
      background-color: transparent; }
      .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {
        color: #525ce5; }
    .topnav .arrow-down::after {
      right: 15px;
      position: absolute; } }

@media (min-width: 992px) {
  body[data-layout="horizontal"][data-topbar="light"] .navbar-brand-box .logo-dark {
    display: block; }
  body[data-layout="horizontal"][data-topbar="light"] .navbar-brand-box .logo-light {
    display: none; }
  body[data-layout="horizontal"][data-topbar="light"] .topnav {
    background-color: #141b2d; }
    body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav .nav-link {
      color: rgba(255, 255, 255, 0.5); }
      body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav .nav-link:focus, body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav .nav-link:hover {
        color: rgba(255, 255, 255, 0.9); }
    body[data-layout="horizontal"][data-topbar="light"] .topnav .navbar-nav > .dropdown.active > a {
      color: rgba(255, 255, 255, 0.9) !important; } }

body[data-layout="horizontal"] .logo-dark {
  display: none; }

body[data-layout="horizontal"] .logo-light {
  display: block; }

body[data-topbar="colored"] #page-topbar {
  background-color: #778beb; }

body[data-topbar="colored"] .navbar-header .dropdown .show.header-item {
  background-color: rgba(255, 255, 255, 0.05); }

body[data-topbar="colored"] .navbar-header .waves-effect .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

body[data-topbar="colored"] .title-tooltip li i {
  color: rgba(255, 255, 255, 0.8); }

body[data-topbar="colored"] .header-item {
  color: rgba(255, 255, 255, 0.5); }
  body[data-topbar="colored"] .header-item:hover {
    color: #fff; }

body[data-topbar="colored"] .header-profile-user {
  background-color: rgba(255, 255, 255, 0.25); }

body[data-topbar="colored"] .noti-icon i {
  color: rgba(255, 255, 255, 0.5); }

body[data-topbar="colored"] .logo-dark {
  display: none; }

body[data-topbar="colored"] .logo-light {
  display: block; }

body[data-topbar="colored"] .app-search .form-control {
  background-color: rgba(241, 245, 247, 0.07);
  color: #fff; }

body[data-topbar="colored"] .app-search span,
body[data-topbar="colored"] .app-search input.form-control::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.5); }

body[data-layout-size="boxed"] {
  background-color: #f1f3f7; }
  body[data-layout-size="boxed"] #layout-wrapper {
    background-color: #f5f7fa;
    max-width: 1400px;
    margin: 0 auto;
    -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
            box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }
  body[data-layout-size="boxed"] #page-topbar {
    max-width: 1400px;
    margin: 0 auto; }
  body[data-layout-size="boxed"] .footer {
    margin: 0 auto;
    max-width: calc(1400px - 260px); }
  body[data-layout-size="boxed"].vertical-collpsed .footer {
    max-width: calc(1400px - 70px); }

body[data-layout="horizontal"][data-layout-size="boxed"] #page-topbar, body[data-layout="horizontal"][data-layout-size="boxed"] #layout-wrapper, body[data-layout="horizontal"][data-layout-size="boxed"] .footer {
  max-width: 100%; }

body[data-layout="horizontal"][data-layout-size="boxed"] .container-fluid, body[data-layout="horizontal"][data-layout-size="boxed"] .navbar-header {
  max-width: 1400px; }

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent; }

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  -webkit-transition: all 0.5s ease-out;
  transition: all 0.5s ease-out;
  -webkit-transition-property: -webkit-transform, opacity;
  -webkit-transition-property: opacity, -webkit-transform;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0) translate(0, 0);
  transform: scale(0) translate(0, 0);
  pointer-events: none; }

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2); }

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4); }

.waves-notransition {
  -webkit-transition: none !important;
  transition: none !important; }

.waves-button,
.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1; }

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em; }

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em; }

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom; }

.waves-input-wrapper.waves-button {
  padding: 0; }

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1; }

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%; }

.waves-float {
  -webkit-mask-image: none;
  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  -webkit-transition: all 300ms;
  transition: all 300ms; }

.waves-float:active {
  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }

.waves-block {
  display: block; }

.waves-effect.waves-light .waves-ripple {
  background-color: rgba(255, 255, 255, 0.4); }

.waves-effect.waves-primary .waves-ripple {
  background-color: rgba(82, 92, 229, 0.4); }

.waves-effect.waves-success .waves-ripple {
  background-color: rgba(35, 197, 143, 0.4); }

.waves-effect.waves-info .waves-ripple {
  background-color: rgba(91, 164, 229, 0.4); }

.waves-effect.waves-warning .waves-ripple {
  background-color: rgba(238, 177, 72, 0.4); }

.waves-effect.waves-danger .waves-ripple {
  background-color: rgba(241, 78, 78, 0.4); }

.avatar-xs {
  height: 2rem;
  width: 2rem; }

.avatar-sm {
  height: 2.5rem;
  width: 2.5rem; }

.avatar-md {
  height: 4.5rem;
  width: 4.5rem; }

.avatar-lg {
  height: 6rem;
  width: 6rem; }

.avatar-xl {
  height: 7.5rem;
  width: 7.5rem; }

.mini-stat-icon {
  width: 46px;
  height: 46px; }

.avatar-title {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100%; }

.custom-accordion .card + .card {
  margin-top: 0.5rem; }

.custom-accordion a.collapsed i.accor-plus-icon:before {
  content: "\F0415"; }

.custom-accordion .card-header {
  border-radius: 7px; }

.custom-accordion-arrow .card {
  border: 1px solid #edf1f5;
  -webkit-box-shadow: none;
          box-shadow: none; }

.custom-accordion-arrow .card-header {
  padding-left: 45px;
  position: relative; }
  .custom-accordion-arrow .card-header .accor-arrow-icon {
    position: absolute;
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
    background-color: #525ce5;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    left: 10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%); }

.custom-accordion-arrow a.collapsed i.accor-arrow-icon:before {
  content: "\F0142"; }

.font-size-10 {
  font-size: 10px !important; }

.font-size-11 {
  font-size: 11px !important; }

.font-size-12 {
  font-size: 12px !important; }

.font-size-13 {
  font-size: 13px !important; }

.font-size-14 {
  font-size: 14px !important; }

.font-size-15 {
  font-size: 15px !important; }

.font-size-16 {
  font-size: 16px !important; }

.font-size-17 {
  font-size: 17px !important; }

.font-size-18 {
  font-size: 18px !important; }

.font-size-20 {
  font-size: 20px !important; }

.font-size-22 {
  font-size: 22px !important; }

.font-size-24 {
  font-size: 24px !important; }

.media {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start; }

.media-body {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1; }

.social-list-item {
  height: 2rem;
  width: 2rem;
  line-height: calc(2rem - 2px);
  display: block;
  border: 1px solid #adb5bd;
  border-radius: 50%;
  color: #adb5bd;
  text-align: center;
  -webkit-transition: all 0.4s;
  transition: all 0.4s; }
  .social-list-item:hover {
    color: #74788d;
    background-color: #edf1f5; }

.w-xs {
  min-width: 80px; }

.w-sm {
  min-width: 95px; }

.w-md {
  min-width: 110px; }

.w-lg {
  min-width: 140px; }

.w-xl {
  min-width: 160px; }

.bg-overlay {
  position: absolute;
  height: 100%;
  width: 100%;
  right: 0;
  bottom: 0;
  left: 0;
  top: 0;
  opacity: 0.7;
  background-color: #000; }

.flex-1 {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1; }

.alert-dismissible .btn-close {
  font-size: 10px;
  padding: 1.05rem 1.25rem;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat; }

#preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 9999; }

#status {
  width: 40px;
  height: 40px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -20px 0 0 -20px; }

.spinner-chase {
  margin: 0 auto;
  width: 40px;
  height: 40px;
  position: relative;
  -webkit-animation: spinner-chase 2.5s infinite linear both;
          animation: spinner-chase 2.5s infinite linear both; }

.chase-dot {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-animation: chase-dot 2.0s infinite ease-in-out both;
          animation: chase-dot 2.0s infinite ease-in-out both; }
  .chase-dot:before {
    content: '';
    display: block;
    width: 25%;
    height: 25%;
    background-color: #525ce5;
    border-radius: 100%;
    -webkit-animation: chase-dot-before 2.0s infinite ease-in-out both;
            animation: chase-dot-before 2.0s infinite ease-in-out both; }
  .chase-dot:nth-child(1) {
    -webkit-animation-delay: -1.1s;
            animation-delay: -1.1s; }
    .chase-dot:nth-child(1):before {
      -webkit-animation-delay: -1.1s;
              animation-delay: -1.1s; }
  .chase-dot:nth-child(2) {
    -webkit-animation-delay: -1.0s;
            animation-delay: -1.0s; }
    .chase-dot:nth-child(2):before {
      -webkit-animation-delay: -1.0s;
              animation-delay: -1.0s; }
  .chase-dot:nth-child(3) {
    -webkit-animation-delay: -0.9s;
            animation-delay: -0.9s; }
    .chase-dot:nth-child(3):before {
      -webkit-animation-delay: -0.9s;
              animation-delay: -0.9s; }
  .chase-dot:nth-child(4) {
    -webkit-animation-delay: -0.8s;
            animation-delay: -0.8s; }
    .chase-dot:nth-child(4):before {
      -webkit-animation-delay: -0.8s;
              animation-delay: -0.8s; }
  .chase-dot:nth-child(5) {
    -webkit-animation-delay: -0.7s;
            animation-delay: -0.7s; }
    .chase-dot:nth-child(5):before {
      -webkit-animation-delay: -0.7s;
              animation-delay: -0.7s; }
  .chase-dot:nth-child(6) {
    -webkit-animation-delay: -0.6s;
            animation-delay: -0.6s; }
    .chase-dot:nth-child(6):before {
      -webkit-animation-delay: -0.6s;
              animation-delay: -0.6s; }

@-webkit-keyframes spinner-chase {
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

@keyframes spinner-chase {
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

@-webkit-keyframes chase-dot {
  80%, 100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

@keyframes chase-dot {
  80%, 100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

@-webkit-keyframes chase-dot-before {
  50% {
    -webkit-transform: scale(0.4);
            transform: scale(0.4); }
  100%, 0% {
    -webkit-transform: scale(1);
            transform: scale(1); } }

@keyframes chase-dot-before {
  50% {
    -webkit-transform: scale(0.4);
            transform: scale(0.4); }
  100%, 0% {
    -webkit-transform: scale(1);
            transform: scale(1); } }

.form-check-right {
  padding-left: 0;
  display: inline-block;
  padding-right: 1.5em; }
  .form-check-right .form-check-input {
    float: right;
    margin-left: 0;
    margin-right: -1.5em; }
  .form-check-right .form-check-label {
    display: block; }

.form-check {
  position: relative;
  text-align: left; }

.form-check-label {
  cursor: pointer;
  margin-bottom: 0; }

.dash-summary {
  border-top: 1px solid #eaedf1; }

.dash-main-border {
  border-bottom: 1px solid #eaedf1; }

.dash-info-widget {
  background: #f9fafc; }

.dash-goal {
  border-left: 1px solid #eaedf1; }

@media (max-width: 768px) {
  .dash-goal {
    border-left: none; } }

.carousel-indicators {
  bottom: -20px; }
  .carousel-indicators button {
    background-color: #525ce5 !important;
    width: 10px !important;
    height: 10px !important;
    border-radius: 50% !important;
    margin: 5px;
    opacity: 0.5; }

.mini-stats-wid .mini-stat-icon {
  overflow: hidden;
  position: relative; }
  .mini-stats-wid .mini-stat-icon:before, .mini-stats-wid .mini-stat-icon:after {
    content: "";
    position: absolute;
    width: 8px;
    height: 54px;
    background-color: rgba(255, 255, 255, 0.1);
    left: 16px;
    -webkit-transform: rotate(32deg);
            transform: rotate(32deg);
    top: -5px;
    -webkit-transition: all 0.4s;
    transition: all 0.4s; }
  .mini-stats-wid .mini-stat-icon::after {
    left: -12px;
    width: 12px;
    -webkit-transition: all 0.2s;
    transition: all 0.2s; }

.mini-stats-wid:hover .mini-stat-icon::after {
  left: 60px; }

.inbox-wid .inbox-list-item a {
  color: #495057;
  display: block;
  padding: 11px 0px;
  border-bottom: 1px solid #edf1f5; }

.inbox-wid .inbox-list-item:first-child a {
  padding-top: 0px; }

.inbox-wid .inbox-list-item:last-child a {
  border-bottom: 0px; }

.activity-border:before {
  content: "";
  position: absolute;
  height: 38px;
  border-left: 2px dashed #eaedf1;
  top: 40px;
  left: 0px; }

.activity-wid {
  margin-left: 16px; }
  .activity-wid .activity-list {
    position: relative;
    padding: 0 0 33px 30px; }
    .activity-wid .activity-list .activity-icon {
      position: absolute;
      left: -20px;
      top: 0px;
      z-index: 2; }
    .activity-wid .activity-list:last-child {
      padding-bottom: 0px; }

.button-items {
  margin-left: -8px;
  margin-bottom: -12px; }
  .button-items .btn {
    margin-bottom: 12px;
    margin-left: 8px; }

.mfp-popup-form {
  max-width: 1140px; }

.bs-example-modal {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  z-index: 1;
  display: block; }

.icon-demo-content {
  text-align: center;
  color: #adb5bd; }
  .icon-demo-content i {
    display: block;
    font-size: 24px;
    color: #74788d;
    width: 48px;
    height: 48px;
    line-height: 46px;
    margin: 0px auto;
    margin-bottom: 16px;
    border-radius: 4px;
    border: 1px solid #edf1f5;
    -webkit-transition: all 0.4s;
    transition: all 0.4s; }
  .icon-demo-content .col-lg-4 {
    margin-top: 24px; }
    .icon-demo-content .col-lg-4:hover i {
      background-color: #525ce5;
      color: #fff; }

.grid-structure .grid-container {
  background-color: #f9fafc;
  margin-top: 10px;
  font-size: .8rem;
  font-weight: 500;
  padding: 10px 20px; }

.card-radio {
  background-color: #fff;
  border: 2px solid #eaedf1;
  border-radius: 0.25rem;
  padding: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }
  .card-radio:hover {
    cursor: pointer; }

.card-radio-label {
  display: block; }

.card-radio-input {
  display: none; }
  .card-radio-input:checked + .card-radio {
    border-color: #525ce5 !important; }

.navs-carousel .owl-nav {
  margin-top: 16px; }
  .navs-carousel .owl-nav button {
    width: 30px;
    height: 30px;
    line-height: 28px !important;
    font-size: 20px !important;
    border-radius: 50% !important;
    background-color: rgba(82, 92, 229, 0.25) !important;
    color: #525ce5 !important;
    margin: 4px 8px !important; }

@media print {
  .vertical-menu,
  .right-bar,
  .page-title-box,
  .navbar-header,
  .footer {
    display: none !important; }
  .card-body,
  .main-content,
  .right-bar,
  .page-content,
  body {
    padding: 0;
    margin: 0; }
  .card {
    border: 0; } }

[data-simplebar] {
  position: relative;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -ms-flex-line-pack: start;
      align-content: flex-start;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start; }

.simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit; }

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0; }

.simplebar-offset {
  direction: inherit !important;
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0 !important;
  bottom: 0;
  right: 0 !important;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch; }

.simplebar-content-wrapper {
  direction: inherit;
  -webkit-box-sizing: border-box !important;
          box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%;
  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  visibility: visible;
  overflow: auto;
  /* Scroll on this element otherwise element can't have a padding applied properly */
  max-width: 100%;
  /* Not required for horizontal scroll to trigger */
  max-height: 100%;
  /* Needed for vertical scroll to trigger */
  scrollbar-width: none;
  padding: 0px !important; }

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none; }

.simplebar-content:before,
.simplebar-content:after {
  content: ' ';
  display: table; }

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none; }

.simplebar-height-auto-observer-wrapper {
  -webkit-box-sizing: inherit !important;
          box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  -webkit-box-flex: inherit;
      -ms-flex-positive: inherit;
          flex-grow: inherit;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  -ms-flex-preferred-size: 0;
      flex-basis: 0; }

.simplebar-height-auto-observer {
  -webkit-box-sizing: inherit;
          box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1; }

.simplebar-track {
  z-index: 1;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden; }

[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -moz-user-select: none;
   -ms-user-select: none;
       user-select: none;
  -webkit-user-select: none; }

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all; }

.simplebar-scrollbar {
  position: absolute;
  right: 2px;
  width: 6px;
  min-height: 10px; }

.simplebar-scrollbar:before {
  position: absolute;
  content: '';
  background: #a2adb7;
  border-radius: 7px;
  left: 0;
  right: 0;
  opacity: 0;
  -webkit-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear; }

.simplebar-scrollbar.simplebar-visible:before {
  /* When hovered, remove all transitions from drag handle */
  opacity: 0.5;
  -webkit-transition: opacity 0s linear;
  transition: opacity 0s linear; }

.simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px; }

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px; }

.simplebar-track.simplebar-horizontal {
  left: 0;
  height: 11px; }

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  height: 100%;
  left: 2px;
  right: 2px; }

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  right: auto;
  left: 0;
  top: 2px;
  height: 7px;
  min-height: 0;
  min-width: 10px;
  width: auto; }

/* Rtl support */
[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {
  right: auto;
  left: 0; }

.hs-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll; }

.simplebar-hide-scrollbar {
  position: fixed;
  left: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none; }

.custom-scroll {
  height: 100%; }

.fc-toolbar h2 {
  font-size: 16px;
  line-height: 30px;
  text-transform: uppercase; }

.fc th.fc-widget-header {
  background: #f9fafc;
  font-size: 13px;
  line-height: 20px;
  padding: 10px 0;
  text-transform: uppercase;
  font-weight: 600; }

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
  border-color: #f9fafc; }

.fc-unthemed td.fc-today {
  background: #fafbfc; }

.fc-button {
  background: #fff;
  border-color: #edf1f5;
  color: #495057;
  text-transform: capitalize;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 6px 12px !important;
  height: auto !important; }

.fc-state-down,
.fc-state-active,
.fc-state-disabled {
  background-color: #525ce5;
  color: #fff;
  text-shadow: none; }

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 0.8125rem;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center; }

#external-events .external-event {
  text-align: left !important;
  padding: 8px 16px; }

.fc-event, .fc-event-dot {
  background-color: #525ce5; }

.fc-event .fc-content {
  color: #fff; }

.fc .table-bordered td, .fc .table-bordered th {
  border-color: #edf1f5; }

@media (max-width: 575.98px) {
  .fc .fc-toolbar {
    display: block; } }

.fc .fc-toolbar h2 {
  font-size: 16px;
  line-height: 30px;
  text-transform: uppercase; }

@media (max-width: 767.98px) {
  .fc .fc-toolbar .fc-left,
  .fc .fc-toolbar .fc-right,
  .fc .fc-toolbar .fc-center {
    float: none;
    display: block;
    text-align: center;
    clear: both;
    margin: 10px 0; }
  .fc .fc-toolbar > * > * {
    float: none; }
  .fc .fc-toolbar .fc-today-button {
    display: none; } }

.fc .fc-toolbar .btn {
  text-transform: capitalize; }

.fc-bootstrap .fc-today.alert-info {
  background-color: #eaedf1; }

.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {
  background-color: #000 !important; }

[dir="rtl"] .fc-header-toolbar {
  direction: ltr !important; }

[dir="rtl"] .fc-toolbar > * > :not(:first-child) {
  margin-left: .75em; }

.sp-container {
  background-color: #fff;
  z-index: 1000; }
  .sp-container button {
    padding: .25rem .5rem;
    font-size: .71094rem;
    border-radius: .2rem;
    font-weight: 400;
    color: #343a40; }
    .sp-container button.sp-palette-toggle {
      background-color: #f9fafc; }
    .sp-container button.sp-choose {
      background-color: #23c58f;
      margin-left: 5px;
      margin-right: 0; }

.sp-palette-container {
  border-right: 1px solid #edf1f5; }

.sp-input {
  background-color: #fff;
  border-color: #ced4da !important;
  color: #495057; }
  .sp-input:focus {
    outline: none; }

[dir="rtl"] .sp-alpha {
  direction: rtl; }

[dir="rtl"] .sp-original-input-container .sp-add-on {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important; }

[dir="rtl"] input.spectrum.with-add-on {
  border: 1px solid #ced4da;
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem; }

#session-timeout-dialog .close {
  display: none; }

#session-timeout-dialog .countdown-holder {
  color: #f14e4e;
  font-weight: 500; }

#session-timeout-dialog .btn-default {
  background-color: #fff;
  color: #f14e4e;
  -webkit-box-shadow: none;
          box-shadow: none; }

.irs {
  font-family: var(--bs-font-sans-serif); }

.irs--round .irs-bar,
.irs--round .irs-to,
.irs--round .irs-from,
.irs--round .irs-single {
  background: #525ce5 !important;
  font-size: 11px; }

.irs--round .irs-to:before,
.irs--round .irs-from:before,
.irs--round .irs-single:before {
  display: none; }

.irs--round .irs-line {
  background: #eaedf1;
  border-color: #eaedf1; }

.irs--round .irs-grid-text {
  font-size: 11px;
  color: #adb5bd; }

.irs--round .irs-min,
.irs--round .irs-max {
  color: #adb5bd;
  background: #eaedf1;
  font-size: 11px; }

.irs--round .irs-handle {
  border: 2px solid #525ce5;
  width: 10px;
  height: 16px;
  top: 29px;
  background-color: #fff !important; }

.swal2-container .swal2-title {
  font-size: 24px;
  font-weight: 500; }

.swal2-content {
  font-size: 16px; }

.swal2-icon.swal2-question {
  border-color: #5ba4e5;
  color: #5ba4e5; }

.swal2-icon.swal2-success [class^=swal2-success-line] {
  background-color: #23c58f; }

.swal2-icon.swal2-success .swal2-success-ring {
  border-color: rgba(35, 197, 143, 0.3); }

.swal2-icon.swal2-warning {
  border-color: #eeb148;
  color: #eeb148; }

.swal2-styled:focus {
  -webkit-box-shadow: none;
          box-shadow: none; }

.swal2-progress-steps .swal2-progress-step {
  background: #525ce5; }
  .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
    background: #525ce5; }
    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {
      background: rgba(82, 92, 229, 0.3); }

.swal2-progress-steps .swal2-progress-step-line {
  background: #525ce5; }

.swal2-loader {
  border-color: #525ce5 transparent #525ce5 transparent; }

.symbol {
  border-color: #fff; }

.rating-symbol-background, .rating-symbol-foreground {
  font-size: 24px; }

.rating-symbol-foreground {
  top: 0px; }

.rating-star > span {
  display: inline-block;
  vertical-align: middle; }
  .rating-star > span.badge {
    margin-left: 4px; }

.error {
  color: #f14e4e; }

.parsley-error {
  border-color: #f14e4e; }

.parsley-errors-list {
  display: none;
  margin: 0;
  padding: 0; }
  .parsley-errors-list.filled {
    display: block; }
  .parsley-errors-list > li {
    font-size: 12px;
    list-style: none;
    color: #f14e4e;
    margin-top: 5px; }

.select2-container {
  display: block; }
  .select2-container .select2-selection--single {
    background-color: #fff;
    border: 1px solid #ced4da;
    height: 38px; }
    .select2-container .select2-selection--single:focus {
      outline: none; }
    .select2-container .select2-selection--single .select2-selection__rendered {
      line-height: 36px;
      padding-left: 12px;
      color: #495057; }
    .select2-container .select2-selection--single .select2-selection__arrow {
      height: 34px;
      width: 34px;
      right: 3px; }
      .select2-container .select2-selection--single .select2-selection__arrow b {
        border-color: #adb5bd transparent transparent transparent;
        border-width: 6px 6px 0 6px; }
    .select2-container .select2-selection--single .select2-selection__placeholder {
      color: #495057; }

.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #adb5bd transparent !important;
  border-width: 0 6px 6px 6px !important; }

.select2-container--default .select2-search--dropdown {
  padding: 10px;
  background-color: #fff; }
  .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da;
    background-color: #fff;
    color: #74788d;
    outline: none; }

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #525ce5; }

.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: #f9fafc;
  color: #16181b; }
  .select2-container--default .select2-results__option[aria-selected=true]:hover {
    background-color: #525ce5;
    color: #fff; }

.select2-results__option {
  padding: 6px 12px; }

.select2-dropdown {
  border: 1px solid rgba(0, 0, 0, 0.15);
  background-color: #fff;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }

.select2-search input {
  border: 1px solid #eaedf1; }

.select2-container .select2-selection--multiple {
  min-height: 38px;
  background-color: #fff;
  border: 1px solid #ced4da !important; }
  .select2-container .select2-selection--multiple .select2-selection__rendered {
    padding: 2px 10px; }
  .select2-container .select2-selection--multiple .select2-search__field {
    border: 0;
    color: #495057; }
    .select2-container .select2-selection--multiple .select2-search__field::-webkit-input-placeholder {
      color: #495057; }
    .select2-container .select2-selection--multiple .select2-search__field::-moz-placeholder {
      color: #495057; }
    .select2-container .select2-selection--multiple .select2-search__field:-ms-input-placeholder {
      color: #495057; }
    .select2-container .select2-selection--multiple .select2-search__field::-ms-input-placeholder {
      color: #495057; }
    .select2-container .select2-selection--multiple .select2-search__field::placeholder {
      color: #495057; }
  .select2-container .select2-selection--multiple .select2-selection__choice {
    background-color: #edf1f5;
    border: 1px solid #eaedf1;
    border-radius: 1px;
    padding: 0 7px; }

.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #ced4da; }

.select2-container--default .select2-results__group {
  font-weight: 600; }

.select2-result-repository__avatar {
  float: left;
  width: 60px;
  margin-right: 10px; }
  .select2-result-repository__avatar img {
    width: 100%;
    height: auto;
    border-radius: 2px; }

.select2-result-repository__statistics {
  margin-top: 7px; }

.select2-result-repository__forks,
.select2-result-repository__stargazers,
.select2-result-repository__watchers {
  display: inline-block;
  font-size: 11px;
  margin-right: 1em;
  color: #adb5bd; }
  .select2-result-repository__forks .fa,
  .select2-result-repository__stargazers .fa,
  .select2-result-repository__watchers .fa {
    margin-right: 4px; }
    .select2-result-repository__forks .fa.fa-flash::before,
    .select2-result-repository__stargazers .fa.fa-flash::before,
    .select2-result-repository__watchers .fa.fa-flash::before {
      content: "\f0e7";
      font-family: 'Font Awesome 5 Free'; }

.select2-results__option--highlighted .select2-result-repository__forks,
.select2-results__option--highlighted .select2-result-repository__stargazers,
.select2-results__option--highlighted .select2-result-repository__watchers {
  color: rgba(255, 255, 255, 0.8); }

.select2-result-repository__meta {
  overflow: hidden; }

.img-flag {
  margin-right: 7px;
  height: 15px;
  width: 18px; }

/* CSS Switch */
input[switch] {
  display: none; }
  input[switch] + label {
    font-size: 1em;
    line-height: 1;
    width: 56px;
    height: 24px;
    background-color: #ced4da;
    background-image: none;
    border-radius: 2rem;
    padding: 0.16667rem;
    cursor: pointer;
    display: inline-block;
    text-align: center;
    position: relative;
    font-weight: 500;
    -webkit-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out; }
    input[switch] + label:before {
      color: #343a40;
      content: attr(data-off-label);
      display: block;
      font-family: inherit;
      font-weight: 500;
      font-size: 12px;
      line-height: 21px;
      position: absolute;
      right: 1px;
      margin: 3px;
      top: -2px;
      text-align: center;
      min-width: 1.66667rem;
      overflow: hidden;
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out; }
    input[switch] + label:after {
      content: '';
      position: absolute;
      left: 3px;
      background-color: #edf1f5;
      -webkit-box-shadow: none;
              box-shadow: none;
      border-radius: 2rem;
      height: 20px;
      width: 20px;
      top: 2px;
      -webkit-transition: all 0.1s ease-in-out;
      transition: all 0.1s ease-in-out; }
  input[switch]:checked + label {
    background-color: #525ce5; }

input[switch]:checked + label {
  background-color: #525ce5; }
  input[switch]:checked + label:before {
    color: #fff;
    content: attr(data-on-label);
    right: auto;
    left: 3px; }
  input[switch]:checked + label:after {
    left: 33px;
    background-color: #edf1f5; }

input[switch="bool"] + label {
  background-color: #f14e4e; }

input[switch="bool"] + label:before, input[switch="bool"]:checked + label:before,
input[switch="default"]:checked + label:before {
  color: #fff; }

input[switch="bool"]:checked + label {
  background-color: #23c58f; }

input[switch="default"]:checked + label {
  background-color: #a2a2a2; }

input[switch="primary"]:checked + label {
  background-color: #525ce5; }

input[switch="success"]:checked + label {
  background-color: #23c58f; }

input[switch="info"]:checked + label {
  background-color: #5ba4e5; }

input[switch="warning"]:checked + label {
  background-color: #eeb148; }

input[switch="danger"]:checked + label {
  background-color: #f14e4e; }

input[switch="dark"]:checked + label {
  background-color: #343a40; }

.square-switch {
  margin-right: 7px; }
  .square-switch input[switch] + label, .square-switch input[switch] + label:after {
    border-radius: 4px; }

.datepicker {
  border: 1px solid #f9fafc;
  padding: 8px;
  z-index: 999 !important; }
  .datepicker table tr th {
    font-weight: 500; }
  .datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {
    background-color: #525ce5 !important;
    background-image: none;
    -webkit-box-shadow: none;
            box-shadow: none;
    color: #fff !important; }
  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,
  .datepicker table tr td span.focused,
  .datepicker table tr td span:hover {
    background: #edf1f5; }
  .datepicker table tr td.new, .datepicker table tr td.old,
  .datepicker table tr td span.new,
  .datepicker table tr td span.old {
    color: #adb5bd;
    opacity: 0.6; }
  .datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {
    background-color: #eaedf1; }

.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {
  padding: 7px; }

.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }

.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }

.bootstrap-touchspin .input-group-btn-vertical {
  right: 0 !important;
  left: 100% !important; }

.bootstrap-touchspin .bootstrap-touchspin-up {
  border-top-right-radius: 4px !important;
  border-bottom-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important; }

.bootstrap-touchspin .bootstrap-touchspin-down {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 4px !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important; }

.table-bordered {
  border: 1px solid #edf1f5; }

div.dataTables_wrapper div.dataTables_filter {
  text-align: right; }
  div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0.5em;
    margin-right: 0; }

.tox-tinymce {
  border: 2px solid #eaedf1 !important; }

.tox .tox-statusbar {
  border-top: 1px solid #eaedf1 !important; }

.tox .tox-menubar,
.tox .tox-edit-area__iframe,
.tox .tox-statusbar {
  background-color: #fff !important;
  background: none !important; }

.tox .tox-mbtn {
  color: #495057 !important; }
  .tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {
    background-color: #eaedf1 !important; }

.tox .tox-tbtn:hover {
  background-color: #eaedf1 !important; }

.tox .tox-toolbar__primary {
  border-color: #eaedf1 !important; }

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
  background: #eaedf1 !important; }

.tox .tox-tbtn {
  color: #495057 !important; }
  .tox .tox-tbtn svg {
    fill: #495057 !important; }

.tox .tox-edit-area__iframe {
  background-color: #fff !important; }

.tox .tox-statusbar a,
.tox .tox-statusbar__path-item,
.tox .tox-statusbar__wordcount {
  color: #495057 !important; }

.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {
  border-right: 1px solid #dbe0e7 !important; }

.tox-tinymce-aux {
  z-index: 1000 !important; }

/* Dropzone */
.dropzone {
  min-height: 230px;
  border: 2px dashed #ced4da;
  background: #fff;
  border-radius: 6px; }
  .dropzone .dz-message {
    font-size: 24px;
    width: 100%; }

.twitter-bs-wizard .twitter-bs-wizard-nav {
  position: relative; }
  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {
    content: "";
    width: 189px;
    height: 2px;
    background: rgba(82, 92, 229, 0.2);
    position: absolute;
    top: 26px;
    margin-left: 100px; }
  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {
    display: inline-block;
    border-radius: 30px;
    padding: 4px 0px;
    width: 200px;
    line-height: 34px;
    color: #525ce5;
    text-align: center;
    position: relative;
    background-color: rgba(82, 92, 229, 0.2); }
    @media (max-width: 991.98px) {
      .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {
        display: block;
        margin: 0 auto 8px !important;
        width: 170px; } }
  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {
    display: block;
    margin-top: 8px;
    font-weight: 600; }
    @media (max-width: 575.98px) {
      .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {
        display: none; } }
  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {
    background-color: transparent;
    color: #495057; }
    .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {
      background-color: #525ce5;
      color: #fff; }

.twitter-bs-wizard .twitter-bs-wizard-pager-link {
  padding-top: 24px;
  padding-left: 0;
  list-style: none;
  margin-bottom: 0; }
  .twitter-bs-wizard .twitter-bs-wizard-pager-link li {
    display: inline-block; }
    .twitter-bs-wizard .twitter-bs-wizard-pager-link li a {
      display: inline-block;
      padding: .47rem .75rem;
      background-color: #525ce5;
      color: #fff;
      border-radius: .25rem; }
    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {
      cursor: not-allowed;
      background-color: #757dea; }
    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {
      float: right; }

.twitter-bs-wizard-tab-content {
  padding-top: 24px;
  min-height: 262px; }

@media (max-width: 1024px) {
  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {
    background: transparent !important; } }

.table-rep-plugin .btn-toolbar {
  display: block; }

.table-rep-plugin .table-responsive {
  border: none !important; }

.table-rep-plugin .btn-group .btn-default {
  background-color: #74788d;
  color: #f9fafc;
  border: 1px solid #74788d; }
  .table-rep-plugin .btn-group .btn-default.btn-primary {
    background-color: #525ce5;
    border-color: #525ce5;
    color: #fff;
    -webkit-box-shadow: 0 0 0 2px rgba(82, 92, 229, 0.5);
            box-shadow: 0 0 0 2px rgba(82, 92, 229, 0.5); }

.table-rep-plugin .btn-group.pull-right {
  float: right; }
  .table-rep-plugin .btn-group.pull-right .dropdown-menu {
    right: 0;
    -webkit-transform: none !important;
            transform: none !important;
    top: 100% !important; }

.table-rep-plugin tbody th {
  font-size: 14px;
  font-weight: normal; }

.table-rep-plugin .checkbox-row {
  padding-left: 40px;
  color: #495057 !important; }
  .table-rep-plugin .checkbox-row:hover {
    background-color: #f4f6f9 !important; }
  .table-rep-plugin .checkbox-row label {
    display: inline-block;
    padding-left: 5px;
    position: relative; }
    .table-rep-plugin .checkbox-row label::before {
      -o-transition: 0.3s ease-in-out;
      -webkit-transition: 0.3s ease-in-out;
      background-color: #fff;
      border-radius: 3px;
      border: 1px solid #eaedf1;
      content: "";
      display: inline-block;
      height: 17px;
      left: 0;
      margin-left: -20px;
      position: absolute;
      transition: 0.3s ease-in-out;
      width: 17px;
      outline: none !important; }
    .table-rep-plugin .checkbox-row label::after {
      color: #edf1f5;
      display: inline-block;
      font-size: 11px;
      height: 16px;
      left: 0;
      margin-left: -20px;
      padding-left: 3px;
      padding-top: 1px;
      position: absolute;
      top: -1px;
      width: 16px; }
  .table-rep-plugin .checkbox-row input[type="checkbox"] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: none !important; }
    .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label {
      opacity: 0.65; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:focus + label::before {
    outline-offset: -2px;
    outline: none; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    content: "\f00c";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:disabled + label::before {
    background-color: #f9fafc;
    cursor: not-allowed; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::before {
    background-color: #525ce5;
    border-color: #525ce5; }
  .table-rep-plugin .checkbox-row input[type="checkbox"]:checked + label::after {
    color: #fff; }

.table-rep-plugin .fixed-solution .sticky-table-header {
  top: 70px !important;
  background-color: #525ce5; }
  .table-rep-plugin .fixed-solution .sticky-table-header table {
    color: #fff; }

.table-rep-plugin table.focus-on tbody tr.focused th,
.table-rep-plugin table.focus-on tbody tr.focused td,
.table-rep-plugin .sticky-table-header {
  background: #525ce5;
  border-color: #525ce5;
  color: #fff; }
  .table-rep-plugin table.focus-on tbody tr.focused th table,
  .table-rep-plugin table.focus-on tbody tr.focused td table,
  .table-rep-plugin .sticky-table-header table {
    color: #fff; }

@media (min-width: 992px) {
  body[data-layout="horizontal"] .fixed-solution .sticky-table-header {
    top: 148px !important; } }

.table-striped > tbody > tr:nth-of-type(odd).focused {
  -webkit-box-shadow: none !important;
          box-shadow: none !important; }
  .table-striped > tbody > tr:nth-of-type(odd).focused td, .table-striped > tbody > tr:nth-of-type(odd).focused th {
    -webkit-box-shadow: none !important;
            box-shadow: none !important; }

.table-edits input, .table-edits select {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  border: 1px solid #ced4da;
  background-color: #fff;
  color: #495057;
  border-radius: 0.25rem; }
  .table-edits input:focus, .table-edits select:focus {
    outline: none;
    border-color: #b1bbc4; }

.apex-charts {
  min-height: 10px !important; }
  .apex-charts text {
    font-family: var(--bs-font-sans-serif) !important;
    fill: #adb5bd; }
  .apex-charts .apexcharts-canvas {
    margin: 0 auto; }

.apexcharts-tooltip-title,
.apexcharts-tooltip-text {
  font-family: var(--bs-font-sans-serif) !important; }

.apexcharts-legend-series {
  font-weight: 500; }

.apexcharts-gridline {
  pointer-events: none;
  stroke: #f8f9fa; }

.apexcharts-legend-text {
  color: #74788d !important;
  font-family: var(--bs-font-sans-serif) !important;
  font-size: 13px !important; }

.apexcharts-pie-label {
  fill: #fff !important; }

.apexcharts-yaxis text,
.apexcharts-xaxis text {
  font-family: var(--bs-font-sans-serif) !important;
  fill: #adb5bd; }

.ct-golden-section:before {
  float: none; }

.ct-chart {
  max-height: 300px; }
  .ct-chart .ct-label {
    fill: #adb5bd;
    color: #adb5bd;
    font-size: 12px;
    line-height: 1; }

.ct-chart.simple-pie-chart-chartist .ct-label {
  color: #fff;
  fill: #fff;
  font-size: 16px; }

.ct-grid {
  stroke: rgba(52, 58, 64, 0.1); }

.ct-chart .ct-series.ct-series-a .ct-bar,
.ct-chart .ct-series.ct-series-a .ct-line,
.ct-chart .ct-series.ct-series-a .ct-point,
.ct-chart .ct-series.ct-series-a .ct-slice-donut {
  stroke: #525ce5; }

.ct-chart .ct-series.ct-series-b .ct-bar,
.ct-chart .ct-series.ct-series-b .ct-line,
.ct-chart .ct-series.ct-series-b .ct-point,
.ct-chart .ct-series.ct-series-b .ct-slice-donut {
  stroke: #23c58f; }

.ct-chart .ct-series.ct-series-c .ct-bar,
.ct-chart .ct-series.ct-series-c .ct-line,
.ct-chart .ct-series.ct-series-c .ct-point,
.ct-chart .ct-series.ct-series-c .ct-slice-donut {
  stroke: #f14e4e; }

.ct-chart .ct-series.ct-series-d .ct-bar,
.ct-chart .ct-series.ct-series-d .ct-line,
.ct-chart .ct-series.ct-series-d .ct-point,
.ct-chart .ct-series.ct-series-d .ct-slice-donut {
  stroke: #5ba4e5; }

.ct-chart .ct-series.ct-series-e .ct-bar,
.ct-chart .ct-series.ct-series-e .ct-line,
.ct-chart .ct-series.ct-series-e .ct-point,
.ct-chart .ct-series.ct-series-e .ct-slice-donut {
  stroke: #23c58f; }

.ct-chart .ct-series.ct-series-f .ct-bar,
.ct-chart .ct-series.ct-series-f .ct-line,
.ct-chart .ct-series.ct-series-f .ct-point,
.ct-chart .ct-series.ct-series-f .ct-slice-donut {
  stroke: #343a40; }

.ct-chart .ct-series.ct-series-g .ct-bar,
.ct-chart .ct-series.ct-series-g .ct-line,
.ct-chart .ct-series.ct-series-g .ct-point,
.ct-chart .ct-series.ct-series-g .ct-slice-donut {
  stroke: #6f42c1; }

.ct-series-a .ct-area,
.ct-series-a .ct-slice-pie {
  fill: #525ce5; }

.ct-series-b .ct-area,
.ct-series-b .ct-slice-pie {
  fill: #23c58f; }

.ct-series-c .ct-area,
.ct-series-c .ct-slice-pie {
  fill: #eeb148; }

.ct-series-d .ct-area,
.ct-series-d .ct-slice-pie {
  fill: #23c58f; }

.ct-area {
  fill-opacity: .33; }

.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 10px;
  padding: 2px 10px;
  border-radius: 3px;
  background: #343a40;
  color: #eaedf1;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity .2s linear;
  transition: opacity .2s linear; }
  .chartist-tooltip.tooltip-show {
    opacity: 1; }

.ct-line {
  stroke-width: 3px; }

.ct-point {
  stroke-width: 7px; }

/* Flot chart */
.flotTip {
  padding: 8px 12px !important;
  background-color: #343a40 !important;
  border: 1px solid #343a40 !important;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  z-index: 100;
  color: #edf1f5;
  opacity: 1;
  border-radius: 3px !important;
  font-size: 14px !important; }

.legend div {
  background-color: transparent !important; }

.legend tr {
  height: 30px; }

.legendLabel {
  padding-left: 5px;
  line-height: 10px;
  padding-right: 10px;
  font-size: 13px;
  font-weight: 500;
  color: #adb5bd; }

.legendColorBox div {
  border-radius: 3px; }
  .legendColorBox div div {
    border-radius: 3px; }

.float-lable-box table {
  margin: 0 auto; }

@media (max-width: 575.98px) {
  .legendLabel {
    display: none; } }

.jqstooltip {
  -webkit-box-sizing: content-box;
          box-sizing: content-box;
  width: auto !important;
  height: auto !important;
  background-color: #343a40 !important;
  -webkit-box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
          box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  padding: 5px 10px !important;
  border-radius: 3px;
  border-color: #212529 !important; }

.jqsfield {
  color: #edf1f5 !important;
  font-size: 12px !important;
  line-height: 18px !important;
  font-family: var(--bs-font-sans-serif) !important;
  font-weight: 500 !important; }

.gmaps, .gmaps-panaroma {
  height: 300px;
  background: #f9fafc;
  border-radius: 3px; }

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  background: #525ce5;
  border-radius: 4px;
  padding: 10px 20px; }

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute; }
  .gmaps-overlay_arrow.above {
    bottom: -15px;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-top: 16px solid #525ce5; }
  .gmaps-overlay_arrow.below {
    top: -15px;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-bottom: 16px solid #525ce5; }

.jvectormap-label {
  border: none;
  background: #343a40;
  color: #f9fafc;
  font-family: var(--bs-font-sans-serif);
  font-size: 0.875rem;
  padding: 5px 8px; }

.editable-input .form-control {
  display: inline-block; }

.editable-buttons {
  margin-left: 7px; }
  .editable-buttons .editable-cancel {
    margin-left: 7px; }

.home-btn {
  position: absolute;
  top: 15px;
  right: 25px; }

.home-center {
  display: table;
  width: 100%;
  height: 100%; }

.home-desc-center {
  display: table-cell;
  vertical-align: middle; }

.authentication-bg {
  background-image: url(../images/title-img.png);
  height: 100vh;
  background-size: cover;
  background-position: center; }

.authentication-bg .bg-overlay {
  background-color: #525ce5; }

.error-page {
  text-transform: uppercase;
  background: repeating-linear-gradient(45deg, #525ce5, #525ce5 10px, #23c58f 10px, #23c58f 20px);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 120px;
  line-height: .8;
  position: relative; }

.faq-icon i {
  width: 30px;
  height: 30px;
  line-height: 28px;
  border: 1px solid;
  border-radius: 50%;
  text-align: center;
  float: right;
  font-size: 16px;
  display: inline-block; }

.faq-icon:after {
  content: "";
  position: absolute;
  width: 30px;
  height: 30px;
  opacity: 0.2;
  right: 50px;
  margin-top: -10px;
  border-radius: 50%;
  background: #525ce5; }

.search-box .form-control {
  border-radius: 30px;
  padding-left: 40px;
  border: 1px solid #eaedf1; }

.search-box .search-icon {
  font-size: 16px;
  position: absolute;
  left: 13px;
  top: 0;
  line-height: 38px; }

.categories-group-list {
  display: block;
  color: #343a40;
  font-weight: 500;
  padding: 8px 16px; }
  .categories-group-list[aria-expanded="true"] {
    background-color: #eaedf1; }
  .categories-group-list:last-child {
    border: 0; }
  .categories-group-list:hover {
    color: #343a40; }

.categories-list {
  padding: 8px 0px; }
  .categories-list li a {
    display: block;
    padding: 4px 16px;
    color: #495057; }
  .categories-list li.active a {
    color: #525ce5; }

.product-detai-imgs .nav .nav-link {
  margin: 7px 0px; }
  .product-detai-imgs .nav .nav-link.active {
    background-color: #eaedf1; }

.product-color a {
  display: inline-block;
  text-align: center;
  color: #495057; }
  .product-color a .product-color-item {
    margin: 7px;
    border: 2px solid #edf1f5;
    border-radius: 4px; }
  .product-color a.active, .product-color a:hover {
    color: #525ce5; }
    .product-color a.active .product-color-item, .product-color a:hover .product-color-item {
      border-color: #525ce5 !important; }

.product-track {
  border: 1px solid #edf1f5; }

.ecommerce-sortby-list li {
  color: #343a40; }
  .ecommerce-sortby-list li a {
    color: #495057;
    padding: 4px; }
  .ecommerce-sortby-list li.active a {
    color: #525ce5; }

.product-img {
  position: relative; }
  .product-img .product-ribbon {
    position: absolute;
    top: 0;
    left: 0px;
    padding: 6px 8px;
    border-radius: 50% 50% 25% 75%/44% 68% 32% 56%;
    width: 62px;
    height: 60px;
    color: #fff;
    font-size: 15px;
    text-align: center; }
  .product-img .product-like {
    position: absolute;
    top: 0;
    right: 0; }
    .product-img .product-like a {
      display: inline-block;
      width: 40px;
      height: 40px;
      border: 2px solid #eaedf1;
      line-height: 38px;
      border-radius: 50%;
      text-align: center;
      color: #adb5bd; }

.product-detail .nav-pills .nav-link {
  margin-bottom: 7px; }
  .product-detail .nav-pills .nav-link.active {
    background-color: #eaedf1; }
  .product-detail .nav-pills .nav-link .tab-img {
    width: 5rem; }

.product-detail .product-img {
  border: 1px solid #edf1f5;
  padding: 24px; }

.product-desc-list li {
  padding: 4px 0px; }

.product-review-link .list-inline-item a {
  color: #74788d; }

.product-review-link .list-inline-item:not(:last-child) {
  margin-right: 14px; }

.product-cart-touchspin {
  border: 1px solid #ced4da;
  background-color: #fff;
  border-radius: 0.25rem; }
  .product-cart-touchspin .form-control {
    border-color: transparent;
    height: 32px; }
  .product-cart-touchspin .input-group-btn .btn {
    background-color: transparent !important;
    border-color: transparent !important;
    color: #525ce5 !important;
    font-size: 16px;
    padding: 3px 12px;
    -webkit-box-shadow: none;
            box-shadow: none; }

.shipping-address {
  -webkit-box-shadow: none;
          box-shadow: none; }
  .shipping-address.active {
    border-color: #525ce5 !important; }

.twitter-bs-wizard .chackout-border:before {
  content: "";
  width: 139px;
  height: 2px;
  background: rgba(82, 92, 229, 0.2);
  position: absolute;
  top: 26px;
  margin-left: 100px; }

.twitter-bs-wizard .add-product-border:before {
  content: "";
  width: 324px;
  height: 2px;
  background: rgba(82, 92, 229, 0.2);
  position: absolute;
  top: 26px;
  margin-left: 100px; }

@media (max-width: 1024px) {
  .twitter-bs-wizard .chackout-border, .twitter-bs-wizard .add-product-border {
    width: 180px; }
    .twitter-bs-wizard .chackout-border:before, .twitter-bs-wizard .add-product-border:before {
      background: transparent !important; } }

/* ==============
  Email
===================*/
.email-leftbar {
  width: 236px;
  float: left;
  padding: 20px;
  border-radius: 5px; }

.email-rightbar {
  margin-left: 260px; }

.chat-user-box p.user-title {
  color: #343a40;
  font-weight: 600; }

.chat-user-box p {
  font-size: 12px; }

@media (max-width: 767px) {
  .email-leftbar {
    float: none;
    width: 100%; }
  .email-rightbar {
    margin: 0; } }

.mail-list a {
  display: block;
  color: #74788d;
  line-height: 24px;
  padding: 8px 5px; }
  .mail-list a.active {
    color: #f14e4e;
    font-weight: 500; }

.message-list {
  display: block;
  padding-left: 0; }
  .message-list li {
    position: relative;
    display: block;
    height: 50px;
    line-height: 50px;
    cursor: default;
    -webkit-transition-duration: .3s;
            transition-duration: .3s; }
    .message-list li a {
      color: #74788d; }
    .message-list li:hover {
      background: #eaedf1;
      -webkit-transition-duration: .05s;
              transition-duration: .05s; }
    .message-list li .col-mail {
      float: left;
      position: relative; }
    .message-list li .col-mail-1 {
      width: 320px; }
      .message-list li .col-mail-1 .star-toggle,
      .message-list li .col-mail-1 .checkbox-wrapper-mail,
      .message-list li .col-mail-1 .dot {
        display: block;
        float: left; }
      .message-list li .col-mail-1 .dot {
        border: 4px solid transparent;
        border-radius: 100px;
        margin: 22px 26px 0;
        height: 0;
        width: 0;
        line-height: 0;
        font-size: 0; }
      .message-list li .col-mail-1 .checkbox-wrapper-mail {
        margin: 15px 10px 0 20px; }
      .message-list li .col-mail-1 .star-toggle {
        margin-top: 18px;
        margin-left: 5px; }
      .message-list li .col-mail-1 .title {
        position: absolute;
        top: 0;
        left: 110px;
        right: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-bottom: 0; }
    .message-list li .col-mail-2 {
      position: absolute;
      top: 0;
      left: 320px;
      right: 0;
      bottom: 0; }
      .message-list li .col-mail-2 .subject,
      .message-list li .col-mail-2 .date {
        position: absolute;
        top: 0; }
      .message-list li .col-mail-2 .subject {
        left: 0;
        right: 200px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap; }
      .message-list li .col-mail-2 .date {
        right: 0;
        width: 170px;
        padding-left: 80px; }
    .message-list li.active, .message-list li.active:hover {
      -webkit-box-shadow: inset 3px 0 0 #525ce5;
              box-shadow: inset 3px 0 0 #525ce5; }
    .message-list li.unread {
      background-color: #eaedf1;
      font-weight: 500;
      color: #292d32; }
      .message-list li.unread a {
        color: #292d32;
        font-weight: 500; }
  .message-list .checkbox-wrapper-mail {
    cursor: pointer;
    height: 20px;
    width: 20px;
    position: relative;
    display: inline-block;
    -webkit-box-shadow: inset 0 0 0 1px #ced4da;
            box-shadow: inset 0 0 0 1px #ced4da;
    border-radius: 1px; }
    .message-list .checkbox-wrapper-mail input {
      opacity: 0;
      cursor: pointer; }
    .message-list .checkbox-wrapper-mail input:checked ~ label {
      opacity: 1; }
    .message-list .checkbox-wrapper-mail label {
      position: absolute;
      height: 20px;
      width: 20px;
      left: 0;
      cursor: pointer;
      opacity: 0;
      margin-bottom: 0;
      -webkit-transition-duration: .05s;
              transition-duration: .05s;
      top: 0; }
      .message-list .checkbox-wrapper-mail label:before {
        content: "\F012C";
        font-family: "Material Design Icons";
        top: 0;
        height: 20px;
        color: #292d32;
        width: 20px;
        position: absolute;
        margin-top: -16px;
        left: 4px;
        font-size: 13px; }

@media (max-width: 575.98px) {
  .message-list li .col-mail-1 {
    width: 200px; } }

@media (min-width: 992px) {
  .chat-leftsidebar {
    min-width: 380px; } }

.chat-leftsidebar .chat-leftsidebar-nav .nav {
  background-color: #fff; }

.chat-noti-dropdown.active:before {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #f14e4e;
  border-radius: 50%;
  right: 0; }

.chat-noti-dropdown .btn {
  padding: 6px;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-size: 20px; }

.chat-list {
  margin: 0; }
  .chat-list li.active a {
    background-color: #fff;
    -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
            box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }
  .chat-list li a {
    display: block;
    padding: 14px 16px;
    color: #74788d;
    -webkit-transition: all 0.4s;
    transition: all 0.4s;
    border: 1px solid #edf1f5;
    border-radius: 4px;
    margin-top: 10px; }
    .chat-list li a:hover {
      background-color: #fff;
      -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
              box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }

.user-chat-nav .dropdown .nav-btn {
  height: 40px;
  width: 40px;
  line-height: 34px;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
  font-size: 16px;
  background-color: #f9fafc;
  border-radius: 50%; }

.user-chat-nav .dropdown .dropdown-menu {
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  border: 1px solid #edf1f5; }

.chat-conversation li {
  clear: both; }

.chat-conversation .chat-day-title {
  position: relative;
  text-align: center;
  margin-bottom: 24px; }
  .chat-conversation .chat-day-title .title {
    background-color: #fff;
    position: relative;
    z-index: 1;
    padding: 6px 24px; }
  .chat-conversation .chat-day-title:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 1px;
    left: 0;
    right: 0;
    background-color: #edf1f5;
    top: 10px; }
  .chat-conversation .chat-day-title .badge {
    font-size: 12px; }

.chat-conversation .conversation-list {
  margin-bottom: 24px;
  display: inline-block;
  position: relative; }
  .chat-conversation .conversation-list .arrow-left {
    position: relative; }
    .chat-conversation .conversation-list .arrow-left:before {
      content: "";
      position: absolute;
      top: 10px;
      right: 100%;
      border: 7px solid transparent;
      border-right: 7px solid rgba(82, 92, 229, 0.1); }
  .chat-conversation .conversation-list .ctext-wrap {
    padding: 12px 24px;
    background-color: rgba(82, 92, 229, 0.1);
    border-radius: 8px 8px 8px 0px;
    overflow: hidden; }
    .chat-conversation .conversation-list .ctext-wrap .conversation-name {
      font-weight: 500;
      color: #525ce5;
      margin-bottom: 4px;
      position: relative; }
  .chat-conversation .conversation-list .dropdown {
    float: right; }
    .chat-conversation .conversation-list .dropdown .dropdown-toggle {
      font-size: 18px;
      padding: 4px;
      color: #74788d; }
      @media (max-width: 575.98px) {
        .chat-conversation .conversation-list .dropdown .dropdown-toggle {
          display: none; } }
    .chat-conversation .conversation-list .dropdown .dropdown-menu {
      -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
              box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
      border: 1px solid #edf1f5; }
  .chat-conversation .conversation-list .chat-time {
    font-size: 12px; }

.chat-conversation .right .conversation-list {
  float: right; }
  .chat-conversation .right .conversation-list .arrow-right {
    position: relative; }
    .chat-conversation .right .conversation-list .arrow-right:before {
      content: "";
      position: absolute;
      top: 10px;
      left: 100%;
      border: 7px solid transparent;
      border-left: 7px solid #f9fafc; }
  .chat-conversation .right .conversation-list .ctext-wrap {
    background-color: #f9fafc;
    text-align: right;
    border-radius: 8px 8px 0px 8px; }
  .chat-conversation .right .conversation-list .dropdown {
    float: left; }
  .chat-conversation .right .conversation-list.last-chat .conversation-list:before {
    right: 0;
    left: auto; }

.chat-input-section {
  border-top: 1px solid #edf1f5; }

.chat-input {
  border-radius: 30px;
  background-color: #f9fafc !important;
  border-color: #f9fafc !important;
  padding-right: 120px; }

.chat-input-links {
  position: absolute;
  right: 16px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%); }
  .chat-input-links li a {
    font-size: 16px;
    line-height: 36px;
    padding: 0px 4px;
    display: inline-block; }

@media (max-width: 575.98px) {
  .chat-send {
    min-width: auto; } }

.search-box .search-icon {
  font-size: 16px;
  position: absolute;
  left: 13px;
  top: 2px;
  font-size: 15px;
  line-height: 34px; }

.search-box .form-control {
  padding-left: 40px;
  border-radius: 5px; }

.counter-number {
  font-size: 32px;
  text-align: center; }
  .counter-number span {
    font-size: 16px;
    display: block;
    padding-top: 7px; }

.coming-box {
  float: left;
  width: 21%;
  padding: 14px 7px;
  margin: 0px 12px 24px 12px;
  background-color: #fff;
  border-radius: 5px;
  border-radius: 0.25rem;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }

@media (max-width: 991.98px) {
  .coming-box {
    width: 40%; } }

/************** vertical timeline **************/
.timeline {
  position: relative;
  width: 100%;
  padding: 30px 0; }

.timeline .timeline-end,
.timeline .timeline-start,
.timeline .timeline-year {
  position: relative;
  width: 100%;
  text-align: center;
  z-index: 1; }

.timeline .timeline-end p,
.timeline .timeline-start p,
.timeline .timeline-year p {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin: 0;
  padding: 30px 0;
  text-align: center;
  background: url(../images/user-img.png);
  background-color: #525ce5;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 100px;
  color: #fff;
  text-transform: uppercase; }

.timeline .timeline-year {
  margin: 30px 0; }

.timeline .timeline-continue {
  position: relative;
  width: 100%;
  padding: 60px 0; }
  .timeline .timeline-continue:after {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    top: 0;
    left: 50%;
    margin-left: -1px;
    background: #525ce5; }

.timeline .timeline-date {
  margin: 40px 10px 0 10px; }

.timeline .row.timeline-left,
.timeline .row.timeline-right .timeline-date {
  text-align: right; }

.timeline .row.timeline-right,
.timeline .row.timeline-left .timeline-date {
  text-align: left; }

.timeline .timeline-date::after {
  content: "";
  display: block;
  position: absolute;
  width: 14px;
  height: 14px;
  top: 45px;
  background: #525ce5;
  border-radius: 15px;
  z-index: 1; }

.timeline .row.timeline-left .timeline-date::after {
  left: -7px; }

.timeline .row.timeline-right .timeline-date::after {
  right: -7px; }

.timeline .timeline-box,
.timeline .timeline-launch {
  position: relative;
  display: inline-block;
  margin: 15px;
  padding: 20px;
  border: 1px solid #edf1f5;
  border-radius: 6px; }

.timeline .timeline-launch {
  width: 100%;
  margin: 15px 0;
  padding: 0;
  border: none;
  text-align: center;
  background: transparent; }

.timeline .timeline-box::after,
.timeline .timeline-box::before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid; }

.timeline .row.timeline-left .timeline-box::after,
.timeline .row.timeline-left .timeline-box::before {
  left: 100%; }

.timeline .row.timeline-right .timeline-box::after,
.timeline .row.timeline-right .timeline-box::before {
  right: 100%; }

.timeline .timeline-launch .timeline-box::after,
.timeline .timeline-launch .timeline-box::before {
  left: 50%;
  margin-left: -10px; }

.timeline .timeline-box::after {
  top: 26px;
  border-color: transparent transparent transparent #f9fafc;
  border-width: 10px; }

.timeline .timeline-box::before {
  top: 25px;
  border-color: transparent transparent transparent #edf1f5;
  border-width: 11px; }

.timeline .row.timeline-right .timeline-box::after {
  border-color: transparent #f9fafc transparent transparent; }

.timeline .row.timeline-right .timeline-box::before {
  border-color: transparent #edf1f5 transparent transparent; }

.timeline .timeline-launch .timeline-box::after {
  top: -20px;
  border-color: transparent transparent #edf1f5 transparent; }

.timeline .timeline-launch .timeline-box::before {
  top: -19px;
  border-color: transparent transparent #f9fafc transparent;
  border-width: 10px;
  z-index: 1; }

.timeline .timeline-launch .timeline-text {
  width: 100%; }

@media (max-width: 767px) {
  .timeline .timeline-continue::after {
    left: 40px; }
  .timeline .timeline-end,
  .timeline .timeline-start,
  .timeline .timeline-year,
  .timeline .row.timeline-left,
  .timeline .row.timeline-right .timeline-date,
  .timeline .row.timeline-right,
  .timeline .row.timeline-left .timeline-date,
  .timeline .timeline-launch {
    text-align: left; }
  .timeline .row.timeline-left .timeline-date::after,
  .timeline .row.timeline-right .timeline-date::after {
    left: 47px; }
  .timeline .timeline-box,
  .timeline .row.timeline-right .timeline-date,
  .timeline .row.timeline-left .timeline-date {
    margin-left: 55px; }
  .timeline .timeline-launch .timeline-box {
    margin-left: 0; }
  .timeline .row.timeline-left .timeline-box::after {
    left: -20px;
    border-color: transparent #f9fafc transparent transparent; }
  .timeline .row.timeline-left .timeline-box::before {
    left: -22px;
    border-color: transparent #edf1f5 transparent transparent; }
  .timeline .timeline-launch .timeline-box::after,
  .timeline .timeline-launch .timeline-box::before {
    left: 30px;
    margin-left: 0; } }

.pricing-nav-tabs {
  display: inline-block;
  background-color: #fff;
  -webkit-box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
          box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);
  padding: 4px;
  border-radius: 7px; }
  .pricing-nav-tabs li {
    display: inline-block; }

.pricing-box .plan-features li {
  padding: 7px 0px; }

/*********************
    Faqs
**********************/
.faq-nav-tabs .nav-item {
  margin: 0px 8px; }

.faq-nav-tabs .nav-link {
  text-align: center;
  margin-bottom: 8px;
  border: 2px solid #edf1f5;
  color: #495057; }
  .faq-nav-tabs .nav-link .nav-icon {
    font-size: 40px;
    margin-bottom: 8px;
    display: block; }
  .faq-nav-tabs .nav-link.active {
    border-color: #525ce5;
    background-color: transparent;
    color: #495057; }
    .faq-nav-tabs .nav-link.active .nav-icon {
      color: #525ce5; }

.text-error {
  font-size: 120px; }
  @media (max-width: 575.98px) {
    .text-error {
      font-size: 86px; } }

.error-text {
  color: #f14e4e;
  position: relative; }
  .error-text .error-img {
    position: absolute;
    width: 120px;
    left: -15px;
    right: 0;
    bottom: 47px; }
    @media (max-width: 575.98px) {
      .error-text .error-img {
        width: 86px;
        left: -12px;
        bottom: 38px; } }
rror-img {
        width: 86px;
        right: -12px;
        bottom: 38px; } }
