from django.urls import path
from django.views.generic import TemplateView
from items import views

urlpatterns = [
    # Ecommerce
    path('calculate_menu', views.Calculate.as_view(), name='calculate_menu'),
    path('custom-menu', views.custom_menu, name='custom-menu'),
    path('deals-calculator/<int:deal_id>/', views.Pre_Deals.as_view(), name='deals-calculator'),
    path('create_menu/', views.create_menu.as_view(), name='create_menu'),
    
    # Deal Management
    path('deals/', views.DealsList.as_view(), name='deals_list'),
    path('deals/edit/<int:deal_id>/', views.EditDeal.as_view(), name='edit_deal'),
    path('delete-deal/', views.delete_deal, name='delete_deal'),
    
    # CSV Import
    path('import-products-csv/', views.import_products_csv, name='import_products_csv'),
    path('download-product-template/', views.download_product_csv_template, name='download_product_csv_template'),
]
