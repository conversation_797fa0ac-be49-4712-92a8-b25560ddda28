# Generated by Django 4.2.7 on 2025-06-26 14:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('items', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bill_no', models.IntegerField(null=True, unique=True)),
                ('sr', models.IntegerField(null=True)),
                ('status', models.CharField(choices=[('Confirm', 'Confirm'), ('Tentative ', 'Tentative')], default='Tentative ', max_length=10)),
                ('event_timing', models.CharField(choices=[('Day', 'Day'), ('Night ', 'Night')], default='Night ', max_length=10)),
                ('booking_date', models.DateField(auto_now_add=True)),
                ('event_date', models.DateField()),
                ('no_of_people', models.IntegerField(null=True)),
                ('location', models.CharField(choices=[('Hall1', 'Hall 1'), ('Hall2', 'Hall 2'), ('Business_Center', 'Business Center'), ('Lawn', 'Lawn')], default='Hall1', max_length=20)),
                ('setup', models.CharField(choices=[('Normal', 'Normal'), ('Delux', 'Delux'), ('VIP', 'VIP')], default='Delux', max_length=10)),
                ('deals', models.CharField(max_length=200)),
                ('customer_name', models.CharField(max_length=200)),
                ('stage_charges', models.IntegerField(default=0, null=True)),
                ('entry_charges', models.IntegerField(default=0, null=True)),
                ('hall_charges', models.FloatField(default=0, null=True)),
                ('gents', models.IntegerField(default=0)),
                ('ladies', models.IntegerField(default=0)),
                ('customer_number', models.CharField(blank=True, max_length=14, null=True)),
                ('per_head', models.IntegerField(null=True)),
                ('extra_charges', models.IntegerField()),
                ('food_menu', models.CharField(max_length=200)),
                ('detials', models.TextField()),
                ('total_menu', models.IntegerField(default=0)),
                ('discount_amount', models.IntegerField(blank=True, default=0, null=True)),
                ('payment_details', models.TextField()),
                ('payment_count', models.IntegerField(default=0, editable=False)),
                ('total_amount', models.IntegerField(editable=False, null=True)),
                ('recieved_amount', models.IntegerField(null=True)),
                ('remaining_amount', models.IntegerField(editable=False, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='RentalTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(max_length=100)),
                ('customer_contact', models.CharField(max_length=20)),
                ('customer_address', models.TextField(blank=True, null=True)),
                ('rental_date', models.DateField()),
                ('expected_return_date', models.DateField()),
                ('actual_return_date', models.DateField(blank=True, null=True)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('price_per_day', models.DecimalField(decimal_places=2, max_digits=10)),
                ('deposit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('payment_method', models.CharField(default='Cash', max_length=50)),
                ('damage_charges', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('missing_charges', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('notes', models.TextField(blank=True, null=True)),
                ('return_notes', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Returned', 'Returned')], max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='items.rentalproduct')),
            ],
        ),
        migrations.CreateModel(
            name='MyKitchenexpense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(max_length=50)),
                ('date', models.DateField()),
                ('naan', models.CharField(max_length=50)),
                ('cold_drinks', models.CharField(max_length=50)),
                ('bbq', models.CharField(max_length=50)),
                ('water', models.CharField(max_length=50)),
                ('payment_details', models.TextField(max_length=300)),
                ('mutton', models.CharField(max_length=50)),
                ('chicken', models.CharField(max_length=50)),
                ('beef', models.CharField(max_length=50)),
                ('rice', models.CharField(max_length=50)),
                ('dahi', models.CharField(max_length=50)),
                ('doodh', models.CharField(max_length=50)),
                ('sabzi', models.CharField(max_length=50)),
                ('fruits', models.CharField(max_length=50)),
                ('khoya_cream_paneer', models.CharField(max_length=50)),
                ('dry_fruits', models.CharField(max_length=50)),
                ('oil', models.CharField(max_length=50)),
                ('other_items_bill', models.CharField(max_length=50)),
                ('other_items_desc', models.CharField(max_length=50)),
                ('total_bill', models.CharField(max_length=50)),
                ('bill', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='ecommerce.eventsale')),
            ],
        ),
        migrations.CreateModel(
            name='EventExpense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(blank=True, max_length=30)),
                ('pakwan_bill', models.IntegerField(blank=True, default=0)),
                ('electicity', models.IntegerField(blank=True, default=0)),
                ('naan_qty', models.IntegerField(blank=True, default=0)),
                ('naan_bill', models.IntegerField(blank=True, default=0, editable=False)),
                ('cold_drink', models.IntegerField(blank=True, default=0)),
                ('cold_drink_bill', models.IntegerField(blank=True, default=0, editable=False)),
                ('cold_drink_type', models.CharField(blank=True, max_length=30)),
                ('water', models.IntegerField(blank=True, default=0)),
                ('water_bill', models.IntegerField(blank=True, default=0, editable=False)),
                ('water_bottles_type', models.CharField(blank=True, max_length=30)),
                ('bbq_kg_qty', models.IntegerField(blank=True, default=0)),
                ('bbq_price', models.IntegerField(blank=True, default=0, editable=False)),
                ('bbq_type', models.CharField(blank=True, max_length=30)),
                ('diesel_ltr', models.IntegerField(blank=True, default=0)),
                ('no_of_waiters', models.IntegerField(blank=True, default=0)),
                ('waiters_bill', models.IntegerField(blank=True, default=0, editable=False)),
                ('stuff_bill', models.IntegerField(blank=True, default=0, editable=False)),
                ('dhobi', models.IntegerField(blank=True, default=0)),
                ('other_expense', models.IntegerField(blank=True, default=0)),
                ('other_expense_detals', models.TextField(blank=True)),
                ('setup_bill', models.IntegerField(blank=True, default=0)),
                ('decor', models.CharField(blank=True, max_length=200)),
                ('decor_bill', models.IntegerField(blank=True, default=0)),
                ('total_expense', models.IntegerField(blank=True, editable=False)),
                ('expense_date', models.DateField()),
                ('bill', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='ecommerce.eventsale')),
            ],
        ),
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_title', models.CharField(max_length=200)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('event_time', models.CharField(max_length=10)),
                ('sale_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='ecommerce.eventsale')),
            ],
        ),
    ]
