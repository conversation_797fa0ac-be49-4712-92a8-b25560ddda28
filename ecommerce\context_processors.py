from django.shortcuts import render , redirect
from items.models import Deals , MyProducts
from datetime import datetime, timedelta
from .models import EventSale

def deals(request):
    deals = Deals.objects.all()
    context = {
        "deals": deals,
    }
    return context

def upcoming_events_count(request):
    """Context processor to provide upcoming events count and details."""
    today = datetime.now().date()
    five_days_later = today + timedelta(days=5)
    tomorrow = today + timedelta(days=1)
    
    # Get upcoming events in the next 5 days (only confirmed)
    upcoming_events = EventSale.objects.filter(
        event_date__gte=today,
        event_date__lte=five_days_later,
        status='Confirm'
    ).order_by('event_date')
    
    # Get urgent events (events tomorrow) (only confirmed)
    urgent_events = EventSale.objects.filter(
        event_date=tomorrow,
        status='Confirm'
    )
    
    upcoming_count = upcoming_events.count()
    has_urgent_events = urgent_events.exists()
    
    return {
        'upcoming_events_count': upcoming_count,
        'upcoming_events': upcoming_events[:5],  # Limit to 5 most recent
        'urgent_events': urgent_events,
        'has_urgent_events': has_urgent_events
    }
            