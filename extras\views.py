from django.shortcuts import render, get_object_or_404
from django.views.generic.base import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from ecommerce.models import EventSale
from django.http import HttpResponse, Http404
from django.core.management import call_command
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.shortcuts import redirect
from django.contrib import messages
from django.conf import settings
import os
import datetime
import mimetypes

# Create your views here.
class Timeline(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-timeline.html"

    LOGIN_URL = "account/login"  

def order_slip(request, sale_id):
    sale = get_object_or_404(EventSale, pk = sale_id)
    # Split quantity and menu items for Invoice to show only items.
    my_menu = {}
    lst = sale.food_menu.split(',')
    # print(lst)
    for ls in lst:
        lss = ls.strip().split(' ')
        
        if len(lss) > 1:
            item = lss[0]
            print(lss)
            my_menu.update({item: lss[1][1:-1]})

    context = {
        'sale': sale,
        'menu': my_menu
    }
    return render(request, 'extras/pages/thermal_order_slip.html', context)
    

class Invoice(LoginRequiredMixin,TemplateView):
    template_name = "extras/sale_invoice.html"
    LOGIN_URL = "account/login"  
# def sale_invoice(request, sale_id):
    
from django.shortcuts import get_object_or_404, render
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin

class sale_invoice(LoginRequiredMixin, View):
    login_url = "account/login"

    def get(self, request, sale_id, format=None):
        sale = get_object_or_404(EventSale, pk=sale_id)
        status = "Paid" if sale.remaining_amount == 0 else "Unpaid"
        my_menu = [ls.strip().split(' ')[0] for ls in sale.food_menu.split(',') if len(ls.strip().split(' ')) > 1]

        total_extra_charges = sale.stage_charges + sale.entry_charges + sale.extra_charges

        template_name = "extras/pages/a4_invoice.html"

        # Calculate correct menu charges (total_amount includes extra charges, so we subtract them)
        menu_charges = sale.total_amount - total_extra_charges
        
        context = {
            'sale': sale,
            'total': sale.total_amount,
            'due': (sale.total_amount - sale.discount_amount) - sale.recieved_amount,
            'net_amount': sale.total_amount - sale.discount_amount,
            'status': status,
            'food_menu': my_menu,
            'extra': total_extra_charges,
            'menu_charges': menu_charges,
            'sub_total': sale.total_amount
        }

        return render(request, template_name, context)


class Details(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/details.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        no_of_people = request.POST.get('no_of_people')
        grand_total = request.POST.get('grand_total')
        menu_items = request.POST.get('menu_items')

        return render(request, self.template_name)

class Blankpage(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-blank.html"
class Error404(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-404.html"
class Error500(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-500.html"
class Pricing(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-pricing.html"
class Maintenance(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-maintenance.html"
class Comingsoon(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-comingsoon.html"
class Faqs(LoginRequiredMixin,TemplateView):
    template_name = "extras/pages/pages-faq.html"
class Lockscreen(LoginRequiredMixin,TemplateView):
    template_name = "authentication/auth-lock-screen.html"
class Login(LoginRequiredMixin,TemplateView):
    template_name = "authentication/auth-login.html"
class Register(LoginRequiredMixin,TemplateView):
    template_name = "authentication/auth-register.html"

@method_decorator(staff_member_required, name='dispatch')
class DatabaseBackupView(TemplateView):
    """View to create and download database backups"""
    template_name = 'extras/database_backup.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get list of existing backups
        backup_dir = os.path.join(settings.BASE_DIR, 'backups')
        backups = []
        
        if os.path.exists(backup_dir):
            for file in os.listdir(backup_dir):
                if file.endswith(('.json', '.sql')):
                    filepath = os.path.join(backup_dir, file)
                    file_size = os.path.getsize(filepath) / (1024 * 1024)  # MB
                    created_time = datetime.datetime.fromtimestamp(os.path.getctime(filepath))
                    
                    backups.append({
                        'filename': file,
                        'size': f"{file_size:.2f} MB",
                        'created': created_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'download_url': f"/extras/download-backup/{file}/"
                    })
        
        context['backups'] = sorted(backups, key=lambda x: x['created'], reverse=True)
        return context

@staff_member_required
def create_backup(request):
    """Create a new database backup"""
    if request.method == 'POST':
        backup_format = request.POST.get('format', 'json')
        
        try:
            from django.core.management import call_command
            from io import StringIO
            import sys
            
            # Capture output
            old_stdout = sys.stdout
            sys.stdout = captured_output = StringIO()
            
            # Run backup command
            call_command('backup_db', format=backup_format)
            
            # Restore stdout
            sys.stdout = old_stdout
            output = captured_output.getvalue()
            
            messages.success(request, f'Database backup created successfully! Format: {backup_format}')
            
        except Exception as e:
            messages.error(request, f'Error creating backup: {str(e)}')
    
    return redirect('extras:database_backup')

@staff_member_required  
def download_backup(request, filename):
    """Download a database backup file"""
    backup_dir = os.path.join(settings.BASE_DIR, 'backups')
    file_path = os.path.join(backup_dir, filename)
    
    if not os.path.exists(file_path):
        raise Http404("Backup file not found")
    
    # Security check - ensure file is in backups directory
    if not os.path.commonpath([backup_dir, file_path]) == backup_dir:
        raise Http404("Invalid file path")
    
    # Get file info
    file_size = os.path.getsize(file_path)
    content_type, _ = mimetypes.guess_type(file_path)
    
    if not content_type:
        content_type = 'application/octet-stream'
    
    # Create response
    with open(file_path, 'rb') as f:
        response = HttpResponse(f.read(), content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        response['Content-Length'] = file_size
        
    return response