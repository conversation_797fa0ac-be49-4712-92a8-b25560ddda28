from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class UserProfile(models.Model):
    ADMIN = 'admin'
    OPERATOR = 'operator'
    
    ROLE_CHOICES = [
        (ADMIN, 'Admin'),
        (OPERATOR, 'Operator'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default=OPERATOR)
    
    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"
