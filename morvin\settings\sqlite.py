from .base import *
import os

DEBUG = True

ALLOWED_HOSTS = ['*', 'healthcheck.railway.app', '*.railway.app', 'software.sultanatmarquee.com', '.sultanatmarquee.com', '.vercel.app', '.now.sh']

# SQLite Database Configuration
# Store database outside project directory to prevent data loss during deployment
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR.parent / 'persistent_data' / 'db_data',
    }
}

# Backup settings for SQLite
DBBACKUP_STORAGE = 'django.core.files.storage.FileSystemStorage'
DBBACKUP_STORAGE_OPTIONS = {'location': BASE_DIR.parent / 'persistent_data' / 'db_backups'}

# Ensure directories exist
os.makedirs(BASE_DIR.parent / 'persistent_data', exist_ok=True)
os.makedirs(BASE_DIR.parent / 'persistent_data' / 'db_backups', exist_ok=True)

# Additional SQLite optimizations
DATABASES['default']['OPTIONS'] = {
    'timeout': 20,
    'check_same_thread': False,
}

# Static files configuration for deployment
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

# CSRF Trusted Origins
CSRF_TRUSTED_ORIGINS = [
    'https://software.sultanatmarquee.com',
    'https://*.railway.app',
    'https://*.vercel.app',
    'http://localhost',
    'http://127.0.0.1',
    'http://localhost:8000',
    'http://127.0.0.1:8000',
] 