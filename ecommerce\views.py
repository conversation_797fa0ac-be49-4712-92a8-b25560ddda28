from django.shortcuts import render , redirect,get_object_or_404
from django.views.generic.base import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.http import require_GET
from django.db.models import Sum, Q, Count, F, ExpressionWrapper, DecimalField, DateField
from ecommerce.models import EventSale
from ecommerce.models import EventExpense
from ecommerce.models import MyKitchenexpense
from items.models import Deals
import json
from django.utils import timezone
from generalExpense.models import Salary, DailyExpenses, ConstructionAndRepair, OtherExpense
from django.http import JsonResponse
from django.views.decorators.http import require_GET
from django.urls import reverse
from django.views import View
from items.models import Deals , MyProducts,Brand, Unit, Inventory
from rest_framework import viewsets
from decimal import Decimal 
from .models import Event
from .serializers import EventSerializer
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.core.serializers import serialize
from django.http import JsonResponse
from items.models import Category
from datetime import datetime, timedelta
import time
from django.db.models.functions import ExtractMonth
from django.contrib import messages
from django.db import IntegrityError
from django.db import transaction
from django.views.generic import DetailView
from django.core.paginator import Paginator
from extras.decorators import admin_required
import calendar
from django.utils import timezone
from datetime import datetime, timedelta
from collections import defaultdict

# Use timezone-aware current date for deployment compatibility
current_date = timezone.now()

# Create your views here.
class Products(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/ecommerce-products.html"
    LOGIN_URL = "account/login"  

    def get(self, request):
        cat = Category.objects.all()
        products = MyProducts.objects.all()


       


        context = {
            "category": cat,
            "products": products,
        }
        return render(request, self.template_name, context)

class ProductsDetail(LoginRequiredMixin,TemplateView):
    
    template_name = "ecommerce/ecommerce-product-detail.html"

    LOGIN_URL = "account/login"  




class ProductList(LoginRequiredMixin,TemplateView):
    LOGIN_URL = "account/login/"  

    template_name = "ecommerce/product_list.html"
    def get(self, request):
        myproudcts = MyProducts.objects.all()
        category = Category.objects.all()
        brand = Brand.objects.all()
        unit = Unit.objects.all()

        context = {
            "myproducts": myproudcts,
            "brands": brand,
            "category": category,
            "units": unit,
        }
        return render(request, self.template_name, context)


from django.db.models import Sum
from django.shortcuts import render
from datetime import date
from collections import defaultdict

@method_decorator(admin_required, name='dispatch')
class ExpenseSummary(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/finance_reports/expense_summary.html"
    LOGIN_URL = "account/login"

    def get(self, request):
        # Aggregate totals from EventExpense (only for confirmed events)
        expense_totals = EventExpense.objects.filter(bill__status='Confirm').aggregate(
            total_pakwan=Sum('pakwan_bill'),
            total_diesel=Sum('diesel_ltr'),
            total_electricity=Sum('electicity'),
            total_dhobi=Sum('dhobi'),
            total_other_expense=Sum('other_expense'),
            total_waiters=Sum('waiters_bill'),
            total_stuff=Sum('stuff_bill'),
            total_setup=Sum('setup_bill'),
            total_decor=Sum('decor_bill'),
            total_naan=Sum('naan_qty'),
            total_cold_drinks=Sum('cold_drink'),
            total_bbq=Sum('bbq_kg_qty'),
            total_water=Sum('water')
        )

        # Initialize dynamic food totals
        food_menu_totals = {
            'Naan': expense_totals['total_naan'],  # Fixed name
            'BBQ': 0,
            'Cold Drinks': 0,
            'Water': 0
        }
        dynamic_items = {}

        # Aggregate totals from EventSale (parse the food_menu)
        sales = EventSale.objects.filter(status='Confirm')

        # Process each sale's food menu
        for sale in sales:
            if sale.food_menu:
                items = sale.food_menu.split(',')
                for item in items:
                    item = item.strip()
                    if '(' in item and ')' in item:
                        name = item.split('(')[0].strip()
                        try:
                            qty = int(item.split('(')[1].split(')')[0])
                            if name in dynamic_items:
                                dynamic_items[name] += qty
                            else:
                                dynamic_items[name] = qty
                        except (ValueError, IndexError):
                            continue

        # Add dynamic items to food totals
        food_menu_totals.update(dynamic_items)

        context = {
            'expense_totals': expense_totals,
            'food_menu_totals': food_menu_totals
        }
        return render(request, self.template_name, context)


import re

def parse_food_menu(food_menu):
    pattern = r'([^(]+)\s*\((\d+)\)'
    return [match[0].strip() for match in re.findall(pattern, food_menu)]

def product_list(request):
    # Fetch products from EventSale (only confirmed)
    event_sale_products = EventSale.objects.filter(status='Confirm').values_list('food_menu', flat=True).distinct()
    parsed_sale_products = set()
    for food_menu in event_sale_products:
        parsed_sale_products.update(parse_food_menu(food_menu))
    
    # Add specified EventExpense fields
    expense_fields = [
        'Pakwan_Bill', 'Diesel', 'Electicity_Bill', 'Dhobi', 
        'Other_Expenses', 'Waiters', 'Stuff', 'Setup', 'Decor'
    ]
    
    # Combine and sort all unique products
    all_products = sorted(list(parsed_sale_products) + expense_fields)
    
    context = {'products': all_products}
    
    if request.method == 'POST':
        selected_month = request.POST.get('month')
        selected_product = request.POST.get('product')
        
        # Convert selected_month to datetime object
        month_date = datetime.strptime(selected_month, '%Y-%m')
        
        # Filter expenses based on selected month and product (only confirmed)
        expenses = EventExpense.objects.filter(expense_date__year=month_date.year, expense_date__month=month_date.month, bill__status='Confirm')
        
        # Prepare data for template
        expense_data = []
        for index, expense in enumerate(expenses, start=1):
            if selected_product in expense_fields:
                # Handle EventExpense fields
                value = getattr(expense, selected_product.lower(), 0)
                if value:
                    expense_data.append({
                        'no': index,
                        'date': expense.expense_date,
                        'bill': expense.bill,
                        'product_name': selected_product,
                        'total_amount': value
                    })
            else:
                # Handle food_menu items
                food_menu = expense.bill.food_menu
                if selected_product in food_menu:
                    pattern = rf'{re.escape(selected_product)}\s*\((\d+)\)'
                    match = re.search(pattern, food_menu)
                    if match:
                        quantity = int(match.group(1))
                        expense_data.append({
                            'no': index,
                            'date': expense.expense_date,
                            'bill': expense.bill,
                            'product_name': selected_product,
                            'total_amount': f"{quantity} units"
                        })
        
        context.update({
            'expense_data': expense_data,
            'selected_month': selected_month,
            'selected_product': selected_product,
        })
    
    return render(request, 'ecommerce/finance_reports/expense_monthly_report.html', context)


@method_decorator(admin_required, name='dispatch')
class HallSaleSummary(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/finance_reports/hall_sale.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        # Query the database to get total sales for every month in the current year
        monthly_sales = EventSale.objects.filter(
            event_date__year=current_date.year,
            status='Confirm'
        ).annotate(month=ExtractMonth('event_date')) \
            .values('month') \
            .annotate(total_sales=Sum('total_amount')) \
            .order_by('month')
            
        month_sales_dict = {entry['month']: entry['total_sales'] for entry in monthly_sales}

        # Initialize a list to store total sales for every month
        every_month_sale = []

        # Iterate through all 12 months
        for month in range(1, 13):
            # Check if the month is present in the dictionary
            if month in month_sales_dict:
                total_sales = month_sales_dict[month]
            else:
                total_sales = 0  # Set total sales to 0 for missing months
            every_month_sale.append(total_sales)

        # Define the months and days in each month
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

        # Initialize an empty dictionary to store the data
        dailySalesData = {}

        # Loop through each month and query the data
        for i, month in enumerate(months):
            days = days_in_month[i]
            data = EventSale.objects.filter(
                event_date__month=i + 1,
                status='Confirm'
            ).values('event_date').annotate(total_sales=Sum('total_amount')).order_by('event_date')
            sales_data = [0] * days

            for entry in data:
                day = entry['event_date'].day
                count = entry['total_sales']
                sales_data[day - 1] = count

            dailySalesData[month] = sales_data

        # Sale Table - Only show confirmed sales
        all_sale = EventSale.objects.filter(status='Confirm')

        context = {
            "sale": every_month_sale,
            "daily_sale" : json.dumps(dailySalesData),
            "sale_table" : all_sale
        }
        
        return render(request, self.template_name, context)
        


    
@method_decorator(admin_required, name='dispatch')
class HallExpenseSummary(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/finance_reports/hall_expense.html"
    LOGIN_URL = "account/login"  
    def get(self, request):

       # montly expense
        expense_list = []
        
        # Get total expenses for each month based on the EventSale's create date (only confirmed events)
         # Query the database to get total sales for every month in the current year
        monthly_expense = EventExpense.objects.filter(expense_date__year=current_date.year, bill__status='Confirm').annotate(month=ExtractMonth('expense_date')) \
            .values('month') \
            .annotate(total_expense=Sum('total_expense')) \
            .order_by('month')
        
        month_expense_dict = {entry['month']: entry['total_expense'] for entry in monthly_expense}
        
           # Iterate through all 12 months
        for month in range(1, 13):
            # Check if the month is present in the dictionary
            if month in month_expense_dict:
                total_expense = month_expense_dict[month]
            else:
                total_expense = 0  # Set total expense to 0 for missing months
            expense_list.append(total_expense)

        # print(expense_list , 'expense')


        # Define the months and days in each month
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
 
        dailyExpenseData = {}
         # Loop through each month and query the data
        for i, month in enumerate(months):
            days = days_in_month[i]
            data = EventExpense.objects.filter(expense_date__month=i + 1, bill__status='Confirm').values('expense_date').annotate(total_Expense=Sum('total_expense')).order_by('expense_date')
            Expense_data = [0] * days

            for entry in data:
                day = entry['expense_date'].day
                count = entry['total_Expense']
                Expense_data[day - 1] = count

            dailyExpenseData[month] = Expense_data
                
        # print(dailyExpenseData)
        
        all_expense = EventExpense.objects.filter(bill__status='Confirm')

   
        context = {
            "expense": expense_list,
            "daily_expense": json.dumps(dailyExpenseData),
            "expense_table" : all_expense  
        }
        
        return render(request, self.template_name, context)
    


@method_decorator(admin_required, name='dispatch')
class KitchenSaleSummary(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/finance_reports/kitchen_sale.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
              
        all_kitchen_sale = MyKitchenexpense.objects.filter(bill__status='Confirm')  # Only confirmed kitchen sales

   
        context = {
         
            "kitchen_sale_table" : all_kitchen_sale  
        }
        
        return render(request, self.template_name, context)
    
    
    
    
@method_decorator(admin_required, name='dispatch')
class KitchenExpenseSummary(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/finance_reports/kitchen_expense.html"  
    LOGIN_URL = "account/login"  
    def get(self, request):
        # Fetch sales, deals, and kitchen expenses - Only confirmed sales for dropdown
        sales = EventSale.objects.filter(status='Confirm')
        kitchen_expense = MyKitchenexpense.objects.filter(bill__status='Confirm')

        context = {
            "sales": sales,
            "kitchen_expense": kitchen_expense
        }
        return render(request, self.template_name, context)
    

@method_decorator(admin_required, name='dispatch')
class Summary(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/finance_reports/summaries.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        try:
            # Use timezone-aware current date for deployment compatibility
            current_date = timezone.now()
            monthly_data = []

            # Iterate through the 12 months
            for month in range(1, 13):
                # Event Sale total for the month (only confirmed)
                event_sale_total = (
                    EventSale.objects
                    .filter(event_date__month=month, event_date__year=current_date.year, status='Confirm')
                    .aggregate(total=Sum('total_amount'))
                )['total'] or 0

                # Event Expense total for the month (only for confirmed events)
                event_expense_total = (
                    EventExpense.objects
                    .filter(expense_date__month=month, expense_date__year=current_date.year, bill__status='Confirm')
                    .aggregate(total=Sum('total_expense'))
                )['total'] or 0

                # Kitchen sale total (only confirmed)
                kitchen_sale_total = (
                    EventSale.objects
                    .filter(event_date__month=month, event_date__year=current_date.year, status='Confirm')
                    .aggregate(total=Sum('total_menu'))
                )['total'] or 0

                # Kitchen expense total (only for confirmed events)
                # Note: total_bill is a CharField, so we need to cast it to Decimal for aggregation
                kitchen_expense_total = 0
                try:
                    kitchen_expenses = MyKitchenexpense.objects.filter(
                        date__month=month, 
                        date__year=current_date.year, 
                        bill__status='Confirm'
                    )
                    for expense in kitchen_expenses:
                        try:
                            bill_amount = Decimal(expense.total_bill) if expense.total_bill else 0
                            kitchen_expense_total += bill_amount
                        except (ValueError, TypeError):
                            # Skip invalid values
                            continue
                except Exception:
                    kitchen_expense_total = 0

                # Calculate event_profit, kitchen_profit, gross_profit
                event_profit = event_sale_total - event_expense_total
                kitchen_profit = kitchen_sale_total - kitchen_expense_total
                gross_profit = event_profit + kitchen_profit

                # Calculate general_expense for the month
                general_expense_total = (
                    Salary.objects
                    .filter(on_date__month=month, on_date__year=current_date.year)
                    .aggregate(total=Sum('amount'))
                )['total'] or 0

                general_expense_total += (
                    DailyExpenses.objects
                    .filter(on_date__month=month, on_date__year=current_date.year)
                    .aggregate(total=Sum('amount'))
                )['total'] or 0

                # Calculate net profit
                net_profit = gross_profit - general_expense_total

                # Add month's data to list
                monthly_data.append({
                    'month': calendar.month_name[month],
                    'event_total_sale': event_sale_total,
                    'event_total_expense': event_expense_total,
                    'event_profit': event_profit,
                    'kitchen_total_sale': kitchen_sale_total,
                    'kitchen_total_expense': kitchen_expense_total,
                    'kitchen_profit': kitchen_profit,
                    'gross_profit': gross_profit,
                    'general_expense': general_expense_total,
                    'net_profit': net_profit
                })

            context = {
                'monthly_data': monthly_data
            }
            return render(request, self.template_name, context)
        
        except Exception as e:
            # Log the error for debugging in production
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in Summary view: {str(e)}")
            
            # Return empty data instead of crashing
            context = {
                'monthly_data': [],
                'error_message': 'Unable to load summary data. Please try again later.'
            }
            return render(request, self.template_name, context)
    
          

from django.core.paginator import Paginator
from django.shortcuts import render
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin

class Eventsale(LoginRequiredMixin, View):
    template_name = "ecommerce/event-sale.html"
    login_url = "account/login"

    def get(self, request):
        # Filter for confirmed sales only - ORDER BY MOST RECENT FIRST
        sales = EventSale.objects.filter(status='Confirm').order_by('-id').only(
            "id", "food_menu", "stage_charges", "entry_charges", "extra_charges", 
            "total_amount", "discount_amount", "recieved_amount"
        )
        
        # Apply pagination BEFORE looping
        paginator = Paginator(sales, 50)  # Only fetch 50 per page
        page_number = request.GET.get("page")
        page_obj = paginator.get_page(page_number)

        # Process only the paginated records
        for sale in page_obj:
            sale.menu_items = [item.strip().split(" ")[0] for item in sale.food_menu.split(",") if len(item.strip().split(" ")) > 1]
            sale.total_extra_charges = sale.stage_charges + sale.entry_charges + sale.extra_charges
            sale.total_due = sale.total_amount + sale.discount_amount - (sale.recieved_amount + sale.total_extra_charges)

        context = {
            "sales": page_obj,  # Pass paginated data
            "deals": Deals.objects.all()
        }
        return render(request, self.template_name, context)





class Tentative(LoginRequiredMixin, View):
    template_name = "ecommerce/Tentative.html"
    login_url = "account/login"

    def get(self, request):
        from .models import EventSale  # Ensure you import the correct model

        # Filter for tentative sales only - ORDER BY MOST RECENT FIRST
        sales = EventSale.objects.filter(status='Tentative ').order_by('-id').only(
            "id", "food_menu", "stage_charges", "entry_charges", "extra_charges", 
            "total_amount", "discount_amount", "recieved_amount"
        )
        
        # Apply pagination BEFORE looping
        paginator = Paginator(sales, 50)  # Only fetch 50 per page
        page_number = request.GET.get("page")
        page_obj = paginator.get_page(page_number)

        # Process only the paginated records
        for sale in page_obj:
            sale.menu_items = [item.strip().split(" ")[0] for item in sale.food_menu.split(",") if len(item.strip().split(" ")) > 1]
            sale.total_extra_charges = sale.stage_charges + sale.entry_charges + sale.extra_charges
            sale.total_due = sale.total_amount + sale.discount_amount - (sale.recieved_amount + sale.total_extra_charges)

        context = {
            "sales": page_obj,  # Pass paginated data
            "deals": Deals.objects.all()
        }
        return render(request, self.template_name, context)







    

def delete_sale(request, sale_id):
    
   

    sales = get_object_or_404(EventSale, id=sale_id)
   
    
    if sales is not None:
        try:
            sales.delete()
            messages.success(request, "Event Deleted successfully")
            return redirect("event-sale")

        except IntegrityError as e:
            error_message = str(e)
            if "Cannot delete some instances of model 'EventSale'" in error_message:
                messages.error(request, "Cannot delete Sale. Check for any expense related to sale you are deleting, Delete them first. ")
    else:
        return redirect("event-sale")

    sale = EventSale.objects.filter(status='Confirm')  # Only confirmed sales
    deals = Deals.objects.all()


    context = {
            "sales": sale,
            
            "deals": deals
        }
    
    return render(request, 'ecommerce/event-sale.html', context=context)

class Kitchensale(LoginRequiredMixin, View):
    template_name = "ecommerce/kitchen-sale.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        sale = EventSale.objects.filter(status='Confirm')
        deals = Deals.objects.all()
        total_sales = EventSale.objects.filter(status='Confirm').aggregate(total_sales=Sum('total_amount'))['total_sales']
        total_expenses = EventExpense.objects.filter(bill__status='Confirm').aggregate(total_expenses=Sum('total_expense'))['total_expenses']

        total_sales = total_sales or 0
        total_expenses = total_expenses or 0

        context = {
            "sales": sale,
            "deals": deals
        }
        return render(request, self.template_name, context)

def get_product_price(request):
    product_name = request.GET.get('product_name')
    try:
        # Use __icontains to match the keyword in the product name
        product = MyProducts.objects.filter(product_name__icontains=product_name).first()
        if product:
            return JsonResponse({'price': product.price})
        else:
            return JsonResponse({'error': 'Product not found'}, status=404)
    except MyProducts.DoesNotExist:
        return JsonResponse({'error': 'Product not found'}, status=404)

class Kitchenexpense(LoginRequiredMixin, View):
    template_name = "ecommerce/kitchen-expense.html"

    def get(self, request):
        # Fetch sales, deals, and kitchen expenses - Only confirmed sales for dropdown
        sales = EventSale.objects.filter(status='Confirm')
        kitchen_expense = MyKitchenexpense.objects.filter(bill__status='Confirm')

        context = {
            "sales": sales,
            "kitchen_expense": kitchen_expense
        }
        return render(request, self.template_name, context)

    def parse_food_menu(self, food_menu):
        """Parse the food_menu string and return a dictionary of item names and quantities."""
        menu_items = {}
        items = food_menu.split(",")  # Split by comma to separate each item
        for item in items:
            if "(" in item and ")" in item:
                name = item.split("(")[0].strip()  # Extract item name
                quantity = int(item.split("(")[1].replace(")", "").strip())  # Extract quantity in parentheses
                menu_items[name] = quantity  # Store in dictionary
        return menu_items

    def calculate_menu_prices(self, menu_items):
        """Calculate the total price for items based on keywords like 'water', 'bbq', 'drink', and 'naan'."""
        menu_prices = {
            'Naan': {'total_price': 0},
            'Cold_Drinks': {'total_price': 0},
            'bbq': {'total_price': 0},
            'water': {'total_price': 0}
        }

        for item_name, quantity in menu_items.items():
            # Match keywords in product names and sum the prices
            if "naan" in item_name.lower():
                product = MyProducts.objects.filter(product_name__icontains='naan').first()
                if product:
                    menu_prices['Naan']['total_price'] += product.price * quantity

            elif "drink" in item_name.lower():
                product = MyProducts.objects.filter(product_name__icontains='drink').first()
                if product:
                    menu_prices['Cold_Drinks']['total_price'] += product.price * quantity

            elif "bbq" in item_name.lower():
                product = MyProducts.objects.filter(product_name__icontains='bbq').first()
                if product:
                    menu_prices['bbq']['total_price'] += product.price * quantity

            elif "water" in item_name.lower():
                product = MyProducts.objects.filter(product_name__icontains='water').first()
                if product:
                    menu_prices['water']['total_price'] += product.price * quantity

        return menu_prices
    
    def post(self, request):
        if request.method == "POST":
            bill_num = request.POST.get('bill')
            payment = request.POST.get('payment-details')
            naan_price = request.POST.get('naan')
            cold_drinks_price = request.POST.get('cold_drinks')
            bbq_price = request.POST.get('bbq')
            water_price = request.POST.get('water')
            date = request.POST.get('date')
            mutton = request.POST.get('mutton')
            chicken = request.POST.get('chicken')
            beef = request.POST.get('beef')
            rice = request.POST.get('rice')
            dahi = request.POST.get('dahi')
            doodh = request.POST.get('doodh')
            sabzi = request.POST.get('sabzi')
            fruits = request.POST.get('fruits')
            khoya_paneer = request.POST.get('khoya-paneer')
            dry = request.POST.get('dry-fruits')
            oil = request.POST.get('oil')
            other = request.POST.get('other-items-bill')
            other_desc = request.POST.get('other-items-desc')

            # Fetch the EventSale object based on the provided bill_num
            try:
                event_sale = EventSale.objects.get(id=bill_num)
            except EventSale.DoesNotExist:
                return redirect('kitchen-expense')  # Redirect to avoid crashing the page
            else:
                # Parse the food_menu from the EventSale
                menu_items = self.parse_food_menu(event_sale.food_menu)

                # Calculate prices for each item in the menu
                menu_prices = self.calculate_menu_prices(menu_items)

                # Extract the calculated prices for Naan, Cold Drinks, BBQ, and Water
                calculated_naan_price = menu_prices.get('Naan', {}).get('total_price', 0)
                calculated_bbq_price = menu_prices.get('bbq', {}).get('total_price', 0)
                calculated_cold_drinks_price = menu_prices.get('Cold_Drinks', {}).get('total_price', 0)
                calculated_water_price = menu_prices.get('water', {}).get('total_price', 0)

                # Use the calculated prices if the POST data does not provide them
                naan_price = naan_price or calculated_naan_price
                bbq_price = bbq_price or calculated_bbq_price
                cold_drinks_price = cold_drinks_price or calculated_cold_drinks_price
                water_price = water_price or calculated_water_price

                # Calculate the total bill, including mutton, chicken, etc.
                total = (int(mutton) + int(chicken) + int(beef) + int(rice) + int(dahi) +
                         int(doodh) + int(sabzi) + int(fruits) + int(khoya_paneer) +
                         int(oil) + int(other) + int(dry) + int(naan_price) + int(bbq_price) +
                         int(water_price) + int(cold_drinks_price))

                # Create the Kitchenexpense object using the EventSale object
                MyKitchenexpense.objects.create(
                    bill=event_sale,  # Use the EventSale object
                    customer_name=event_sale.customer_name,
                    date=date,
                    naan=naan_price,
                    cold_drinks=cold_drinks_price,
                    bbq=bbq_price,
                    water=water_price,
                    payment_details=payment,
                    mutton=mutton,
                    chicken=chicken,
                    beef=beef,
                    rice=rice,
                    dahi=dahi,
                    doodh=doodh,
                    sabzi=sabzi,
                    fruits=fruits,
                    dry_fruits=dry,
                    khoya_cream_paneer=khoya_paneer,
                    oil=oil,
                    other_items_bill=other,
                    other_items_desc=other_desc,
                    total_bill=total  # Total bill including calculated items
                )
        
        return redirect('kitchen-expense')

class KitchenexpenseUpdate(LoginRequiredMixin, View):
    template_name = "ecommerce/kitchen-expense.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        sale = EventSale.objects.filter(status='Confirm')  # Only confirmed sales
        deals = Deals.objects.all()
        kitchen_expense = MyKitchenexpense.objects.filter(bill__status='Confirm')  # Only confirmed kitchen expenses
        total_sales = EventSale.objects.filter(status='Confirm').aggregate(total_sales=Sum('total_amount'))['total_sales']
        total_expenses = EventExpense.objects.filter(bill__status='Confirm').aggregate(total_expenses=Sum('total_expense'))['total_expenses']

        total_sales = total_sales or 0
        total_expenses = total_expenses or 0

      


        context = {
            "sales": sale,
            
            "deals": deals,

            "kitchen_expense" : kitchen_expense
        }
        return render(request, self.template_name, context)
    
    def post(self, request,kitchen_expense_id):
        if request.method == "POST":
            requests = MyKitchenexpense.objects.get(id=kitchen_expense_id)

            print("I am here")
            bill_num = request.POST.get('bill')
            payment = request.POST.get('payment-details')

            date = request.POST.get('date')
            mutton = request.POST.get('mutton')

            chicken = request.POST.get('chicken')
            beef = request.POST.get('beef')
            rice = request.POST.get('rice')

            dahi = request.POST.get('dahi')
            doodh = request.POST.get('doodh')
            sabzi = request.POST.get('sabzi')
            fruits = request.POST.get('fruits')

            khoya_paneer = request.POST.get('khoya-paneer')
            dry = request.POST.get('dry-fruits')
            oil = request.POST.get('oil')
            other = request.POST.get('other-items-bill')
            other_desc = request.POST.get('other-items-desc')

            
            # Fetch the EventSale object based on the provided bill_num
            try:
                event_sale = EventSale.objects.get(id=bill_num)
            except EventSale.DoesNotExist:
                # Handle the case where the EventSale with the given ID doesn't exist
                # You can return an error message or redirect to an error page
                pass
            else:
                # Create the Kitchenexpense object using the EventSale object
                total=int(mutton) + int(chicken) + int(beef) + int(rice) + int(rice) + int(dahi) + int(doodh) + int(sabzi) + int(fruits) + int(khoya_paneer) + int(oil) + int(other) + int(dry)
                
                requests.bill=event_sale,  # Use the EventSale object
                requests.customer_name = event_sale.customer_name,
                requests.date=date,
                requests.payment_details=payment,
                requests.mutton=mutton,
                requests.chicken=chicken,
                requests.beef=beef,
                requests.rice=rice,
                requests.dahi=dahi,
                requests.doodh=doodh,
                requests.sabzi=sabzi,
                requests.fruits=fruits,
                requests.dry_fruits = dry,
                requests.khoya_cream_paneer=khoya_paneer,
                requests.oil=oil,
                requests.other_items_bill=other,
                requests.other_items_desc=other_desc,
                requests.total_bill= total
                requests.save()

                return redirect('kitchen-expense')
                

        return render(request, "ecommerce/kitchen-expense.html")

def delete_kitchen_expense(request, kitchen_expense_id):
    

    expense = get_object_or_404(MyKitchenexpense, pk=kitchen_expense_id)
    # print(event.id)
    if expense is not None:
        try:
            expense.delete()
            messages.success(request, "Deleted Successfully")
            return redirect("kitchen-expense")


        except IntegrityError as e:
            error_message = str(e)
            if "Cannot delete some instances of model 'EventSale'" in error_message:
                messages.error(request, "Cannot delete due to related objects. Hint: Check for any expense relatd to sale you are deleting, Delete them first. ")
    else:
        return redirect("kitchen-expense")

    sale = EventSale.objects.filter(status='Confirm')  # Only confirmed sales
    deals = Deals.objects.all()
    kitchen_expense = MyKitchenexpense.objects.filter(bill__status='Confirm')  # Only confirmed kitchen expenses
    context = {
            "sales": sale,
            
            "deals": deals,

            "kitchen_expense" : kitchen_expense
        }
    
    return render(request, 'ecommerce/kitchen-expense.html', context=context)

def DeleteProducts(request, product_id):
        
    product = get_object_or_404(MyProducts, pk=product_id)
    # print(event.id)
    if product is not None:
        product.delete()
        return redirect("ecommerce-product-list")
    
    category = Category.objects.all()
    brand = Brand.objects.all()
    unit = Unit.objects.all()
    products = MyProducts.objects.all()
    
    context = {
        "category": category,
        "brands": brand,
        "units": unit,
        "products": products
    }
    return render(request, "ecommerce/product_list.html", context = context)

class UpdateEventsale(LoginRequiredMixin, View):
    template_name = "ecommerce/event-sale.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        sale = EventSale.objects.filter(status='Confirm')  # Only confirmed sales
        deals = Deals.objects.all()
        total_sales = EventSale.objects.filter(status='Confirm').aggregate(total_sales=Sum('total_amount'))['total_sales']
        total_expenses = EventExpense.objects.filter(bill__status='Confirm').aggregate(total_expenses=Sum('total_expense'))['total_expenses']

        total_sales = total_sales or 0
        total_expenses = total_expenses or 0

      


        context = {
            "sales": sale,
            
            "deals": deals
        }
        return render(request, self.template_name, context)
    
    
    def post(self, request, sale_id):
        if request.method == "POST":

            # Updating Sales
            requests = EventSale.objects.get(id=sale_id)

            bill_number = request.POST.get('bill-no')
            serial = request.POST.get('serial-no')

            event_status = request.POST.get('status')
            event_time = request.POST.get('event-time')
            location = request.POST.get('location')

            event_date = request.POST.get('event-date')
            number_of_people = request.POST.get('no-of-people')
            setup = request.POST.get('setup')

            deals = request.POST.get('deals')
            customer_name = request.POST.get('customer-name')
            customer_number = request.POST.get('customer-number')
            per_head = request.POST.get('per-head')
            extra_charge = request.POST.get('extra-charges')
            stage_charges = request.POST.get('stage-charges')
            entry_charges = request.POST.get('entry-charges')
            hall_charges = request.POST.get('hall')

            details = request.POST.get('details')
            received_ammount = request.POST.get('received-amount')

            # Get discount parameters
            discount_amount = request.POST.get('discount-amount')
            discount_type = request.POST.get('discount')
            
            total = (int(number_of_people) * int(per_head)) + (int(extra_charge) + int(stage_charges) + int(entry_charges))

            # Calculate discount
            discount = 0
            if discount_amount and discount_type:
                # Calculate by discount type
                if discount_type == 'percent':
                    discount = ((int(discount_amount) / 100) * total)
                
                # Calculate by fix price
                elif discount_type == "fix":
                    discount = int(discount_amount)
                    
                # Calculate by per head
                elif discount_type == "per-head":
                    discount = total - (int(number_of_people) * int(discount_amount))
                    requests.per_head = int(discount_amount)  # Update per_head for per-head discount 

            payments_details = ''
            if not received_ammount == "0":
                now = datetime.now()    
                formatted_date = now.strftime("%Y-%m-%d %H:%M:%S")
                
                requests.payment_count = requests.payment_count + 1
                payments_details = f"\n{requests.payment_count}: was {received_ammount}. Change date was: {formatted_date}"

            



            requests.bill_no = bill_number
            requests.sr = serial

            requests.status = event_status
            requests.event_timing = event_time
            requests.location = location

            requests.event_date = event_date
            requests.no_of_people = number_of_people
            requests.setup = setup

            requests.customer_name = customer_name
            requests.customer_number = customer_number
            # print(requests.deals)
            # deal = get_object_or_404(Deals, pk=requests.deals)
            # requests.deals = deal
            if len(payments_details) > 1:
                requests.payment_details = requests.payment_details + payments_details

            requests.per_head = per_head
            
            requests.extra_charges = extra_charge
            requests.stage_charges = stage_charges
            requests.entry_charges = entry_charges
            requests.hall_charges = hall_charges
            

           
            requests.detials = details
            new_rec_amount = requests.recieved_amount + int(received_ammount)
            requests.recieved_amount = new_rec_amount
            requests.total_amount = total
            requests.discount_amount = discount

            requests.remaining_amount = int(total) - int(new_rec_amount) - int(discount)

            requests.save()

            # # Updating Events
            # e_requests = Event.objects.get(id=sale_id)
            # e_requests.event_title = customer_name
            # e_requests.start_date = event_date
            # e_requests.end_date = event_date
            # e_requests.event_time = event_time
            # e_requests.save()

            
        
            

        # Smart redirect based on the updated status
        if event_status == 'Tentative ':
            return redirect('tentativeSales')
        else:
            return redirect('event-sale')

 
 
 
 
 

class UpdateEventExpense(LoginRequiredMixin, View):
    template_name = "ecommerce/event-expense.html"

    LOGIN_URL = "account/login"  
    
    def post(self, request, expense_id):
        # try:
            if request.method == "POST":
                
                requests = EventExpense.objects.get(id=expense_id)

                
                bill = request.POST.get('bill')
                
                bill_number = get_object_or_404(EventSale, bill_no=bill)
                customer_name = bill_number.customer_name
                pakwan = int(request.POST.get('pakhwan'))

                electicity = request.POST.get('electicity-bill')
                # naan =int(request.POST.get('naan-qty'))
                
            
                # drinks = int(request.POST.get('cold-drinks'))
                # drinks_type = request.POST.get('cold-drinks-type')


                # water = int(request.POST.get('water-bottles'))
                # water_type = request.POST.get('water-bottles-type')

                # bbqs = int(request.POST.get('bbq-qty'))
                # bbq_type = request.POST.get('bbq-type')

                diesel = request.POST.get('diesel-ltr')
                no_of_waiters = request.POST.get('no-of-waiters')
                dhobi = request.POST.get('dhobi')
                stuff = request.POST.get('stuff')

                other_expenses = request.POST.get('other-expense')

                expense_details = request.POST.get('expense-details')
            
                setup = request.POST.get('setup-bill')
                decor = request.POST.get('decore-details')
                decor_bill = request.POST.get('decor-bill')

                # try:
                # nan = MyProducts.objects.get(product_name='Naan')
                # naan_price = nan.price * naan




                # drink = 0
                # if drinks_type == 'Cold Drinks 1.5L':
                #     drink = MyProducts.objects.get(product_name='Cold_Drinks_1.5L')


                # elif drinks_type == "Cold Drinks Tin":
                #     drink = MyProducts.objects.get(product_name='Cold_Drinks_Tin')
                    

                
                # elif drinks_type == "Cold Drinks 2.5L":
                #     drink = MyProducts.objects.get(product_name='Cold_Drinks_2.5L')
                    


                # else:
                #     drink = 0

                # if not drink == 0 :
                #     drink = drink.price * drinks

                
                # bottles = 0
                # if water_type == 'Water 1.5L':
                #     bottles = MyProducts.objects.get(product_name='Water_1.5L')

                # elif water_type == "Water 500ML":
                #     bottles = MyProducts.objects.get(product_name='Water_500ML')

                # elif water_type == "Water 300ML":
                #     bottles = MyProducts.objects.get(product_name='Water_300ML')

                # else:
                #     bottles = 0
                
                # if not bottles == 0 :
                #     bottles = bottles.price * water
                
                # bbqs = MyProducts.objects.get(product_name="BBQ")
                # bbq_price = bbq * bbqs.price

                # bbq = 0
                # if bbq_type == 'Reshmi Kabab':
                #     bbq = MyProducts.objects.get(product_name='Reshmi_Kabab')

                # elif bbq_type == "Chicken Achari Boti":
                #     bbq = MyProducts.objects.get(product_name='Chicken_Achari_Boti')

                # elif bbq_type == "Seekh Kabab":
                #     bbq = MyProducts.objects.get(product_name='Seekh_Kabab')

                # elif bbq_type == "Malai Boti":
                #     bbq = MyProducts.objects.get(product_name='Malai_Boti')

                # else:
                #     bbq = 0
                
            
                # if not bbq == 0 :
                #     bbq = bbqs * bbq.price

                wait = MyProducts.objects.get(product_name="Waiters")
                waiters = wait.price * int(no_of_waiters)

                pakwan = int(pakwan)
                electicity = int(electicity)
                # naan_price = int(naan_price)
                # drink = int(drink)
                # bottles = int(bottles)
                # bbq_price = int(bbq)
                diesel = int(diesel)
                waiters = int(waiters)
                stuff = int(stuff)
                dhobi = int(dhobi)
                other_expenses = int(other_expenses)
                setup = int(setup)
                decor_bill = int(decor_bill)
                # print(pakwan, naan_price, drink, bottles, bbq_price, diesel, waiters, stuff, dhobi, other_expenses, setup, decor_bill)

                # print("New ")
                # print(pakwan, electicity, naan_price, bottles, drink, bbq_price, diesel, waiters, stuff, dhobi, other_expenses, setup, decor_bill )

                # print("OLD")
                # print(requests.electicity, requests.electicity, requests.naan_bill, bottles, requests.cold_drink_bill, requests.bbq_price, requests.diesel_ltr, requests.waiters_bill, stuff, dhobi, other_expenses, setup, decor_bill )


                total = pakwan + electicity + diesel + waiters + stuff + dhobi + other_expenses + setup + decor_bill

                requests.customer_name = customer_name
                requests.electicity = electicity
                # requests.naan_qty = naan
                # requests.naan_bill = naan_price
                # requests.cold_drink_type = drinks_type
                # requests.cold_drink = drinks
                # requests.cold_drink_bill = drink
                # requests.water_bottles_type = water_type
                # requests.water = water
                # requests.water_bill = bottles
                # requests.bbq_type = bbq_type
                # requests.bbq_kg_qty = bbqs
                # requests.bbq_price = bbq
                requests.diesel_ltr = diesel
                requests.no_of_waiters = no_of_waiters
                requests.waiters_bill = waiters
                requests.dhobi = dhobi
                requests.other_expense = other_expenses
                requests.other_expense_detals = expense_details
                requests.setup_bill = setup
                requests.decor = decor
                requests.stuff_bill = stuff
                requests.decor_bill = decor_bill
                requests.total_expense = total
            
                requests.save()
                messages.success(request, "Expense Updated Successfully.")
                return redirect('event-expense') 
        # except:
        #     messages.error(request, "Error While updating expense. Please check form submission again.")
        #     return redirect('event-expense') 

class Eventexpense(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/event-expense.html"
    login_url = "account/login"

    def get(self, request):
        expenses = EventExpense.objects.filter(bill__status='Confirm')  # Query confirmed expenses only
        events = EventSale.objects.filter(status='Confirm')  # Query confirmed events only

        # Pagination setup
        paginator = Paginator(expenses, 50)  # Display 50 records per page
        page_number = request.GET.get("page")
        page_obj = paginator.get_page(page_number)

        # Aggregate total expenses (only confirmed)
        total_expenses = EventExpense.objects.filter(bill__status='Confirm').aggregate(
            total_deisel=Sum('diesel_ltr'),
            total_electricity=Sum('electicity'),
            total_pakwan=Sum('pakwan_bill'),
            total_waiters_bill=Sum('waiters_bill'),
            total_stuff_bill=Sum('stuff_bill'),
            total_dhobi=Sum('dhobi'),
            total_other_expense=Sum('other_expense'),
            total_setup_bill=Sum('setup_bill'),
            total_decore_bill=Sum('decor_bill'),
            total_expense=Sum('total_expense')
        )

        # Extract values safely to avoid NoneType errors
        def get_value(field):
            return total_expenses.get(field) or 0  # Return 0 if None

        context = {
            "expenses": page_obj,  # Paginated expenses
            "events": events,
            "electricity": get_value('total_electricity'),
            "diesel": get_value('total_deisel'),
            "pakwan": get_value('total_pakwan'),
            "waiters": get_value('total_waiters_bill'),
            "stuff": get_value('total_stuff_bill'),
            "dhobi": get_value('total_dhobi'),
            "other_expenses": get_value('total_other_expense'),
            "setup": get_value('total_setup_bill'),
            "decor": get_value('total_decore_bill'),
            "total": get_value('total_expense'),
            "len_expense": expenses.count(),  # Count instead of len()
        }
        return render(request, self.template_name, context)

    @transaction.atomic
    def post(self, request):
        try: 
            bill = request.POST.get('bill-no')
            print(bill)
            bill_number = get_object_or_404(EventSale, pk=bill)
            
            customer_name = bill_number.customer_name
            pakwan = int(request.POST.get('pakwan-bill'))

            electicity = request.POST.get('electicity-bill')
            # naan =int(request.POST.get('naan-qty'))
            
        
            # drinks = int(request.POST.get('cold-drinks'))
            # drinks_type = request.POST.get('cold-drinks-type')


            # water = int(request.POST.get('water-bottles'))
            # water_type = request.POST.get('water-bottles-type')

            # bbqs = int(request.POST.get('bbq-qty'))
            # bbq_type = request.POST.get('bbq-type')

            diesel = request.POST.get('diesel-ltr')
            no_of_waiters = request.POST.get('no-of-waiters')
            dhobi = request.POST.get('dhobi')
            stuff = request.POST.get('stuff')

            other_expenses = request.POST.get('other-expense')

            expense_details = request.POST.get('expense-details')
           
            setup = request.POST.get('setup-bill')
            decor_details = request.POST.get('decore-details')
            decor_bill = request.POST.get('decor-bill')
    
            # try:
            # nan = MyProducts.objects.get(product_name='Naan')
            # naan_price = nan.price * naan




            # drink = 0
            # if drinks_type == 'Cold Drinks 1.5L':
            #     drink = MyProducts.objects.get(product_name='Cold_Drinks_1.5L')


            # elif drinks_type == "Cold Drinks Tin":
            #     drink = MyProducts.objects.get(product_name='Cold_Drinks_Tin')
                

            
            # elif drinks_type == "Cold Drinks 2.5L":
            #     drink = MyProducts.objects.get(product_name='Cold_Drinks_2.5L')
                    


            # else:
            #     drink = ''
                
            
            # if not drink == '' :
            #     drink = drink.price * drinks

            
            # bottles = 0
            # if water_type == 'Water 1.5L':
            #     bottles = MyProducts.objects.get(product_name='Water_1.5L')

            # elif water_type == "Water 500ML":
            #     bottles = MyProducts.objects.get(product_name='Water_500ML')

            # elif water_type == "Water 300ML":
            #     bottles = MyProducts.objects.get(product_name='Water_300ML')

            # else:
            #     bottles = ''
            
            # if not bottles == '' :
            #     bottles = bottles.price * water
            
            # bbqs = MyProducts.objects.get(product_name="BBQ")
            # bbq_price = bbq * bbqs.price

            # bbq = 0
            # if bbq_type == 'Reshmi Kabab':
            #     bbq = MyProducts.objects.get(product_name='Reshmi_Kabab')

            # elif bbq_type == "Chicken Achari Boti":
            #     bbq = MyProducts.objects.get(product_name='Chicken_Achari_Boti')

            # elif bbq_type == "Seekh Kabab":
            #     bbq = MyProducts.objects.get(product_name='Seekh_Kabab')

            # elif bbq_type == "Malai Boti":
            #     bbq = MyProducts.objects.get(product_name='Malai_Boti')

            # else:
            #     bbq = 0
            
            
            # if not bbq == 0 :
            #     bbq = bbqs * bbq.price

            wait = MyProducts.objects.get(product_name="Waiters")
            waiters = wait.price * int(no_of_waiters)

            pakwan = int(pakwan)
            # naan_price = int(naan_price)
            # drink = int(drink)
            # bottles = int(bottles)
            # bbq_price = int(bbq)
            electicity = int(electicity)
            diesel = int(diesel)
            waiters = int(waiters)
            stuff = int(stuff)
            dhobi = int(dhobi)
            other_expenses = int(other_expenses)
            setup = int(setup)
            decor_bill = int(decor_bill)
            


            total = pakwan + diesel + electicity + waiters + stuff + dhobi + other_expenses + setup + decor_bill
            if EventExpense.objects.filter(bill=bill_number).exists():
                    messages.error(request, "Sorry! This Expense already Exists")
                    return redirect('event-expense')

            
            else:

                add_event_expense = EventExpense.objects.create(
                        bill=bill_number,
                        customer_name = customer_name,
                        pakwan_bill=str(pakwan),
                        electicity = str(electicity),
                        # naan_qty =  str(naan),
                        # cold_drink= str(drinks),
                        # water = str(water),
                        # bbq_kg_qty=str(bbqs),
                        # naan_bill=str(naan_price),
                        # cold_drink_bill=str(drink),
                        # cold_drink_type = drinks_type,
                        # water_bill=str(bottles),
                        # water_bottles_type=water_type,
                        # bbq_price=str(bbq_price),
                        # bbq_type = bbq_type,
                        diesel_ltr=str(diesel),
                        no_of_waiters= str(no_of_waiters),
                        waiters_bill=str(waiters),
                        stuff_bill=str(stuff),
                        dhobi=str(dhobi),
                        other_expense = other_expenses,
                        other_expense_detals= expense_details,
                        setup_bill=str(setup),
                        decor= decor_details,
                        decor_bill=str(decor_bill),
                        total_expense = str(total)
                    )
                messages.success(request, "Expense Added Successfully")
                return redirect('event-expense')

        except:
            messages.error(request, "Invalid form! Please check form submission again.")
            return redirect('event-expense')
            
            
            
def DeleteExpense(request, expense_id):
        
    expense = get_object_or_404(EventExpense, pk=expense_id)
    try:
        if expense is not None:
            expense.delete()
            messages.success(request, "Expense Deleted Successfully")
            return redirect("event-expense")
    except:
        messages.error(request, "Error while deletion")
        return redirect("event-expense")
        
    expense = EventExpense.objects.all()
    
    context = {
        "expenses": expense,
    }
    return render(request, "ecommerce/event-expense.html", context = context)         
            
            
            

class ProductsAddCategory(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/category_list.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        category = Category.objects.all()

        context = {
            "category": category,

        }
        return render(request, self.template_name, context)
   
    def post(self, request):
        try:
            if request.method == "POST":
                
                cat_name = request.POST['categoryname']
                cat_dsc = request.POST['categorydesc']
                if Category.objects.filter(name=cat_name).exists():
                    messages.error(request, "That Category already Exists")
                    return redirect('ecommerce_add_category')
            
                else:

                    Category.objects.create(
                        name = cat_name,
                        description = cat_dsc,
                    )
            messages.success(request, "Category Added Successfully")
            return redirect('ecommerce_add_category')
        except:
            messages.error(request, "Invalid Form! please check submission again.")
            return redirect('ecommerce_add_category')

class UpdateCategory(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/category_list.html"
    LOGIN_URL = "account/login"  
    def post(self, request, category_id):
        try:
            if request.method == "POST":
                requests = Category.objects.get(id=category_id)

                cat_name = request.POST['categoryname']
                cat_dsc = request.POST['categorydesc']
                # print(cat_name, cat_dsc, requests.name, requests.discription)
                requests.name = cat_name
                requests.description = cat_dsc

                requests.save()
                messages.success(request, "Category Updated Successfully")
                return redirect("ecommerce_add_category")
        except:
            messages.error(request, "Error while updating Category")
            return redirect("ecommerce_add_category")


def DeleteCategory(request, category_id):
        
    category = get_object_or_404(Category, pk=category_id)
    try:
        if category is not None:
            category.delete()
            messages.success(request, "Category Deleted Successfully")
            return redirect("ecommerce_add_category")
    except:
        messages.error(request, "Error while deletion")
        return redirect("ecommerce_add_category")
        
    cat = Category.objects.all()
    
    context = {
        "category": cat,
    }
    return render(request, "ecommerce/category_list.html", context = context)


class ProductsCheckout(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/ecommerce-checkout.html"
    LOGIN_URL = "account/login"  
class ProductsShops(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/ecommerce-shops.html"
    LOGIN_URL = "account/login"  
class ProductsAddUnit(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/units_list.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        units = Unit.objects.all()

        context = {
            "units": units,
        }
        return render(request, self.template_name, context)
    def post(self, request):
        try:
            if request.method == "POST":
                name = request.POST.get('name')
                shortname = request.POST.get('shortname')
                unit = request.POST.get('unit')

                if Unit.objects.filter(name=name).exists():
                    messages.error(request, "That Unit already Exists")
                    return redirect('ecommerce_add_unit')
            
                else:

                    Unit.objects.create(
                        name= name,
                        short_name = shortname,
                        unit = unit,
                    )
            messages.success(request, "Unit added Successfully")
            return redirect('ecommerce_add_unit')
        except:
            messages.error(request, "Invalid Form Submission, Try again with correct data")
            return redirect('ecommerce_add_unit')


class UpdateUnit(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/units_list.html"
    LOGIN_URL = "account/login"  
    def post(self, request, unit_id):
        try:
            if request.method == "POST":
                requests = Unit.objects.get(id=unit_id)

                name = request.POST.get('name')
                shortname = request.POST.get('shortname')
                unit = request.POST.get('unit')

                requests.name = name
                requests.short_name = shortname
                requests.unit = unit

                requests.save()
                messages.success(request, "Unit Updated Successfully")
        except:
            messages.error(request, "Error while updating")
            return redirect('ecommerce_add_unit')



           
        return redirect('ecommerce_add_unit')

def DeleteUnit(request, unit_id):
        
    unit = get_object_or_404(Unit, pk=unit_id)
    try:
        if unit is not None:
            unit.delete()
            messages.success(request, "Unit Deleted Successfully")
            return redirect("ecommerce_add_unit")
    except:
        messages.error(request, "Error while deletion")
        return redirect("ecommerce_add_unit")
        
    unit = Unit.objects.all()
    
    context = {
        "units": unit,
    }
    return render(request, "ecommerce/product_list.html", context = context)

class ProductsAddBrand(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/brands_list.html"
    LOGIN_URL = "account/login"  
    def get(self, request):
        brand = Brand.objects.all()

        context = {
            "brands": brand
        }
        return render(request, self.template_name, context)

    def post(self, request):
        if request.method == "POST":
            name = request.POST.get('name')

            desc = request.POST.get('desc')

            if Brand.objects.filter(name=name).exists():
                messages.error(request, "That Brand already Exists")
                return redirect('ecommerce_add_brand')
            
            else:

                Brand.objects.create(
                    name= name,
                    desc = desc
                )
            
            messages.success(request, "Brand Successfully Added")
            return redirect('ecommerce_add_brand')
        return render(request, self.template_name)

class UpdateBrand(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/brands_list.html"
    LOGIN_URL = "account/login"  
    def post(self, request, brand_id):
        if request.method == "POST":
            requests = Brand.objects.get(id=brand_id)

            name = request.POST.get('name')
            desc = request.POST.get('desc')
            requests.name= name
            requests.desc = desc
            requests.save()
            messages.success(request, "Brand Successfully updated")
            return redirect('ecommerce_add_brand')
        return render(request, self.template_name)

def DeleteBrand(request, brand_id):
        
    brand = get_object_or_404(Brand, pk=brand_id)
    try:
        if brand is not None:
            brand.delete()
            messages.success(request, "Brand Deleted Successfully")
            return redirect("ecommerce_add_brand")
    except:
        messages.error(request, "Error while deletion")
        return redirect("ecommerce_add_brand")
        
    brand = Brand.objects.all()
    
    context = {
        "brands": brand,
    }
    return render(request, "ecommerce/brands_list.html", context = context)


class ProductsAddInventory(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/ecommerce-add-inventory.html"
    LOGIN_URL = "account/login"  
    def post(self, request):
        if request.method == "POST":

            product_id = request.POST.get('productname')
            product = get_object_or_404(MyProducts, pk=product_id)

            product_qty= request.POST.get('qty')

            previous_qty = product.qty
            qty = int(previous_qty) + int(product_qty)
            
            product.qty = qty
            product.save()

        return render(request, self.template_name)

    def get(self, request):
        products = MyProducts.objects.all()

        context = {
            "products": products
        }
        return render(request, self.template_name, context)

class ProductsAddProduct(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/ecommerce-add-product.html"
    LOGIN_URL = "account/login"  
    def post(self, request):
        try:
            if request.method == "POST":
                product_name = request.POST.get('productname')

                brand_id = request.POST.get('brand_id')
                brand = get_object_or_404(Brand, pk=brand_id)

                unit_id = request.POST.get('unit_id')
                unit = get_object_or_404(Unit, pk=unit_id)
                
            
                category_id = request.POST['category_id']
                category = get_object_or_404(Category, pk=category_id)

                # product_cost = request.POST.get('cost')
                product_price= request.POST.get('price')
                product_qty= request.POST.get('qty')
                productdesc = request.POST.get('productdesc')
                image = request.FILES.get('image')
                if MyProducts.objects.filter(product_name = product_name).exists():
                    messages.error(request, "Product Already Exists")
                    return redirect('ecommerce-product-list')
                else:
                    MyProducts.objects.create(
                        product_name= product_name,
                        brand = brand,
                        unit = unit,
                        category_id = category,
                        # cost= product_cost,
                        price = product_price,
                        qty=product_qty,
                        product_desc = productdesc,
                        product_image = image

                    )
                    messages.success(request, "Product Added Successfully")
                    return redirect('ecommerce-product-list')

        except:
            
            messages.error(request, "Invalid Form Submission,  Hint: Check for empty form fields, make sure to upload product image")
            return redirect('ecommerce-product-list')
        return render(request, 'ecommerce/product_list.html')
        

    def get(self, request):
        category = Category.objects.all()
        brand = Brand.objects.all()
        unit = Unit.objects.all()
        products = MyProducts.objects.all()
        
        context = {
            "category": category,
            "brands": brand,
            "units": unit,
            "products": products
        }
        return render(request, self.template_name, context)


class UpdateProducts(LoginRequiredMixin,TemplateView):
    template_name = "ecommerce/ecommerce-add-product.html"
    LOGIN_URL = "account/login"  
    def post(self, request,product_id):
        if request.method == "POST":

            requests = MyProducts.objects.get(id=product_id)

            product_name = request.POST.get('productname')

            brand_id = request.POST.get('brand_id')
            brand = get_object_or_404(Brand, pk=brand_id)

            unit_id = request.POST.get('unit_id')
            unit = get_object_or_404(Unit, pk=unit_id)
            
        
            category_id = request.POST['category_id']
            category = get_object_or_404(Category, pk=category_id)

            # product_cost = request.POST.get('cost')
            product_price= request.POST.get('price')
            product_qty= request.POST.get('qty')
            productdesc = request.POST.get('productdesc')
            image = request.FILES.get('image')

            requests.product_name = product_name
            requests.brand = brand
            requests.unit = unit
            requests.category_id = category
            requests.price = product_price
            requests.qty = product_qty
            requests.product_desc = productdesc
            
            if image is not None:
                requests.product_image = image


            requests.save()



        return redirect("ecommerce-product-list")
        

    def get(self, request):
        category = Category.objects.all()
        brand = Brand.objects.all()
        unit = Unit.objects.all()
        products = MyProducts.objects.all()
        
        context = {
            "category": category,
            "brands": brand,
            "units": unit,
            "products": products
        }
        return render(request, self.template_name, context)

class Calculate(LoginRequiredMixin,TemplateView):
    template_name = "items/pos.html"
    LOGIN_URL = "account/login"  
    def get(self, request):

        deal = request.GET.get('deals')
        no_people = request.GET.get("numberOfPeople")

        


        products = MyProducts.objects.all()
        product_json = []
        for product in products:
            product_json.append({'id': product.id, 'name': product.name, 'price': float(product.price)})
        
        deals_json = []
        deals_obj = Deals.objects.get(pk=1)
        menu_items = deals_obj.menu_items.all()

        for item in menu_items:
            deals_json.append({'id': item.id, 'name': item.name, 'price': float(item.price)})
        
        context = {
            "page_title": "Point of Sale",
            "products": products,
            "product_json": json.dumps(product_json),
            'deal_type': "custom", 
            "default_items": deals_json,
            "isCustomDeal": False,
            "deal_items": deals_json,
            "no_people": no_people
        }
        return render(request, self.template_name, context)


class DealsCalulator(LoginRequiredMixin,TemplateView):
    template_name = "items/deals-calculator.html"

    LOGIN_URL = "account/login"  


from django.http import JsonResponse
from items.models import RentalProduct, RentalTransaction
from django.utils import timezone
from datetime import datetime
from django.db.models import F

class RentalProductList(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_product_list.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        rental_products = RentalProduct.objects.all()
        category = Category.objects.all()
        brands = Brand.objects.all()
        
        context = {
            "rental_products": rental_products,
            "category": category,
            "brands": brands
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        try:
            product_name = request.POST.get('product_name')
            price_per_day = request.POST.get('price_per_day')
            quantity = request.POST.get('quantity')
            category_id = request.POST.get('category')
            brand_id = request.POST.get('brand')
            status = 'status' in request.POST
            
            # Create new rental product
            RentalProduct.objects.create(
                product_name=product_name,
                price_per_day=price_per_day,
                quantity_available=quantity,
                category_id=category_id if category_id else None,
                brand_id=brand_id if brand_id else None,
                status=status
            )
            
            messages.success(request, "Rental product added successfully")
            return redirect('rental_product_list')
        except Exception as e:
            messages.error(request, f"Error adding rental product: {str(e)}")
            return redirect('rental_product_list')

def add_rental_product(request):
    if request.method == "POST":
        try:
            product_name = request.POST.get('product_name')
            price_per_day = request.POST.get('price_per_day')
            quantity = request.POST.get('quantity')
            category_id = request.POST.get('category')
            brand_id = request.POST.get('brand')
            status = 'status' in request.POST
            
            # Create new rental product
            RentalProduct.objects.create(
                product_name=product_name,
                price_per_day=price_per_day,
                quantity_available=quantity,
                category_id=category_id if category_id else None,
                brand_id=brand_id if brand_id else None,
                status=status
            )
            
            messages.success(request, "Rental product added successfully")
        except Exception as e:
            messages.error(request, f"Error adding rental product: {str(e)}")
    
    return redirect('rental_product_list')

def update_rental_product(request, product_id):
    if request.method == "POST":
        try:
            product = RentalProduct.objects.get(id=product_id)
            
            product.product_name = request.POST.get('product_name')
            product.price_per_day = request.POST.get('price_per_day')
            product.quantity_available = request.POST.get('quantity')
            
            category_id = request.POST.get('category')
            if category_id:
                product.category_id = category_id
                
            brand_id = request.POST.get('brand')
            if brand_id:
                product.brand_id = brand_id
                
            product.status = 'status' in request.POST
            
            product.save()
            
            messages.success(request, "Rental product updated successfully")
        except RentalProduct.DoesNotExist:
            messages.error(request, "Product not found")
        except Exception as e:
            messages.error(request, f"Error updating rental product: {str(e)}")
    
    return redirect('rental_product_list')

def delete_rental_product(request, product_id):
    try:
        product = RentalProduct.objects.get(id=product_id)
        product.delete()
        messages.success(request, "Rental product deleted successfully")
    except RentalProduct.DoesNotExist:
        messages.error(request, "Product not found")
    except Exception as e:
        messages.error(request, f"Error deleting rental product: {str(e)}")
    
    return redirect('rental_product_list')

class RentalIssue(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_issue.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        rental_products = RentalProduct.objects.filter(status=True, quantity_available__gt=0)
        
        context = {
            "rental_products": rental_products
        }
        return render(request, self.template_name, context)
        
    def post(self, request):
        try:
            # Get common form data
            customer_name = request.POST.get('customer_name')
            customer_contact = request.POST.get('customer_contact')
            # Skip customer_address if not in model
            rental_date = request.POST.get('rental_date')
            expected_return_date = request.POST.get('expected_return_date')
            deposit_amount = float(request.POST.get('deposit_amount', 0))
            # Skip payment_method if not in model
            notes = request.POST.get('notes', '')
            
            # Get product data (multiple products)
            product_ids = request.POST.getlist('products[]')
            quantities = request.POST.getlist('quantities[]')
            
            if not product_ids:
                messages.error(request, "Product not found")
                return redirect('rental_issue')
            
            # Create rental transactions for each product
            first_transaction = None
            
            for product_id, quantity in zip(product_ids, quantities):
                product = RentalProduct.objects.get(id=product_id)
                if product.quantity_available >= int(quantity):
                    transaction = RentalTransaction.objects.create(
                        product=product,
                        quantity=int(quantity),
                        price_per_day=product.price_per_day,
                        customer_name=customer_name,
                        customer_contact=customer_contact,
                        rental_date=rental_date,
                        expected_return_date=expected_return_date,
                        deposit_amount=deposit_amount if not first_transaction else 0,  # Only add deposit to first transaction
                        notes=notes,
                        status='Active'
                    )
                    
                    if not first_transaction:
                        first_transaction = transaction
                    
                    product.quantity_available -= int(quantity)
                    product.save()
                else:
                    messages.error(request, f"Insufficient stock for {product.product_name}")
                    return redirect('rental_issue')
            
            messages.success(request, "Rental issued successfully")
            
            # Redirect to the invoice page for the first transaction
            if first_transaction:
                return redirect('rental_thermal_invoice', rental_id=first_transaction.id)
            return redirect('rental_issue')
        except Exception as e:
            messages.error(request, f"Error issuing rental: {str(e)}")
            return redirect('rental_issue')

class RentalThermalInvoice(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_thermal_invoice.html"

from django.http import JsonResponse

def debug_rental_data(request):
    """A simple view to debug rental data"""
    all_rentals = RentalTransaction.objects.all()
    
    data = {
        'total_rentals': all_rentals.count(),
        'active_rentals': RentalTransaction.objects.filter(status='Active').count(),
        'returned_rentals': RentalTransaction.objects.filter(status='Returned').count(),
        'sample_rentals': list(all_rentals.values('id', 'customer_name', 'rental_date', 'status')[:5])
    }
    
    return JsonResponse(data)
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all related rentals (same customer, same rental date)
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                expected_return_date=rental.expected_return_date,
                status='Active'
            )
            
            # Calculate rental days
            rental_date = rental.rental_date
            expected_return_date = rental.expected_return_date
            rental_days = max((expected_return_date - rental_date).days, 1)  # Minimum 1 day
            
            # Calculate total rental amount across all related rentals
            total_amount = Decimal('0')
            for r in related_rentals:
                price_per_day = Decimal(str(r.price_per_day)) if r.price_per_day else Decimal('0')
                quantity = Decimal(str(r.quantity)) if r.quantity else Decimal('0')
                r_amount = Decimal(rental_days) * price_per_day * quantity
                total_amount += r_amount
            
            # Calculate total paid (deposit + any other payments)
            total_paid = Decimal(str(rental.deposit_amount)) if rental.deposit_amount else Decimal('0')
            
            context = {
                "rental": rental,
                "related_rentals": related_rentals,
                "rental_days": rental_days,
                "total_amount": total_amount,
                "total_paid": total_paid,
                "invoice_date": datetime.now().date(),
                "invoice_number": f"INV-{rental.id}-{int(time.time())}"
            }
            
            return render(request, self.template_name, context)
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')

class RentalReturnThermalInvoice(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_return_thermal_invoice.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all related rentals (same customer, same rental date)
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                expected_return_date=rental.expected_return_date,
                status='Returned',
                actual_return_date=rental.actual_return_date
            )
            
            if not related_rentals.exists():
                # If no related rentals found, include at least the current rental
                related_rentals = [rental]
            
            # Calculate rental days
            rental_date = rental.rental_date
            actual_return_date = rental.actual_return_date
            rental_days = max((actual_return_date - rental_date).days, 1)  # Minimum 1 day
            
            # Calculate total rental amount
            total_rental_amount = Decimal('0')
            total_damage_charges = Decimal('0')
            total_missing_charges = Decimal('0')
            
            for r in related_rentals:
                # Calculate rental amount for this item
                price_per_day = Decimal(str(r.price_per_day))
                quantity = Decimal(str(r.quantity))
                r_amount = Decimal(rental_days) * price_per_day * quantity
                total_rental_amount += r_amount
                
                # Add damage and missing charges
                if r.damage_charges:
                    total_damage_charges += Decimal(str(r.damage_charges))
                
                if r.missing_items and r.price_per_day:
                    missing_items = Decimal(str(r.missing_items)) if r.missing_items else Decimal('0')
                    missing_charge = missing_items * price_per_day * Decimal('5')  # 5x daily rate for missing items
                    total_missing_charges += missing_charge
            
            # Calculate deposit refund
            deposit_amount = Decimal(str(rental.deposit_amount)) if rental.deposit_amount else Decimal('0')
            total_charges = total_rental_amount + total_damage_charges + total_missing_charges
            deposit_refund = max(deposit_amount - total_damage_charges - total_missing_charges, Decimal('0'))
            
            context = {
                "rental": rental,
                "related_rentals": related_rentals,
                "rental_days": rental_days,
                "total_rental_amount": total_rental_amount,
                "rental_amount": total_rental_amount,
                "total_damage_charges": total_damage_charges,
                "damage_charges": total_damage_charges,
                "total_missing_charges": total_missing_charges,
                "missing_charges": total_missing_charges,
                "deposit_refund": deposit_refund,
                "total_amount": total_charges,
                "invoice_date": datetime.now().date(),
                "invoice_number": f"RTN-{rental.id}-{int(time.time())}"
            }
            
            return render(request, self.template_name, context)
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')

class RentalReturn(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_return.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id=None):
        # Get all active rentals
        active_rentals = RentalTransaction.objects.filter(status='Active')
        
        # Group rentals by customer and rental date
        grouped_rentals = {}
        for rental in active_rentals:
            key = (rental.customer_name, rental.customer_contact, rental.rental_date, rental.expected_return_date)
            if key not in grouped_rentals:
                grouped_rentals[key] = {
                    'id': rental.id,  # Use first rental's ID as reference
                    'customer_name': rental.customer_name,
                    'customer_contact': rental.customer_contact,
                    'rental_date': rental.rental_date,
                    'expected_return_date': rental.expected_return_date,
                    'products': []
                }
            
            grouped_rentals[key]['products'].append({
                'id': rental.id,
                'product_name': rental.product.product_name,
                'quantity': rental.quantity
            })
        
        rental = None
        related_rentals = []
        
        if rental_id:
            try:
                rental = RentalTransaction.objects.get(id=rental_id, status='Active')
                # Get all related rentals (same customer, same rental date)
                related_rentals = RentalTransaction.objects.filter(
                    customer_name=rental.customer_name,
                    customer_contact=rental.customer_contact,
                    rental_date=rental.rental_date,
                    expected_return_date=rental.expected_return_date,
                    status='Active'
                )
            except RentalTransaction.DoesNotExist:
                messages.error(request, "Rental not found or already returned")
                return redirect('active_rentals_list')
        
        context = {
            "grouped_rentals": grouped_rentals,
            "rental": rental,
            "related_rentals": related_rentals
        }
        return render(request, self.template_name, context)
    
    def post(self, request, rental_id):
        try:
            # Get the reference rental
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all related rentals (same customer, same rental date)
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                expected_return_date=rental.expected_return_date,
                status='Active'
            ).order_by('id')
            
            if not related_rentals.exists():
                messages.error(request, "No active rentals found for this invoice")
                return redirect('active_rentals_list')
            
            # Process the return for all items
            actual_return_date = request.POST.get('actual_return_date')
            condition_notes = request.POST.get('condition_notes', '')
            
            # Calculate total rental amount
            total_rental_amount = Decimal('0')
            total_damage_charges = Decimal('0')
            total_missing_charges = Decimal('0')
            
            # Store the ID of the first rental
            first_rental_id = related_rentals.first().id if related_rentals.exists() else None
            
            # Process each rental item
            for item in related_rentals:
                if item is None:
                    continue  # Skip if item is None
                
                # Get item-specific data
                returned_qty = int(request.POST.get(f'returned_qty_{item.id}', 0))
                damaged_qty = int(request.POST.get(f'damaged_qty_{item.id}', 0))
                missing_qty = int(request.POST.get(f'missing_qty_{item.id}', 0))
                damage_charges = Decimal(request.POST.get(f'damage_charges_{item.id}', '0'))
                missing_charges = Decimal(request.POST.get(f'missing_charges_{item.id}', '0'))
                
                # Calculate rental days
                rental_date = item.rental_date
                return_date = datetime.strptime(actual_return_date, '%Y-%m-%d').date()
                days_rented = max((return_date - rental_date).days, 1)
                
                # Calculate item rental amount
                item_rental_amount = Decimal(days_rented) * Decimal(str(item.price_per_day)) * Decimal(item.quantity)
                total_rental_amount += item_rental_amount
                
                # Add to total charges
                total_damage_charges += damage_charges
                total_missing_charges += missing_charges
                
                # Update the rental status
                item.status = 'Returned'
                item.actual_return_date = actual_return_date
                item.return_notes = condition_notes
                
                # Set item-specific data
                if hasattr(item, 'damage_charges'):
                    item.damage_charges = damage_charges
                
                if hasattr(item, 'missing_charges'):
                    item.missing_charges = missing_charges
                
                if hasattr(item, 'missing_items'):
                    item.missing_items = missing_qty
                
                # Calculate total amount for this item
                item_total = item_rental_amount + damage_charges + missing_charges
                if hasattr(item, 'total_amount'):
                    item.total_amount = item_total
                
                # Update product inventory if product exists
                product = item.product
                if product is not None:
                    # Only add back the returned and damaged items (not missing)
                    product.quantity_available += returned_qty + damaged_qty
                    product.save()
                
                # Save the rental
                item.save()
            
            # Get the first rental again after processing
            try:
                first_rental = RentalTransaction.objects.get(id=first_rental_id)
                messages.success(request, "All rental items returned successfully")
                
                # Redirect to the thermal return invoice
                return redirect('rental_return_thermal_invoice', rental_id=first_rental.id)
            except RentalTransaction.DoesNotExist:
                messages.error(request, "Error processing return: Could not find the primary rental")
                return redirect('active_rentals_list')
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')
        except Exception as e:
            messages.error(request, f"Error processing return: {str(e)}")
            return redirect('active_rentals_list')

class ActiveRentalsList(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/active_rentals_list.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get all active rentals
        active_rentals = RentalTransaction.objects.filter(status='Active')
        
        # Group rentals by customer and rental date
        grouped_rentals = {}
        for rental in active_rentals:
            key = (rental.customer_name, rental.customer_contact, rental.rental_date, rental.expected_return_date)
            if key not in grouped_rentals:
                grouped_rentals[key] = {
                    'id': rental.id,  # Use first rental's ID as reference
                    'customer_name': rental.customer_name,
                    'customer_contact': rental.customer_contact,
                    'rental_date': rental.rental_date,
                    'expected_return_date': rental.expected_return_date,
                    'products': []
                }
            
            grouped_rentals[key]['products'].append({
                'id': rental.id,
                'product_name': rental.product.product_name,
                'quantity': rental.quantity
            })
        
        context = {
            "active_rentals": list(grouped_rentals.values())
        }
        return render(request, self.template_name, context)

@method_decorator(admin_required, name='dispatch')
class ActiveRentalsReport(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/active_rentals_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get all active rentals
        active_rentals_list = RentalTransaction.objects.filter(status='Active').order_by('expected_return_date')
        
        # Pagination
        page = request.GET.get('page', 1)
        paginator = Paginator(active_rentals_list, 10)  # Show 10 rentals per page
        
        try:
            active_rentals = paginator.page(page)
        except PageNotAnInteger:
            active_rentals = paginator.page(1)
        except EmptyPage:
            active_rentals = paginator.page(paginator.num_pages)
        
        context = {
            "active_rentals": active_rentals,
            "report_date": datetime.now().date()
        }
        return render(request, self.template_name, context)

@method_decorator(admin_required, name='dispatch')
class RentalHistoryReport(LoginRequiredMixin, View):
    template_name = "ecommerce/rental/rental_history_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get filter parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        customer = request.GET.get('customer')
        status = request.GET.get('status')
        
        # Base query - get all rentals
        rentals = RentalTransaction.objects.all().order_by('-rental_date')
        
        # Apply filters if provided
        if start_date:
            rentals = rentals.filter(rental_date__gte=start_date)
        
        if end_date:
            rentals = rentals.filter(rental_date__lte=end_date)
        
        if customer:
            rentals = rentals.filter(customer_name=customer)
        
        if status:
            rentals = rentals.filter(status=status)
        
        # Debug: Print the query and count to console
        print(f"Query: {rentals.query}")
        print(f"Count: {rentals.count()}")
        
        # Group rentals by invoice (customer, rental date, expected return date)
        grouped_rentals = {}
        for rental in rentals:
            key = (rental.customer_name, rental.customer_contact, rental.rental_date, rental.expected_return_date, rental.status)
            
            # For returned rentals, also include actual return date in the key
            if rental.status == 'Returned' and rental.actual_return_date:
                key = key + (rental.actual_return_date,)
            
            if key not in grouped_rentals:
                # Create a new invoice entry
                invoice_id = rental.id  # Use the first rental's ID as the invoice ID
                grouped_rentals[key] = {
                    'id': invoice_id,
                    'invoice_number': f"INV-{invoice_id}",
                    'customer_name': rental.customer_name,
                    'customer_contact': rental.customer_contact,
                    'rental_date': rental.rental_date,
                    'expected_return_date': rental.expected_return_date,
                    'actual_return_date': rental.actual_return_date,
                    'status': rental.status,
                    'total_items': 0,
                    'total_amount': 0
                }
            
            # Add this rental's quantity to the total items
            grouped_rentals[key]['total_items'] += rental.quantity
            
            # Calculate and add this rental's amount to the total
            if rental.status == 'Returned' and rental.actual_return_date:
                rental_days = max((rental.actual_return_date - rental.rental_date).days, 1)
            else:
                rental_days = max((rental.expected_return_date - rental.rental_date).days, 1)
            
            # Ensure price_per_day is a number
            price_per_day = float(rental.price_per_day) if rental.price_per_day else 0
            rental_amount = rental_days * price_per_day * rental.quantity
            
            # Add damage and missing charges for returned items
            if rental.status == 'Returned':
                rental_amount += float(rental.damage_charges or 0)
                rental_amount += float(rental.missing_charges or 0)
            
            grouped_rentals[key]['total_amount'] += rental_amount
        
        # Convert to list and sort by rental date (newest first)
        grouped_rentals = list(grouped_rentals.values())
        grouped_rentals.sort(key=lambda x: x['rental_date'], reverse=True)
        
        # Debug: Print the grouped rentals
        print(f"Grouped rentals count: {len(grouped_rentals)}")
        
        # Calculate grand total
        total_amount = sum(invoice['total_amount'] for invoice in grouped_rentals)
        
        # Get unique customers for filter dropdown
        customers = RentalTransaction.objects.values_list('customer_name', flat=True).distinct()
        
        context = {
            "grouped_rentals": grouped_rentals,
            "total_amount": total_amount,
            "customers": customers,
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "customer": customer,
                "status": status
            }
        }
        
        return render(request, self.template_name, context)

class ReturnedRentalsList(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/returned_rentals_list.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get all returned rentals
        all_returned_rentals = RentalTransaction.objects.filter(status='Returned').order_by('-actual_return_date')
        
        # Group rentals by customer, rental date, and return date
        grouped_rentals = {}
        for rental in all_returned_rentals:
            key = (rental.customer_name, rental.rental_date, rental.actual_return_date)
            if key not in grouped_rentals:
                # Use the first rental as the representative for this group
                grouped_rentals[key] = rental
        
        # Convert the dictionary values to a list
        returned_rentals = list(grouped_rentals.values())
        
        context = {
            "returned_rentals": returned_rentals
        }
        return render(request, self.template_name, context)
class RentalDetail(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_detail.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            # Get the reference rental
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all related rentals (same customer, same rental date)
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                expected_return_date=rental.expected_return_date,
                status=rental.status
            )
            
            # Calculate rental days and totals
            total_rental_amount = 0
            total_damage_charges = 0
            total_missing_charges = 0
            
            if rental.status == 'Returned' and rental.actual_return_date:
                rental_days = max((rental.actual_return_date - rental.rental_date).days, 1)
            else:
                rental_days = max((rental.expected_return_date - rental.rental_date).days, 1)
            
            # Calculate totals for all related rentals
            for item in related_rentals:
                # Convert to Decimal to avoid float/Decimal mixing
                price_per_day = Decimal(str(item.price_per_day))
                quantity = Decimal(str(item.quantity))
                
                # Calculate rental amount for this item
                item_rental_amount = price_per_day * quantity * Decimal(str(rental_days))
                total_rental_amount += item_rental_amount
                
                # Add damage and missing charges
                damage_charges = Decimal(str(item.damage_charges)) if hasattr(item, 'damage_charges') and item.damage_charges else Decimal('0')
                missing_charges = Decimal(str(item.missing_charges)) if hasattr(item, 'missing_charges') and item.missing_charges else Decimal('0')
                
                total_damage_charges += damage_charges
                total_missing_charges += missing_charges
            
            context = {
                "rental": rental,
                "related_rentals": related_rentals,
                "rental_days": rental_days,
                "total_rental_amount": total_rental_amount,
                "total_damage_charges": total_damage_charges,
                "total_missing_charges": total_missing_charges,
                "total_amount": total_rental_amount + total_damage_charges + total_missing_charges
            }
            return render(request, self.template_name, context)
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('returned_rentals_list')

class RentalProductEdit(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_product_edit.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, product_id):
        try:
            product = RentalProduct.objects.get(id=product_id)
            category = Category.objects.all()
            brand = Brand.objects.all()
            
            context = {
                "product": product,
                "brands": brand,
                "category": category,
            }
            return render(request, self.template_name, context)
        except RentalProduct.DoesNotExist:
            messages.error(request, "Product not found")
            return redirect('rental_product_list')
    
    def post(self, request, product_id):
        try:
            product = RentalProduct.objects.get(id=product_id)
            
            product.product_name = request.POST.get('product_name')
            product.price_per_day = request.POST.get('price_per_day')
            product.quantity_available = request.POST.get('quantity')
            
            category_id = request.POST.get('category')
            if category_id:
                product.category_id = category_id
                
            brand_id = request.POST.get('brand')
            if brand_id:
                product.brand_id = brand_id
                
            product.status = 'status' in request.POST
            
            product.save()
            
            messages.success(request, "Rental product updated successfully")
            return redirect('rental_product_list')
        except RentalProduct.DoesNotExist:
            messages.error(request, "Product not found")
            return redirect('rental_product_list')
        except Exception as e:
            messages.error(request, f"Error updating rental product: {str(e)}")
            return redirect('rental_product_list')

def delete_rental_product(request, product_id):
    try:
        product = RentalProduct.objects.get(id=product_id)
        product.delete()
        messages.success(request, "Rental product deleted successfully")
    except RentalProduct.DoesNotExist:
        messages.error(request, "Product not found")
    except Exception as e:
        messages.error(request, f"Error deleting rental product: {str(e)}")
    
    return redirect('rental_product_list')

class UpdateFoodMenu(LoginRequiredMixin, View):
    def get(self, request, pk):
        try:
            eventsale = get_object_or_404(EventSale, id=pk)
            food_menu = [eventsale.food_menu]
            
            # Parse the food menu to get product information
            cleaned_data = food_menu[0].strip().rstrip(', ')
            items = cleaned_data.split(', ')
            
            # Initialize product data dictionary
            product_data = {}
            for item in items:
                if ' (' in item and item.endswith(')'):
                    # Find the last occurrence of ' (' to get the quantity
                    last_paren_index = item.rfind(' (')
                    if last_paren_index != -1:
                        name = item[:last_paren_index].strip()
                        quantity_str = item[last_paren_index + 2:].rstrip(')')  # +2 to skip ' ('
                        try:
                            quantity = int(quantity_str.strip())
                            product_data[name] = quantity
                        except ValueError:
                            # If quantity is not a valid integer, skip this item
                            continue
            
            # Get product prices
            product_prices = {}
            for product_name, quantity in product_data.items():
                # Try exact match first
                product = MyProducts.objects.filter(product_name=product_name).first()
                
                # If not found, try with underscores replaced with spaces
                if not product:
                    product_name_with_spaces = product_name.replace('_', ' ')
                    product = MyProducts.objects.filter(product_name=product_name_with_spaces).first()
                
                # If still not found, try with case-insensitive search
                if not product:
                    product = MyProducts.objects.filter(product_name__iexact=product_name).first()
                
                # If still not found, try with case-insensitive search with spaces
                if not product:
                    product = MyProducts.objects.filter(product_name__iexact=product_name_with_spaces).first()
                
                if product:
                    price = product.price
                    product_prices[product_name] = {
                        "name": product_name,
                        "quantity": quantity,
                        "price": price
                    }
                else:
                    # If product not found, set a default price of 0
                    product_prices[product_name] = {
                        "name": product_name,
                        "quantity": quantity,
                        "price": 0
                    }
            
            # Convert to JSON
            json_product_prices = json.dumps(product_prices)
            
            # Get all products for the form
            products = MyProducts.objects.filter(status=1)
            product_json = []
            for product in products:
                product_json.append({'id': product.id, 'name': product.product_name, 'price': float(product.price)})
            
            # Get amounts from the event sale - Fix the data structure
            ammounts = [
                eventsale.no_of_people,
                eventsale.hall_charges,
                eventsale.per_head
            ]
            
            # Create product data for JavaScript with numeric IDs
            product_data_for_js = {}
            for product_name, data in product_prices.items():
                # Try to find the product by name to get its ID
                product = MyProducts.objects.filter(product_name=product_name).first()
                if product:
                    product_data_for_js[str(product.id)] = {
                        'id': product.id,
                        'name': product_name,
                        'price': float(data['price']) if data['price'] else 0,
                        'quantity': data['quantity']
            }
            
            context = {
                'products': products,
                'product_json': json.dumps(product_data_for_js),
                'food_menu': json.dumps(food_menu),
                'myproducts': json.dumps(product_json),
                'ammounts': json.dumps(ammounts),
                'pk': pk
            }
            
            return render(request, 'items/update_deals.html', context)
        except Exception as e:
            messages.error(request, f"Error loading food menu: {str(e)}")
            return redirect('event-sale')
    
    def post(self, request, pk):
        try:
            # Log the incoming request for debugging
            print(f"UpdateFoodMenu POST - PK: {pk}")
            print(f"Request body: {request.body}")
            
            # Process and store data as needed
            data = json.loads(request.body)
            print(f"Parsed data: {data}")
            
            eventsale = EventSale.objects.get(id=pk) 
            print(f"Found EventSale: {eventsale.id}")
            
            # Safely get existing charges with defaults
            extra_charge = getattr(eventsale, 'extra_charges', 0) or 0
            entry_charge = getattr(eventsale, 'entry_charges', 0) or 0
            stage_charges = getattr(eventsale, 'stage_charges', 0) or 0
            
            print(f"Existing charges - Extra: {extra_charge}, Entry: {entry_charge}, Stage: {stage_charges}")
            
            # Safely extract data with validation
            if "menu" not in data:
                return JsonResponse({'error': 'Menu data is required'}, status=400)
            if "values" not in data:
                return JsonResponse({'error': 'Values data is required'}, status=400)
                
            menu = data["menu"]
            values = data["values"]
            
            # Validate required fields in values
            required_fields = ["subTotal", "numberOfPeople", "hallCharges", "menuAmount"]
            for field in required_fields:
                if field not in values:
                    return JsonResponse({'error': f'Missing required field: {field}'}, status=400)
            
            subTotal = values["subTotal"]
            numberOfPeople = values["numberOfPeople"]
            hallCharges = values["hallCharges"]
            menuAmount = values["menuAmount"]
            
            print(f"Extracted values - Menu: {menu[:50]}..., SubTotal: {subTotal}, People: {numberOfPeople}, Hall: {hallCharges}, MenuAmount: {menuAmount}")
            
            # Update event sale fields
            eventsale.food_menu = menu
            eventsale.no_of_people = numberOfPeople
            
            # Convert values to appropriate types with error handling
            try:
                hall_charges_int = int(float(hallCharges)) if hallCharges else 0
                menu_amount_int = int(float(menuAmount)) if menuAmount else 0
                people_count = int(float(numberOfPeople)) if numberOfPeople else 0
                
                per_head = hall_charges_int + menu_amount_int
                eventsale.per_head = per_head
                
                # Calculate total amount
                base_amount = people_count * per_head
                additional_charges = int(float(extra_charge)) + int(float(stage_charges)) + int(float(entry_charge))
                eventsale.total_amount = base_amount + additional_charges
                
                eventsale.hall_charges = hall_charges_int
                
                # Handle received_amount safely
                received_amount = getattr(eventsale, 'recieved_amount', 0) or getattr(eventsale, 'received_amount', 0) or 0
                eventsale.remaining_amount = eventsale.total_amount - received_amount
                
                print(f"Calculated values - Per head: {per_head}, Total: {eventsale.total_amount}, Remaining: {eventsale.remaining_amount}")
                
            except (ValueError, TypeError) as e:
                return JsonResponse({'error': f'Invalid numeric value: {str(e)}'}, status=400)
            
            eventsale.save()
            print("EventSale saved successfully")
            
            messages.success(request, "Event Menu updated successfully")
            # Return JSON response for AJAX request
            return JsonResponse({'success': True, 'message': 'Event Menu updated successfully'})
            
        except json.JSONDecodeError as e:
            error_msg = f'Invalid JSON data: {str(e)}'
            print(f"JSON Error: {error_msg}")
            return JsonResponse({'error': error_msg}, status=400)
        except EventSale.DoesNotExist:
            error_msg = f'Event sale with ID {pk} not found'
            print(f"EventSale Error: {error_msg}")
            return JsonResponse({'error': error_msg}, status=404)
        except Exception as e:
            error_msg = f'Server error: {str(e)}'
            print(f"Unexpected Error: {error_msg}")
            print(f"Error type: {type(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
            return JsonResponse({'error': error_msg}, status=500)
        

def delete_rental(request, rental_id):
    try:
        # Get the rental object
        rental = get_object_or_404(RentalTransaction, id=rental_id)
        
        # If the rental is active, return the items to inventory
        if rental.status == 'Active':
            product = rental.product
            if product is not None:
                product.quantity_available += rental.quantity
                product.save()
        
        # Delete the rental
        rental.delete()
        
        # Add success message
        messages.success(request, "Rental deleted successfully")
    except RentalTransaction.DoesNotExist:
        messages.error(request, "Rental not found")
    except Exception as e:
        messages.error(request, f"Error deleting rental: {str(e)}")
    
    # Redirect based on the referrer
    if 'returned' in request.META.get('HTTP_REFERER', ''):
        return redirect('returned_rentals_list')
    else:
        return redirect('active_rentals_list')

class ProductQuantitySummaryList(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/finance_reports/Product_Report/quantity_summary.html"
    LOGIN_URL = "account/login/"

    def get(self, request):
        # Fetch unique products from EventSale - Only confirmed sales
        event_sale_products = []
        for sale in EventSale.objects.filter(status='Confirm'):
            if sale.food_menu:  # Ensure food_menu is not empty
                products = [product.strip() for product in sale.food_menu.split(',')]
                event_sale_products.extend(
                    {"name": product, "source": "EventSale"} for product in products
                )

        # Fetch unique products from EventExpense - Only from confirmed events
        expense_fields = ['cold_drink_type', 'water_bottles_type', 'bbq_type', 'decor']
        event_expense_products = [
            {"name": product, "source": "EventExpense"}
            for field in expense_fields
            for product in EventExpense.objects.filter(bill__status='Confirm').values_list(field, flat=True).distinct()
            if product  # Filter out empty or null products
        ]

        # Combine both lists
        combined_products = event_sale_products + event_expense_products
        # Remove duplicates and sort by product name
        unique_products = {f"{p['name']}:{p['source']}": p for p in combined_products}
        sorted_products = sorted(unique_products.values(), key=lambda x: x["name"])

        context = {
            "products": sorted_products,
        }
        return render(request, self.template_name, context)


@method_decorator(admin_required, name='dispatch')
class OverdueRentalsReport(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/overdue_rentals_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get current date
        today = datetime.now().date()
        
        # Get all active rentals that are past their expected return date
        overdue_rentals = RentalTransaction.objects.filter(
            status='Active',
            expected_return_date__lt=today
        ).order_by('expected_return_date')
        
        # Group rentals by invoice (customer, rental date, expected return date)
        grouped_rentals = {}
        for rental in overdue_rentals:
            key = (rental.customer_name, rental.customer_contact, rental.rental_date, rental.expected_return_date)
            
            if key not in grouped_rentals:
                # Calculate days overdue
                days_overdue = (today - rental.expected_return_date).days
                
                # Create a new invoice entry
                invoice_id = rental.id  # Use the first rental's ID as the invoice ID
                grouped_rentals[key] = {
                    'id': invoice_id,
                    'invoice_number': f"INV-{invoice_id}",
                    'customer_name': rental.customer_name,
                    'customer_contact': rental.customer_contact,
                    'rental_date': rental.rental_date,
                    'expected_return_date': rental.expected_return_date,
                    'days_overdue': days_overdue,
                    'total_items': 0,
                    'total_amount': 0,
                    'penalty_amount': 0
                }
            
            # Add this rental's quantity to the total items
            grouped_rentals[key]['total_items'] += rental.quantity
            
            # Calculate rental amount
            rental_days = max((rental.expected_return_date - rental.rental_date).days, 1)
            rental_amount = rental_days * float(rental.price_per_day) * rental.quantity
            grouped_rentals[key]['total_amount'] += rental_amount
            
            # Calculate penalty (e.g., 10% of daily rate per day overdue)
            days_overdue = grouped_rentals[key]['days_overdue']
            daily_penalty = float(rental.price_per_day) * 0.10 * rental.quantity  # 10% of daily rate
            penalty_amount = days_overdue * daily_penalty
            grouped_rentals[key]['penalty_amount'] += penalty_amount
        
        # Convert to list and sort by days overdue (highest first)
        grouped_rentals = list(grouped_rentals.values())
        grouped_rentals.sort(key=lambda x: x['days_overdue'], reverse=True)
        
        # Calculate grand totals
        total_items = sum(invoice['total_items'] for invoice in grouped_rentals)
        total_amount = sum(invoice['total_amount'] for invoice in grouped_rentals)
        total_penalty = sum(invoice['penalty_amount'] for invoice in grouped_rentals)
        
        context = {
            "grouped_rentals": grouped_rentals,
            "total_items": total_items,
            "total_amount": total_amount,
            "total_penalty": total_penalty,
            "report_date": today
        }
        
        return render(request, self.template_name, context)

from django.db.models import Count, Sum, F, ExpressionWrapper, DecimalField
from django.db.models.functions import TruncMonth

@method_decorator(admin_required, name='dispatch')
class FrequentRentersReport(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/frequent_renters_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get filter parameters
        period = request.GET.get('period', 'all')  # all, year, 6months, 3months
        
        # Base query
        rentals = RentalTransaction.objects.all()
        
        # Apply time period filter
        today = datetime.now().date()
        if period == 'year':
            one_year_ago = today - timedelta(days=365)
            rentals = rentals.filter(rental_date__gte=one_year_ago)
        elif period == '6months':
            six_months_ago = today - timedelta(days=180)
            rentals = rentals.filter(rental_date__gte=six_months_ago)
        elif period == '3months':
            three_months_ago = today - timedelta(days=90)
            rentals = rentals.filter(rental_date__gte=three_months_ago)
        
        # Group by customer and count rentals
        customer_stats = (
            rentals.values('customer_name', 'customer_contact')
            .annotate(
                rental_count=Count('id', distinct=True),
                total_items=Sum('quantity'),
                total_spent=Sum(
                    ExpressionWrapper(
                        F('price_per_day') * F('quantity'),
                        output_field=DecimalField()
                    )
                )
            )
            .order_by('-rental_count')
        )
        
        # Get monthly rental trends for top customers
        top_customers = [stat['customer_name'] for stat in customer_stats[:10]]
        
        monthly_trends = {}
        if top_customers:
            # Get data for the last 12 months
            twelve_months_ago = today - timedelta(days=365)
            
            # Get monthly rental counts for top customers
            monthly_data = (
                rentals.filter(
                    customer_name__in=top_customers,
                    rental_date__gte=twelve_months_ago
                )
                .annotate(month=TruncMonth('rental_date'))
                .values('month', 'customer_name')
                .annotate(count=Count('id'))
                .order_by('month', 'customer_name')
            )
            
            # Organize data by customer and month
            for entry in monthly_data:
                customer = entry['customer_name']
                month = entry['month'].strftime('%b %Y')
                count = entry['count']
                
                if customer not in monthly_trends:
                    monthly_trends[customer] = {}
                
                monthly_trends[customer][month] = count
        
        context = {
            "customer_stats": customer_stats,
            "monthly_trends": monthly_trends,
            "period": period,
            "report_date": today
        }
        
        return render(request, self.template_name, context)

class ProductMonthlySummary(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/finance_reports/Product_Report/Monthly_report.html"
    LOGIN_URL = "account/login/"

    def get(self, request, product_name, month):
        # Map the month name to its integer value
        month_map = {
            'January': 1,
            'February': 2,
            'March': 3,
            'April': 4,
            'May': 5,
            'June': 6,
            'July': 7,
            'August': 8,
            'September': 9,
            'October': 10,
            'November': 11,
            'December': 12
        }
        month_int = month_map.get(month, None)

        # Filter event sales by food_menu (contains the product_name) and month - Only confirmed sales
        event_sales = EventSale.objects.filter(food_menu__icontains=product_name, event_date__month=month_int, status='Confirm')

        # Prepare context for rendering
        context = {
            "product_name": product_name,
            "event_sales": event_sales,
            "selected_month": month,
        }

        return render(request, self.template_name, context)
    
    
@method_decorator(admin_required, name='dispatch')
class ReturnedItemsReport(LoginRequiredMixin, View):
    template_name = "ecommerce/rental/returned_items_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get filter parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        customer = request.GET.get('customer')
        return_status = request.GET.get('return_status', 'all')  # all, on_time, late
        
        # Base query - get all returned rentals
        returned_items = RentalTransaction.objects.filter(status='Returned').order_by('-actual_return_date')
        
        # Apply filters if provided
        if start_date:
            returned_items = returned_items.filter(actual_return_date__gte=start_date)
        
        if end_date:
            returned_items = returned_items.filter(actual_return_date__lte=end_date)
        
        if customer:
            returned_items = returned_items.filter(customer_name=customer)
        
        # Process items to determine if they were returned on time or late
        items_data = []
        for item in returned_items:
            # Skip items without actual return date
            if not item.actual_return_date:
                continue
                
            # Determine if returned on time or late
            is_late = item.actual_return_date > item.expected_return_date
            return_status_text = "Late" if is_late else "On Time"
            
            # Skip if filtering by return status
            if return_status == 'on_time' and is_late:
                continue
            if return_status == 'late' and not is_late:
                continue
            
            # Determine condition based on damage charges
            has_damage = item.damage_charges and float(item.damage_charges) > 0
            condition = "Damaged" if has_damage else "Good"
            
            # Calculate days late (if applicable)
            days_late = 0
            if is_late:
                days_late = (item.actual_return_date - item.expected_return_date).days
            
            # Add to items data - use return_notes if it exists, otherwise use notes
            return_notes = ""
            if hasattr(item, 'return_notes') and item.return_notes:
                return_notes = item.return_notes
            elif hasattr(item, 'notes') and item.notes:
                return_notes = item.notes
                
            items_data.append({
                'id': item.id,
                'product_name': item.product.product_name if hasattr(item, 'product') else "Unknown Product",
                'customer_name': item.customer_name,
                'customer_contact': item.customer_contact,
                'rental_date': item.rental_date,
                'expected_return_date': item.expected_return_date,
                'actual_return_date': item.actual_return_date,
                'quantity': item.quantity,
                'return_status': return_status_text,
                'days_late': days_late,
                'condition': condition,
                'damage_charges': float(item.damage_charges or 0),
                'return_notes': return_notes
            })
        
        # Get unique customers for filter dropdown
        customers = RentalTransaction.objects.filter(status='Returned').values_list('customer_name', flat=True).distinct()
        
        # Calculate totals
        total_items = sum(item['quantity'] for item in items_data)
        total_on_time = sum(item['quantity'] for item in items_data if item['return_status'] == 'On Time')
        total_late = sum(item['quantity'] for item in items_data if item['return_status'] == 'Late')
        total_good_condition = sum(item['quantity'] for item in items_data if item['condition'] == 'Good')
        total_damaged = sum(item['quantity'] for item in items_data if item['condition'] == 'Damaged')
        total_damage_charges = sum(item['damage_charges'] for item in items_data)
        
        context = {
            "items_data": items_data,
            "customers": customers,
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "customer": customer,
                "return_status": return_status
            },
            "totals": {
                "total_items": total_items,
                "total_on_time": total_on_time,
                "total_late": total_late,
                "total_good_condition": total_good_condition,
                "total_damaged": total_damaged,
                "total_damage_charges": total_damage_charges
            }
        }
        
        return render(request, self.template_name, context)
    
    
@method_decorator(admin_required, name='dispatch')
class DamagedItemsReport(LoginRequiredMixin, View):
    template_name = "ecommerce/rental/damaged_items_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get filter parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        customer = request.GET.get('customer')
        
        # Base query - get all returned rentals with damage charges
        damaged_items = RentalTransaction.objects.filter(
            status='Returned',
            damage_charges__gt=0
        ).order_by('-actual_return_date')
        
        # Apply filters if provided
        if start_date:
            damaged_items = damaged_items.filter(actual_return_date__gte=start_date)
        
        if end_date:
            damaged_items = damaged_items.filter(actual_return_date__lte=end_date)
        
        if customer:
            damaged_items = damaged_items.filter(customer_name=customer)
        
        # Process items to add additional information
        items_data = []
        for item in damaged_items:
            # Skip items without actual return date
            if not item.actual_return_date:
                continue
                
            # Get notes - use return_notes if it exists, otherwise use notes
            return_notes = ""
            if hasattr(item, 'return_notes') and item.return_notes:
                return_notes = item.return_notes
            elif hasattr(item, 'notes') and item.notes:
                return_notes = item.notes
                
            # Add to items data
            items_data.append({
                'id': item.id,
                'product_name': item.product.product_name if hasattr(item, 'product') else "Unknown Product",
                'customer_name': item.customer_name,
                'customer_contact': item.customer_contact,
                'rental_date': item.rental_date,
                'return_date': item.actual_return_date,
                'quantity': item.quantity,
                'damaged_quantity': getattr(item, 'damaged_items', 1),  # Default to 1 if not specified
                'damage_charges': float(item.damage_charges or 0),
                'responsible_party': 'Customer',  # Default to customer, could be extended with a field in the model
                'return_notes': return_notes
            })
        
        # Get unique customers for filter dropdown
        customers = RentalTransaction.objects.filter(
            status='Returned',
            damage_charges__gt=0
        ).values_list('customer_name', flat=True).distinct()
        
        # Calculate totals
        total_items = sum(item['damaged_quantity'] for item in items_data)
        total_damage_charges = sum(item['damage_charges'] for item in items_data)
        
        context = {
            "items_data": items_data,
            "customers": customers,
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "customer": customer
            },
            "totals": {
                "total_items": total_items,
                "total_damage_charges": total_damage_charges
            }
        }
        
        return render(request, self.template_name, context)
    
@method_decorator(admin_required, name='dispatch')
class RentalIncomeReport(LoginRequiredMixin, View):
    template_name = "ecommerce/rental/rental_income_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get filter parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        group_by = request.GET.get('group_by', 'month')  # month, product, customer
        
        # Base query - get all rentals
        rentals = RentalTransaction.objects.all().order_by('-rental_date')
        
        # Apply date filters if provided
        if start_date:
            rentals = rentals.filter(rental_date__gte=start_date)
        
        if end_date:
            rentals = rentals.filter(rental_date__lte=end_date)
        
        # Initialize data structures for different grouping options
        monthly_income = {}
        product_income = {}
        customer_income = {}
        
        # Process rentals to calculate income
        for rental in rentals:
            # Calculate rental amount
            rental_amount = float(rental.total_amount or 0)
            
            # Skip if no amount
            if rental_amount <= 0:
                continue
                
            # Group by month
            month_key = rental.rental_date.strftime('%Y-%m')
            month_name = rental.rental_date.strftime('%B %Y')
            if month_key not in monthly_income:
                monthly_income[month_key] = {
                    'month_name': month_name,
                    'total_amount': 0,
                    'rental_count': 0
                }
            monthly_income[month_key]['total_amount'] += rental_amount
            monthly_income[month_key]['rental_count'] += 1
            
            # Group by product
            product_name = rental.product.product_name if hasattr(rental, 'product') else "Unknown Product"
            if product_name not in product_income:
                product_income[product_name] = {
                    'total_amount': 0,
                    'rental_count': 0
                }
            product_income[product_name]['total_amount'] += rental_amount
            product_income[product_name]['rental_count'] += 1
            
            # Group by customer
            customer_name = rental.customer_name or "Unknown Customer"
            if customer_name not in customer_income:
                customer_income[customer_name] = {
                    'total_amount': 0,
                    'rental_count': 0,
                    'contact': rental.customer_contact or ""
                }
            customer_income[customer_name]['total_amount'] += rental_amount
            customer_income[customer_name]['rental_count'] += 1
        
        # Convert dictionaries to sorted lists
        monthly_data = sorted(monthly_income.values(), key=lambda x: x['month_name'])
        product_data = sorted(
            [{'product_name': k, **v} for k, v in product_income.items()],
            key=lambda x: x['total_amount'],
            reverse=True
        )
        customer_data = sorted(
            [{'customer_name': k, **v} for k, v in customer_income.items()],
            key=lambda x: x['total_amount'],
            reverse=True
        )
        
        # Calculate grand totals
        grand_total = sum(item['total_amount'] for item in monthly_data)
        rental_count = sum(item['rental_count'] for item in monthly_data)
        
        # Determine which data to display based on grouping
        display_data = monthly_data
        if group_by == 'product':
            display_data = product_data
        elif group_by == 'customer':
            display_data = customer_data
        
        context = {
            "monthly_data": monthly_data,
            "product_data": product_data,
            "customer_data": customer_data,
            "display_data": display_data,
            "group_by": group_by,
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "group_by": group_by
            },
            "totals": {
                "grand_total": grand_total,
                "rental_count": rental_count
            }
        }
        
        return render(request, self.template_name, context)
    
    
@method_decorator(admin_required, name='dispatch')
class DamageMissingChargesReport(LoginRequiredMixin, View):
    template_name = "ecommerce/rental/damage_missing_charges_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get filter parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        charge_type = request.GET.get('charge_type', 'all')  # all, damage, missing
        
        # Base query - get all returned rentals with damage or missing charges
        charges_query = RentalTransaction.objects.filter(
            status='Returned'
        ).filter(
            Q(damage_charges__gt=0) | Q(missing_charges__gt=0)
        ).order_by('-actual_return_date')
        
        # Apply filters if provided
        if start_date:
            charges_query = charges_query.filter(actual_return_date__gte=start_date)
        
        if end_date:
            charges_query = charges_query.filter(actual_return_date__lte=end_date)
        
        if charge_type == 'damage':
            charges_query = charges_query.filter(damage_charges__gt=0)
        elif charge_type == 'missing':
            charges_query = charges_query.filter(missing_charges__gt=0)
        
        # Process items to add additional information
        charges_data = []
        for item in charges_query:
            # Skip items without actual return date
            if not item.actual_return_date:
                continue
                
            damage_charges = float(item.damage_charges or 0)
            missing_charges = float(item.missing_charges or 0)
            
            # Skip if no charges based on filter
            if charge_type == 'damage' and damage_charges <= 0:
                continue
            if charge_type == 'missing' and missing_charges <= 0:
                continue
                
            # Get notes - use return_notes if it exists, otherwise use notes
            return_notes = ""
            if hasattr(item, 'return_notes') and item.return_notes:
                return_notes = item.return_notes
            elif hasattr(item, 'notes') and item.notes:
                return_notes = item.notes
                
            # Add to charges data
            charges_data.append({
                'id': item.id,
                'product_name': item.product.product_name if hasattr(item, 'product') else "Unknown Product",
                'customer_name': item.customer_name,
                'customer_contact': item.customer_contact,
                'rental_date': item.rental_date,
                'return_date': item.actual_return_date,
                'damage_charges': damage_charges,
                'missing_charges': missing_charges,
                'total_charges': damage_charges + missing_charges,
                'return_notes': return_notes
            })
        
        # Calculate totals
        total_damage_charges = sum(item['damage_charges'] for item in charges_data)
        total_missing_charges = sum(item['missing_charges'] for item in charges_data)
        total_charges = total_damage_charges + total_missing_charges
        
        context = {
            "charges_data": charges_data,
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "charge_type": charge_type
            },
            "totals": {
                "total_damage_charges": total_damage_charges,
                "total_missing_charges": total_missing_charges,
                "total_charges": total_charges
            }
        }
        
        return render(request, self.template_name, context)
    
    
class Calendar(LoginRequiredMixin, TemplateView):
    template_name = "calendar.html"
    LOGIN_URL = "account/login"  
    
    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)

    def get(self, request):
        event_list = []
        
        # Only get confirmed events
        sale = EventSale.objects.filter(status='Confirm')
        
        serialized_event = serialize('json', sale)
        data = json.loads(serialized_event)

        for i in data:
            event_details = {
                'id': i['pk'],
                'bill_no': i['fields']['bill_no'],
                'customer_name': i['fields']['customer_name'],
                'event_date': i['fields']['event_date'],
            }
            event_list.append(event_details)
        
        event_json = json.dumps(event_list)
        
        return render(request, 'calendar.html', {'serialized_events': event_json})

class UpcomingEventsView(LoginRequiredMixin, TemplateView):
    template_name = 'ecommerce/upcoming_events.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        current_date = timezone.now().date()
        end_date = current_date + timedelta(days=5)
        
        upcoming_events = EventSale.objects.filter(
            event_date__range=[current_date, end_date],
            status='Confirm'
        ).order_by('event_date')
        
        urgent_events = upcoming_events.filter(
            event_date__range=[current_date, current_date + timedelta(days=2)]
        )
        
        context.update({
            'upcoming_events': upcoming_events,
            'urgent_events': urgent_events,
        })
        return context

class EventDetailView(LoginRequiredMixin, DetailView):
    model = EventSale
    template_name = 'ecommerce/event_detail.html'
    context_object_name = 'event'

class TentativeEventsView(LoginRequiredMixin, TemplateView):
    template_name = 'ecommerce/tentative_events.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tentative_events = EventSale.objects.filter(
            status__in=['Tentative ', 'Tentative']  # Handle both formats
        ).order_by('event_date')
        context['tentative_events'] = tentative_events
        return context
    
    
    
class EventSaleDetailView(DetailView):
    model = EventSale
    template_name = 'extras/sale-invoice.html'  # Adjust the path as necessary
    context_object_name = 'event_sale'

    def get_object(self):
        event_id = self.kwargs['event_id']  # Get the event ID from the URL
        print("Event ID:", event_id)  # Print the event ID to the console
        return get_object_or_404(EventSale, pk=event_id)  # Retrieve the EventSale object or return a 404

def update_rental(request, rental_id):
    if request.method == "POST":
        try:
            rental = RentalTransaction.objects.get(id=rental_id, status='Active')
            
            # Get original quantity to adjust product availability
            original_quantity = rental.quantity
            new_quantity = int(request.POST.get('quantity'))
            
            # Update rental details
            rental.customer_name = request.POST.get('customer_name')
            rental.customer_contact = request.POST.get('customer_contact')
            rental.rental_date = request.POST.get('rental_date')
            rental.expected_return_date = request.POST.get('expected_return_date')
            rental.deposit_amount = request.POST.get('deposit_amount')
            rental.notes = request.POST.get('notes')
            
            # Handle quantity change
            if original_quantity != new_quantity:
                # Update product availability
                product = rental.product
                # Add back original quantity
                product.quantity_available += original_quantity
                # Subtract new quantity
                if product.quantity_available >= new_quantity:
                    product.quantity_available -= new_quantity
                    rental.quantity = new_quantity
                    product.save()
                else:
                    messages.error(request, "Not enough items available for rent")
                    return redirect('active_rentals_list')
            
            rental.save()
            messages.success(request, "Rental updated successfully")
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found or already returned")
        except Exception as e:
            messages.error(request, f"Error updating rental: {str(e)}")
    
    return redirect('active_rentals_list')

def update_returned_rental(request, rental_id):
    if request.method == "POST":
        try:
            rental = RentalTransaction.objects.get(id=rental_id, status='Returned')
            
            # Update rental details
            rental.customer_name = request.POST.get('customer_name')
            rental.customer_contact = request.POST.get('customer_contact')
            rental.rental_date = request.POST.get('rental_date')
            rental.actual_return_date = request.POST.get('actual_return_date')
            rental.deposit_amount = request.POST.get('deposit_amount')
            rental.damage_charges = request.POST.get('damage_charges')
            rental.total_amount = request.POST.get('total_amount')
            rental.notes = request.POST.get('notes')
            
            rental.save()
            messages.success(request, "Returned rental updated successfully")
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
        except Exception as e:
            messages.error(request, f"Error updating rental: {str(e)}")
    
    return redirect('returned_rentals_list')

def delete_rental(request, rental_id):
    try:
        rental = RentalTransaction.objects.get(id=rental_id)
        
        # If the rental is active, return the items to inventory
        if rental.status == 'Active':
            product = rental.product
            product.quantity_available += rental.quantity
            product.save()
        
        rental.delete()
        messages.success(request, "Rental deleted successfully")
    except RentalTransaction.DoesNotExist:
        messages.error(request, "Rental not found")
    except Exception as e:
        messages.error(request, f"Error deleting rental: {str(e)}")
    
    # Redirect based on the referrer
    if 'returned' in request.META.get('HTTP_REFERER', ''):
        return redirect('returned_rentals_list')
    else:
        return redirect('active_rentals_list')

class RentalInvoice(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_invoice.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Calculate rental days
            rental_date = rental.rental_date
            if rental.status == 'Returned' and rental.actual_return_date:
                return_date = rental.actual_return_date
            else:
                return_date = rental.expected_return_date
                
            days_rented = max((return_date - rental_date).days, 1)  # Minimum 1 day
            
            # Calculate total amount
            rental_amount = Decimal(days_rented) * Decimal(str(rental.price_per_day)) * Decimal(rental.quantity)
            
            if rental.status == 'Returned':
                total_amount = Decimal(str(rental.total_amount)) if hasattr(rental, 'total_amount') else rental_amount
                damage_charges = Decimal(str(rental.damage_charges)) if hasattr(rental, 'damage_charges') else Decimal('0')
                missing_charges = Decimal(str(rental.missing_charges)) if hasattr(rental, 'missing_charges') else Decimal('0')
            else:
                total_amount = rental_amount
                damage_charges = Decimal('0')
                missing_charges = Decimal('0')
            
            context = {
                "rental": rental,
                "days_rented": days_rented,
                "rental_amount": rental_amount,
                "damage_charges": damage_charges,
                "missing_charges": missing_charges,
                "total_amount": total_amount,
                "invoice_date": datetime.now().date(),
                "invoice_number": f"INV-{rental.id}-{int(time.time())}"
            }
            
            return render(request, self.template_name, context)
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')

class RentalThermalInvoice(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_thermal_invoice.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all related rentals (same customer, same rental date)
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                expected_return_date=rental.expected_return_date,
                status='Active'
            )
            
            # Calculate rental days
            rental_date = rental.rental_date
            expected_return_date = rental.expected_return_date
            rental_days = max((expected_return_date - rental_date).days, 1)  # Minimum 1 day
            
            # Calculate total rental amount across all related rentals
            total_amount = Decimal('0')
            for r in related_rentals:
                price_per_day = Decimal(str(r.price_per_day)) if r.price_per_day else Decimal('0')
                quantity = Decimal(str(r.quantity)) if r.quantity else Decimal('0')
                r_amount = Decimal(rental_days) * price_per_day * quantity
                total_amount += r_amount
            
            # Calculate total paid (deposit + any other payments)
            total_paid = Decimal(str(rental.deposit_amount)) if rental.deposit_amount else Decimal('0')
            
            context = {
                "rental": rental,
                "related_rentals": related_rentals,
                "rental_days": rental_days,
                "total_amount": total_amount,
                "total_paid": total_paid,
                "invoice_date": datetime.now().date(),
                "invoice_number": f"INV-{rental.id}-{int(time.time())}"
            }
            
            return render(request, self.template_name, context)
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')

class RentalIssueInvoice(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_issue_invoice.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all rentals for the same customer with the same rental date
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                status='Active'
            )
            
            # Calculate rental days
            rental_date = rental.rental_date
            expected_return_date = rental.expected_return_date
            rental_days = max((expected_return_date - rental_date).days, 1)  # Minimum 1 day
            
            # Calculate total rental amount across all related rentals
            total_amount = 0
            for r in related_rentals:
                r_amount = rental_days * float(r.price_per_day) * r.quantity
                total_amount += r_amount
            
            context = {
                "rental": rental,
                "related_rentals": related_rentals,
                "rental_days": rental_days,
                "total_amount": total_amount,
                "invoice_date": datetime.now().date(),
                "invoice_number": f"INV-{rental.id}-{int(time.time())}"
            }
            
            return render(request, self.template_name, context)
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')

class RentalReturnInvoice(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/rental_return_invoice.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            rental = RentalTransaction.objects.get(id=rental_id)
            
            if rental.status != 'Returned':
                messages.error(request, "This rental has not been returned yet")
                return redirect('active_rentals_list')
            
            # Calculate rental days
            rental_date = rental.rental_date
            actual_return_date = rental.actual_return_date
            days_rented = max((actual_return_date - rental_date).days, 1)  # Minimum 1 day
            
            # Convert values to Decimal to avoid float/Decimal mixing
            rental_amount = Decimal(days_rented) * Decimal(str(rental.price_per_day)) * Decimal(rental.quantity)
            damage_charges = Decimal(str(rental.damage_charges)) if hasattr(rental, 'damage_charges') else Decimal('0')
            missing_charges = Decimal(str(rental.missing_charges)) if hasattr(rental, 'missing_charges') else Decimal('0')
            
            # Calculate deposit refund
            deposit_amount = Decimal(str(rental.deposit_amount)) if rental.deposit_amount else Decimal('0')
            total_charges = rental_amount + damage_charges + missing_charges
            deposit_refund = max(deposit_amount - damage_charges - missing_charges, Decimal('0'))
            
            context = {
                "rental": rental,
                "days_rented": days_rented,
                "rental_amount": rental_amount,
                "damage_charges": damage_charges,
                "missing_charges": missing_charges,
                "deposit_refund": deposit_refund,
                "total_amount": total_charges,
                "invoice_date": datetime.now().date(),
                "invoice_number": f"RTN-{rental.id}-{int(time.time())}"
            }
            
            return render(request, self.template_name, context)
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('returned_rentals_list')

class ReturnAllRental(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/rental/return_all_rental.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request, rental_id):
        try:
            # Get the reference rental
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all related rentals (same customer, same rental date)
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                expected_return_date=rental.expected_return_date,
                status='Active'
            )
            
            if not related_rentals.exists():
                messages.error(request, "No active rentals found for this invoice")
                return redirect('active_rentals_list')
            
            context = {
                "rental": rental,
                "related_rentals": related_rentals,
                "total_items": related_rentals.count()
            }
            return render(request, self.template_name, context)
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')
    
    def post(self, request, rental_id):
        try:
            # Get the reference rental
            rental = RentalTransaction.objects.get(id=rental_id)
            
            # Get all related rentals (same customer, same rental date)
            related_rentals = RentalTransaction.objects.filter(
                customer_name=rental.customer_name,
                customer_contact=rental.customer_contact,
                rental_date=rental.rental_date,
                expected_return_date=rental.expected_return_date,
                status='Active'
            ).order_by('id')
            
            if not related_rentals.exists():
                messages.error(request, "No active rentals found for this invoice")
                return redirect('active_rentals_list')
            
            # Process the return for all items
            actual_return_date = request.POST.get('actual_return_date')
            condition_notes = request.POST.get('condition_notes', '')
            
            # Calculate total rental amount
            total_rental_amount = Decimal('0')
            total_damage_charges = Decimal('0')
            total_missing_charges = Decimal('0')
            
            # Store the ID of the first rental
            first_rental_id = related_rentals.first().id if related_rentals.exists() else None
            
            # Process each rental item
            for item in related_rentals:
                if item is None:
                    continue  # Skip if item is None
                
                # Get item-specific data
                returned_qty = int(request.POST.get(f'returned_qty_{item.id}', 0))
                damaged_qty = int(request.POST.get(f'damaged_qty_{item.id}', 0))
                missing_qty = int(request.POST.get(f'missing_qty_{item.id}', 0))
                damage_charges = Decimal(request.POST.get(f'damage_charges_{item.id}', '0'))
                missing_charges = Decimal(request.POST.get(f'missing_charges_{item.id}', '0'))
                
                # Calculate rental days
                rental_date = item.rental_date
                return_date = datetime.strptime(actual_return_date, '%Y-%m-%d').date()
                days_rented = max((return_date - rental_date).days, 1)
                
                # Calculate item rental amount
                item_rental_amount = Decimal(days_rented) * Decimal(str(item.price_per_day)) * Decimal(item.quantity)
                total_rental_amount += item_rental_amount
                
                # Add to total charges
                total_damage_charges += damage_charges
                total_missing_charges += missing_charges
                
                # Update the rental status
                item.status = 'Returned'
                item.actual_return_date = actual_return_date
                item.return_notes = condition_notes
                
                # Set item-specific data
                if hasattr(item, 'damage_charges'):
                    item.damage_charges = damage_charges
                
                if hasattr(item, 'missing_charges'):
                    item.missing_charges = missing_charges
                
                if hasattr(item, 'missing_items'):
                    item.missing_items = missing_qty
                
                # Calculate total amount for this item
                item_total = item_rental_amount + damage_charges + missing_charges
                if hasattr(item, 'total_amount'):
                    item.total_amount = item_total
                
                # Update product inventory if product exists
                product = item.product
                if product is not None:
                    # Only add back the returned and damaged items (not missing)
                    product.quantity_available += returned_qty + damaged_qty
                    product.save()
                
                # Save the rental
                item.save()
            
            # Get the first rental again after processing
            try:
                first_rental = RentalTransaction.objects.get(id=first_rental_id)
                messages.success(request, "All rental items returned successfully")
                
                # Redirect to the thermal return invoice
                return redirect('rental_return_thermal_invoice', rental_id=first_rental.id)
            except RentalTransaction.DoesNotExist:
                messages.error(request, "Error processing return: Could not find the primary rental")
                return redirect('active_rentals_list')
            
        except RentalTransaction.DoesNotExist:
            messages.error(request, "Rental not found")
            return redirect('active_rentals_list')
        except Exception as e:
            messages.error(request, f"Error processing return: {str(e)}")
            return redirect('active_rentals_list')

@method_decorator(admin_required, name='dispatch')
class MissingItemsReport(LoginRequiredMixin, View):
    template_name = "ecommerce/rental/missing_items_report.html"
    LOGIN_URL = "account/login/"
    
    def get(self, request):
        # Get filter parameters
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        customer = request.GET.get('customer')
        
        # Base query - get all returned rentals with missing items
        missing_items = RentalTransaction.objects.filter(
            status='Returned',
            missing_items__gt=0
        ).order_by('-actual_return_date')
        
        # Apply filters if provided
        if start_date:
            missing_items = missing_items.filter(actual_return_date__gte=start_date)
        
        if end_date:
            missing_items = missing_items.filter(actual_return_date__lte=end_date)
        
        if customer:
            missing_items = missing_items.filter(customer_name=customer)
        
        # Process items to add additional information
        items_data = []
        for item in missing_items:
            # Skip items without actual return date
            if not item.actual_return_date:
                continue
                
            # Calculate the value of missing items
            missing_quantity = int(item.missing_items or 0)
            price_per_item = float(item.price_per_day or 0)
            missing_value = missing_quantity * price_per_item * 10  # Assuming 10x daily rate as replacement value
            
            # Get notes - use return_notes if it exists, otherwise use notes
            return_notes = ""
            if hasattr(item, 'return_notes') and item.return_notes:
                return_notes = item.return_notes
            elif hasattr(item, 'notes') and item.notes:
                return_notes = item.notes
                
            # Add to items data
            items_data.append({
                'id': item.id,
                'product_name': item.product.product_name if hasattr(item, 'product') else "Unknown Product",
                'customer_name': item.customer_name,
                'customer_contact': item.customer_contact,
                'rental_date': item.rental_date,
                'return_date': item.actual_return_date,
                'rented_quantity': item.quantity,
                'missing_quantity': missing_quantity,
                'price_per_item': price_per_item,
                'missing_value': missing_value,
                'missing_charges': float(item.missing_charges or 0),
                'return_notes': return_notes
            })
        
        # Get unique customers for filter dropdown
        customers = RentalTransaction.objects.filter(
            status='Returned',
            missing_items__gt=0
        ).values_list('customer_name', flat=True).distinct()
        
        # Calculate totals
        total_missing_items = sum(item['missing_quantity'] for item in items_data)
        total_missing_value = sum(item['missing_value'] for item in items_data)
        total_missing_charges = sum(item['missing_charges'] for item in items_data)
        
        context = {
            "items_data": items_data,
            "customers": customers,
            "filters": {
                "start_date": start_date,
                "end_date": end_date,
                "customer": customer
            },
            "totals": {
                "total_missing_items": total_missing_items,
                "total_missing_value": total_missing_value,
                "total_missing_charges": total_missing_charges
            }
        }
        
        return render(request, self.template_name, context)

class Index(LoginRequiredMixin, TemplateView):
    template_name = "index.html"
    
    def get(self, request):
        # ... existing code ...
        
        # Get upcoming events (next 5 days)
        current_date = timezone.now().date()
        end_date = current_date + timedelta(days=5)
        
        upcoming_events = EventSale.objects.filter(
            event_date__range=[current_date, end_date],
            status='Confirm'
        ).order_by('event_date')
        
        # Get urgent events (1-2 days)
        urgent_events = upcoming_events.filter(
            event_date__range=[current_date, current_date + timedelta(days=2)]
        )
        
        # Filter confirmed events for reports
        total_sale = EventSale.objects.filter(
            event_date__year=selected_year, 
            status='Confirm'
        ).aggregate(total_sales=Sum('total_amount'))['total_sales'] or 0
        
        # Update context
        context.update({
            'upcoming_events': upcoming_events,
            'urgent_events': urgent_events,
            'has_urgent_events': urgent_events.exists(),
            'total_sales': total_sale,
        })
        
        return render(request, self.template_name, context)

# Add admin_required decorator to report views
@method_decorator(admin_required, name='dispatch')
class SalesReport(LoginRequiredMixin, TemplateView):
    template_name = "ecommerce/sales_report.html"
    
    def get(self, request):
        # Only include confirmed events
        sales = EventSale.objects.filter(status='Confirm')
        context = {'sales': sales}
        return render(request, self.template_name, context)

class PublicCalendar(TemplateView):
    """Public calendar view that doesn't require login"""
    template_name = "ecommerce/public_calendar.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['current_month'] = timezone.now().strftime('%B %Y')
        return context

def calendar_availability(request):
    """API endpoint for calendar availability data"""
    
    # Get query parameters
    date_param = request.GET.get('date')
    month_param = request.GET.get('month')
    
    try:
        if date_param:
            # Get availability for a specific date
            selected_date = datetime.strptime(date_param, '%Y-%m-%d').date()
            venues_data = get_date_availability(selected_date)
            
            return JsonResponse({
                'success': True,
                'venues': venues_data,
                'date': date_param
            })
            
        elif month_param:
            # Get availability overview for a month
            year, month = map(int, month_param.split('-'))
            availability_data = get_month_availability(year, month)
            
            return JsonResponse({
                'success': True,
                'availability': availability_data,
                'month': month_param
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=400)
    
    return JsonResponse({
        'success': False,
        'error': 'Invalid parameters'
    }, status=400)

def get_date_availability(selected_date):
    """Get venue availability for a specific date"""
    
    # Define venue time slots (matching the model choices exactly)
    venue_slots = {
        'Hall1': ['Day', 'Night '],  # Fixed: Night should have trailing space
        'Hall2': ['Day', 'Night '],  # Fixed: Night should have trailing space
        'Lawn': ['Day', 'Night '],   # Fixed: Night should have trailing space
        'Business_Center': ['Day', 'Night '],  # Fixed: Night should have trailing space
    }
    
    # Get events for the selected date (confirmed and tentative events)
    confirmed_events = EventSale.objects.filter(
        event_date=selected_date,
        status='Confirm'
    )
    
    tentative_events = EventSale.objects.filter(
        event_date=selected_date,
        status='Tentative '  # Note the trailing space
    )
    
    # Debug: Print events found
    print(f"DEBUG: Looking for events on {selected_date}")
    print(f"DEBUG: Found {confirmed_events.count()} confirmed events")
    print(f"DEBUG: Found {tentative_events.count()} tentative events")
    for event in confirmed_events:
        print(f"DEBUG: Confirmed Event - Location: {event.location}, Timing: '{event.event_timing}', Customer: {event.customer_name}")
    for event in tentative_events:
        print(f"DEBUG: Tentative Event - Location: {event.location}, Timing: '{event.event_timing}', Customer: {event.customer_name}")
    
    # Create availability data
    venues_data = []
    
    for venue_name, time_slots in venue_slots.items():
        venue_data = {
            'name': venue_name,
            'time_slots': []
        }
        
        for time_slot in time_slots:
            # Check if this venue and time slot is booked or tentative
            # Search for both formats to handle legacy data
            
            if time_slot == 'Day':
                is_confirmed = confirmed_events.filter(
                    location=venue_name,
                    event_timing='Day'
                ).exists()
                is_tentative = tentative_events.filter(
                    location=venue_name,
                    event_timing='Day'
                ).exists()
            elif time_slot == 'Night ':
                # Check both formats for compatibility with legacy data
                is_confirmed = confirmed_events.filter(
                    location=venue_name,
                    event_timing__in=['Night ', 'Night']  # Fixed: Check both formats
                ).exists()
                is_tentative = tentative_events.filter(
                    location=venue_name,
                    event_timing__in=['Night ', 'Night']  # Fixed: Check both formats
                ).exists()
            else:
                # Fallback to exact match
                is_confirmed = confirmed_events.filter(location=venue_name, event_timing=time_slot).exists()
                is_tentative = tentative_events.filter(location=venue_name, event_timing=time_slot).exists()
            
            # Determine status based on priority: Confirmed > Tentative > Available
            if is_confirmed:
                status = 'Booked'
            elif is_tentative:
                status = 'Tentative'
            else:
                status = 'Available'
            
            # Display the time slot without trailing space for UI
            display_time = time_slot.strip()
            
            venue_data['time_slots'].append({
                'time_range': display_time,
                'status': status
            })
        
        venues_data.append(venue_data)
    
    return venues_data

def get_month_availability(year, month):
    """Get availability overview for a month"""
    
    # Get all events for the month (confirmed and tentative)
    # Handle both 'Tentative' and 'Tentative ' formats for compatibility
    confirmed_events = EventSale.objects.filter(
        event_date__year=year,
        event_date__month=month,
        status='Confirm'
    )
    
    tentative_events = EventSale.objects.filter(
        event_date__year=year,
        event_date__month=month,
        status__in=['Tentative ', 'Tentative']  # Handle both formats
    )
    
    # Group events by date
    confirmed_events_by_date = defaultdict(list)
    tentative_events_by_date = defaultdict(list)
    
    for event in confirmed_events:
        date_str = event.event_date.strftime('%Y-%m-%d')
        confirmed_events_by_date[date_str].append(event)
        
    for event in tentative_events:
        date_str = event.event_date.strftime('%Y-%m-%d')
        tentative_events_by_date[date_str].append(event)
    
    # Define total venues and time slots
    total_venues = 4  # Hall 1, Hall 2, Lawn, Business Center
    total_slots_per_venue = 2  # Day, Night
    total_slots = total_venues * total_slots_per_venue
    
    availability_data = {}
    
    # Get all dates that have any events (confirmed or tentative)
    all_event_dates = set(confirmed_events_by_date.keys()) | set(tentative_events_by_date.keys())
    
    # Calculate availability for each date
    for date_str in all_event_dates:
        confirmed_count = len(confirmed_events_by_date.get(date_str, []))
        tentative_count = len(tentative_events_by_date.get(date_str, []))
        
        # Count unique venues for confirmed events
        confirmed_venues = set(event.location for event in confirmed_events_by_date.get(date_str, []))
        tentative_venues = set(event.location for event in tentative_events_by_date.get(date_str, []))
        
        # Calculate availability status
        total_booked_slots = confirmed_count + tentative_count
        available_slots = total_slots - total_booked_slots
        
        # Determine overall status for the day
        if confirmed_count > 0 and tentative_count > 0:
            # Mixed confirmed and tentative
            status = 'partially-available'
        elif confirmed_count > 0:
            # Only confirmed events
            if confirmed_count >= total_slots:
                status = 'fully-booked'
            else:
                status = 'partially-available'
        elif tentative_count > 0:
            # Only tentative events
            status = 'tentative'
        else:
            status = 'fully-available'
        
        availability_data[date_str] = {
            'total_venues': total_venues,
            'available_venues': max(0, total_venues - len(confirmed_venues | tentative_venues)),
            'total_slots': total_slots,
            'booked_slots': confirmed_count,
            'tentative_slots': tentative_count,
            'available_slots': available_slots,
            'status': status
        }
    
    return availability_data
