// Public Calendar JavaScript
let currentDate = new Date();
let selectedDate = null;
let calendarConfig = {};

function initializeCalendar(config) {
    console.log('Initializing calendar with config:', config);
    calendarConfig = config;
    currentDate = config.currentDate || new Date();
    
    // Set up event listeners
    const prevBtn = document.getElementById('prevMonth');
    const nextBtn = document.getElementById('nextMonth');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', previousMonth);
        console.log('Previous month button listener added');
    } else {
        console.error('Previous month button not found');
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', nextMonth);
        console.log('Next month button listener added');
    } else {
        console.error('Next month button not found');
    }
    
    // Generate initial calendar
    generateCalendar();
}

function generateCalendar() {
    console.log('Generating calendar for:', currentDate);
    
    const monthNames = [
        "January", "February", "March", "April", "May", "June",
        "July", "August", "September", "October", "November", "December"
    ];
    
    const monthTitle = document.getElementById('monthTitle');
    const daysGrid = document.getElementById('daysGrid');
    
    if (!monthTitle) {
        console.error('Month title element not found');
        return;
    }
    
    if (!daysGrid) {
        console.error('Days grid element not found');
        return;
    }
    
    // Update month title
    const monthText = `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
    monthTitle.textContent = monthText;
    console.log('Updated month title to:', monthText);
    
    // Clear previous days
    daysGrid.innerHTML = '';
    console.log('Cleared previous days');
    
    // Get first day of month
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    console.log('Calendar details:', {
        year: year,
        month: month,
        firstDay: firstDay,
        lastDay: lastDay,
        daysInMonth: daysInMonth,
        startingDayOfWeek: startingDayOfWeek
    });
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
        const emptyCell = document.createElement('div');
        emptyCell.className = 'day-cell empty-cell';
        daysGrid.appendChild(emptyCell);
    }
    
    // Add current month days only
    for (let day = 1; day <= daysInMonth; day++) {
        const cellDate = new Date(year, month, day);
        const dayCell = createDayCell(cellDate, true); // true = current month
        daysGrid.appendChild(dayCell);
    }
    
    console.log('Generated', daysGrid.children.length, 'day cells');
    
    // Load availability data for the current month
    loadMonthAvailability();
}

function createDayCell(date, isCurrentMonth) {
    const dayCell = document.createElement('div');
    dayCell.className = 'day-cell';
    dayCell.textContent = date.getDate();
    // Fix timezone offset issue by using local date formatting
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;
    dayCell.setAttribute('data-date', dateString);
    
    // Add classes based on date
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();
    const isPast = date < new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    if (!isCurrentMonth) {
        dayCell.classList.add('other-month');
    }
    
    if (isToday) {
        dayCell.classList.add('today');
    }
    
    if (isPast && isCurrentMonth) {
        dayCell.style.opacity = '0.5';
        dayCell.style.cursor = 'not-allowed';
    } else if (isCurrentMonth) {
        dayCell.addEventListener('click', () => selectDate(date));
        dayCell.style.cursor = 'pointer';
    }
    
    return dayCell;
}

function selectDate(date) {
    console.log('Date selected:', date);
    
    // Remove previous selection
    document.querySelectorAll('.day-cell.selected').forEach(cell => {
        cell.classList.remove('selected');
    });
    
    // Add selection to clicked date
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;
    const dayCell = document.querySelector(`[data-date="${dateString}"]`);
    if (dayCell) {
        dayCell.classList.add('selected');
    }
    
    selectedDate = date;
    
    // Update selected date display
    const selectedDateSpan = document.getElementById('selectedDate');
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    selectedDateSpan.textContent = date.toLocaleDateString('en-US', options);
    
    // Show availability container and load data
    const availabilityContainer = document.getElementById('availabilityContainer');
    if (availabilityContainer) {
        availabilityContainer.style.display = 'block';
        loadDateAvailability(date);
    }
}

function loadMonthAvailability() {
    if (!calendarConfig.availabilityUrl) {
        console.error('Availability URL not configured');
        return;
    }
    
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    
    console.log('Loading month availability for:', year, month);
    
    fetch(`${calendarConfig.availabilityUrl}?month=${year}-${month.toString().padStart(2, '0')}`, {
        method: 'GET',
        headers: {
            'X-CSRFToken': calendarConfig.csrfToken,
            'Content-Type': 'application/json',
        },
    })
    .then(response => {
        console.log('Month availability response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Month availability data:', data);
        if (data.success) {
            updateCalendarAvailability(data.availability);
        }
    })
    .catch(error => {
        console.error('Error loading month availability:', error);
    });
}

function updateCalendarAvailability(availabilityData) {
    console.log('Updating calendar with availability data:', availabilityData);
    
    Object.keys(availabilityData).forEach(dateString => {
        const dayCell = document.querySelector(`[data-date="${dateString}"]`);
        if (dayCell && !dayCell.classList.contains('other-month')) {
            const availability = availabilityData[dateString];
            
            // Remove previous availability classes
            dayCell.classList.remove('fully-booked', 'partially-available', 'fully-available', 'tentative', 'has-events');
            
            if (availability.status) {
                dayCell.classList.add('has-events');
                
                // Use the status from backend
                if (availability.status === 'fully-booked') {
                    dayCell.classList.add('fully-booked');
                } else if (availability.status === 'partially-available') {
                    dayCell.classList.add('partially-available');
                } else if (availability.status === 'tentative') {
                    dayCell.classList.add('tentative');
                } else if (availability.status === 'fully-available') {
                    dayCell.classList.add('fully-available');
                }
            } else {
                // Fallback to old logic if status not provided
                if (availability.total_venues > 0) {
                    dayCell.classList.add('has-events');
                    
                    if (availability.available_venues === 0) {
                        dayCell.classList.add('fully-booked');
                    } else if (availability.available_venues < availability.total_venues) {
                        dayCell.classList.add('partially-available');
                    } else {
                        dayCell.classList.add('fully-available');
                    }
                }
            }
        }
    });
}

function loadDateAvailability(date) {
    const loadingSpinner = document.getElementById('loadingSpinner');
    const venueCards = document.getElementById('venueCards');
    const noEventsMessage = document.getElementById('noEventsMessage');
    
    if (!calendarConfig.availabilityUrl) {
        console.error('Availability URL not configured');
        return;
    }
    
    // Show loading spinner
    if (loadingSpinner) loadingSpinner.style.display = 'block';
    if (venueCards) venueCards.innerHTML = '';
    if (noEventsMessage) noEventsMessage.style.display = 'none';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;
    console.log('Loading availability for date:', dateString);
    
    fetch(`${calendarConfig.availabilityUrl}?date=${dateString}`, {
        method: 'GET',
        headers: {
            'X-CSRFToken': calendarConfig.csrfToken,
            'Content-Type': 'application/json',
        },
    })
    .then(response => {
        console.log('Date availability response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Date availability data:', data);
        if (loadingSpinner) loadingSpinner.style.display = 'none';
        
        if (data.success && data.venues && data.venues.length > 0) {
            renderVenueAvailability(data.venues);
        } else {
            if (noEventsMessage) noEventsMessage.style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error loading date availability:', error);
        if (loadingSpinner) loadingSpinner.style.display = 'none';
        if (noEventsMessage) noEventsMessage.style.display = 'block';
    });
}

function renderVenueAvailability(venues) {
    console.log('Rendering venue availability:', venues);
    const venueCards = document.getElementById('venueCards');
    if (!venueCards) {
        console.error('Venue cards container not found');
        return;
    }
    
    venueCards.innerHTML = '';
    
    venues.forEach(venue => {
        const venueCard = createVenueCard(venue);
        venueCards.appendChild(venueCard);
    });
}

function createVenueCard(venue) {
    const venueCard = document.createElement('div');
    venueCard.className = 'venue-card';
    
    const venueName = document.createElement('div');
    venueName.className = 'venue-name';
    // Display venue names with proper formatting
    let displayName = venue.name;
    if (venue.name === 'Hall1') displayName = 'Hall 1';
    else if (venue.name === 'Hall2') displayName = 'Hall 2';
    else if (venue.name === 'Business_Center') displayName = 'Business Center';
    venueName.textContent = displayName;
    venueCard.appendChild(venueName);
    
    // Add time slots
    if (venue.time_slots && venue.time_slots.length > 0) {
        venue.time_slots.forEach(slot => {
            const timeSlot = document.createElement('div');
            timeSlot.className = 'time-slot';
            
            const slotTime = document.createElement('span');
            slotTime.className = 'slot-time';
            slotTime.textContent = slot.time_range;
            
            const slotStatus = document.createElement('span');
            slotStatus.className = `slot-status ${slot.status.toLowerCase()}`;
            slotStatus.textContent = slot.status;
            
            timeSlot.appendChild(slotTime);
            timeSlot.appendChild(slotStatus);
            venueCard.appendChild(timeSlot);
        });
    }
    
    return venueCard;
}

function previousMonth() {
    console.log('Previous month clicked');
    currentDate.setMonth(currentDate.getMonth() - 1);
    generateCalendar();
    
    // Hide availability if shown
    const availabilityContainer = document.getElementById('availabilityContainer');
    if (availabilityContainer) {
        availabilityContainer.style.display = 'none';
    }
    selectedDate = null;
}

function nextMonth() {
    console.log('Next month clicked');
    currentDate.setMonth(currentDate.getMonth() + 1);
    generateCalendar();
    
    // Hide availability if shown
    const availabilityContainer = document.getElementById('availabilityContainer');
    if (availabilityContainer) {
        availabilityContainer.style.display = 'none';
    }
    selectedDate = null;
}

// Export functions for global access
window.initializeCalendar = initializeCalendar; 