from django.core.management.base import BaseCommand
from ecommerce.models import EventSale, EventExpense
from datetime import datetime, timedelta
import random

class Command(BaseCommand):
    help = 'Generate test data for EventSale and EventExpense models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--delete',
            action='store_true',
            help='Delete existing records before generating new ones',
        )
        parser.add_argument(
            '--years',
            type=int,
            default=3,
            help='Number of years to spread data across (default: 3)',
        )

    def handle(self, *args, **kwargs):
        if kwargs.get('delete'):
            # First delete related records
            self.stdout.write("Deleting related records...")
            EventExpense.objects.all().delete()
            EventSale.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Deleted all existing records'))

        years = kwargs['years']
        
        # Sample food items with their price ranges
        food_items = [
            'Chicken_Biryani', '<PERSON><PERSON>_Biryani', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
            'Cold_Drinks_Tin', 'Water_500ML', 'Naan', 'BBQ_Reshmi_Kabab',
            'BBQ_Chicken_<PERSON><PERSON>ri_<PERSON>', 'Cold_Drinks_1.5L', 'Water_300ML'
        ]

        # BBQ types
        bbq_types = ['Reshmi_Kabab', 'Chicken_Achari_Boti', 'Chicken_Tikka']
        
        # Cold drink types
        cold_drink_types = ['1.5L', 'Tin', '500ML']
        
        # Water bottle types
        water_types = ['300ML', '500ML', '1.5L']

        # Generate data for each year
        end_date = datetime.now()
        for year in range(years):
            current_year = end_date.year - year
            self.stdout.write(self.style.SUCCESS(f'\nGenerating data for year {current_year}'))

            for month in range(1, 13):
                self.stdout.write(f'Generating data for month {month}')

                for day in range(1, 26):
                    try:
                        for _ in range(random.randint(2, 3)):
                            event_date = datetime(current_year, month, day).date()

                            # Generate random food menu
                            food_menu = ', '.join([
                                f"{item} ({random.randint(10, 100)})"
                                for item in food_items
                            ])

                            # Create event sale record
                            event = EventSale.objects.create(
                                status='Confirm',
                                event_timing=random.choice(['Day', 'Night ']),
                                event_date=event_date,
                                no_of_people=random.randint(50, 300),
                                setup=random.choice(['Normal', 'Delux', 'VIP']),
                                food_menu=food_menu,
                                customer_name=f"Test Customer {event_date}_{_+1}",
                                stage_charges=random.randint(1000, 5000),
                                entry_charges=random.randint(500, 2000),
                                hall_charges=random.randint(50000, 150000),
                                gents=random.randint(30, 200),
                                ladies=random.randint(20, 100),
                                customer_number=f"03{random.randint(10, 99)}{random.randint(1000000, 9999999)}",
                                per_head=random.randint(800, 2000),
                                extra_charges=random.randint(5000, 20000),
                                detials="Test event details",
                                total_menu=random.randint(50000, 150000),
                                discount_amount=random.randint(0, 10000),
                                payment_details="Initial payment",
                                total_amount=random.randint(100000, 300000),
                                recieved_amount=random.randint(50000, 150000),
                                remaining_amount=random.randint(0, 50000),
                                deals="Custom Deal"
                            )

                            self.stdout.write(self.style.SUCCESS(f'Created event record for {event_date}'))

                            # Calculate total expense (example calculation)
                            total_expense = (
                                event.stage_charges + event.entry_charges + event.hall_charges +
                                random.randint(1000, 5000)  # Add any other relevant expenses
                            )

                            # Create corresponding expense record
                            expense = EventExpense.objects.create(
                                bill=event,
                                customer_name=event.customer_name,
                                expense_date=event_date,
                                naan_qty=random.randint(100, 500),
                                cold_drink=random.randint(50, 200),
                                cold_drink_type=random.choice(cold_drink_types),
                                water=random.randint(50, 200),
                                water_bottles_type=random.choice(water_types),
                                bbq_kg_qty=random.randint(20, 100),
                                bbq_type=random.choice(bbq_types),
                                pakwan_bill=random.randint(10000, 50000),
                                electicity=random.randint(5000, 15000),
                                diesel_ltr=random.randint(10, 50),
                                no_of_waiters=random.randint(5, 15),
                                dhobi=random.randint(1000, 5000),
                                other_expense=random.randint(1000, 10000),
                                other_expense_detals="Test expense details",
                                setup_bill=random.randint(5000, 20000),
                                decor="Test decoration",
                                decor_bill=random.randint(10000, 30000),
                                total_expense=total_expense
                            )

                            self.stdout.write(self.style.SUCCESS(f'Created expense record for {event_date}'))

                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error generating record for {current_year}-{month}-{day}: {str(e)}')
                        )

        total_events = EventSale.objects.count()
        total_expenses = EventExpense.objects.count()
        self.stdout.write(
            self.style.SUCCESS(f'\nSuccessfully generated {total_events} events and {total_expenses} expense records across {years} years')
        ) 