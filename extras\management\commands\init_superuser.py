from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import IntegrityError

class Command(BaseCommand):
    help = 'Create a superuser for the Hall System'

    def handle(self, *args, **options):
        User = get_user_model()
        
        try:
            # Check if superuser already exists
            if User.objects.filter(is_superuser=True).exists():
                self.stdout.write(
                    self.style.SUCCESS('✅ Superuser already exists')
                )
                return
            
            # Create superuser
            User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123'
            )
            
            self.stdout.write(
                self.style.SUCCESS('✅ Superuser created successfully')
            )
            self.stdout.write(
                self.style.SUCCESS('   Username: admin')
            )
            self.stdout.write(
                self.style.SUCCESS('   Password: admin123')
            )
            
        except IntegrityError:
            self.stdout.write(
                self.style.WARNING('⚠️  Superuser already exists')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error creating superuser: {e}')
            )
            self.stdout.write(
                self.style.WARNING('💡 You can create a superuser later with: python manage.py createsuperuser')
            ) 