from django.contrib import admin

from .models import MyProducts, Category, Deals, Unit, Inventory, Brand, Vendors, RentalProduct, RentalTransaction


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'status')


@admin.register(Vendors)
class VendorsAdmin(admin.ModelAdmin):
    list_display = ('name', 'contact', 'vendor_for')

@admin.register(MyProducts)
class MyProductsAdmin(admin.ModelAdmin):
    list_display = ('product_name', 'product_desc', 'price', 'status')


class DealsAdmin(admin.ModelAdmin):
    list_display = ['code', 'item_count', 'created_at'] if hasattr(Deals, 'created_at') else ['code', 'item_count']
    list_filter = ['created_at'] if hasattr(Deals, 'created_at') else []
    search_fields = ['code']
    filter_horizontal = ['menu_items']  # This makes it easier to select multiple items
    
    def item_count(self, obj):
        return obj.menu_items.count()
    item_count.short_description = 'Number of Items'

admin.site.register(Deals, DealsAdmin)


admin.site.register(Inventory)
admin.site.register(Brand)
admin.site.register(Unit)

@admin.register(RentalProduct)
class RentalProductAdmin(admin.ModelAdmin):
    list_display = ('product_name', 'price_per_day', 'quantity_available', 'status')

@admin.register(RentalTransaction)
class RentalTransactionAdmin(admin.ModelAdmin):
    list_display = ('customer_name', 'product', 'rental_date', 'expected_return_date', 'status')
