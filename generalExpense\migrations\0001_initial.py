# Generated by Django 4.2.7 on 2025-06-26 14:35

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ConstructionAndRepair',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expense_for', models.Char<PERSON>ield(max_length=200)),
                ('details', models.CharField(blank=True, max_length=300)),
                ('amount', models.IntegerField(default=0)),
                ('on_date', models.DateField()),
                ('expense_by', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='DailyExpenses',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expense_type', models.CharField(choices=[('Cards', 'Cards'), ('Internet Bill', 'Internet Bill'), ('Tea', 'Tea'), ('Utility Bills', 'Utility Bills'), ('Food', 'Food'), ('Other Expenses', 'Others Expenses')], max_length=20)),
                ('details', models.CharField(blank=True, max_length=300)),
                ('amount', models.IntegerField(default=0)),
                ('on_date', models.DateField()),
                ('expense_by', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='OtherExpense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expense_type', models.CharField(choices=[('Rents', 'Rents'), ('Taxes', 'Taxes'), ('Online Expense', 'Online Expense'), ('Other Expense', 'Other Expense')], max_length=20)),
                ('details', models.CharField(blank=True, max_length=300)),
                ('amount', models.IntegerField(default=0)),
                ('on_date', models.DateField()),
                ('expense_by', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='Salary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_type', models.CharField(choices=[('Guards', 'Guards'), ('Marquee Staff', 'Marquee Staff'), ('Office Staff', 'Office Staff')], max_length=20)),
                ('details', models.CharField(blank=True, max_length=300)),
                ('amount', models.IntegerField(default=0)),
                ('on_date', models.DateField()),
                ('expense_by', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='VendorsList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vendor_name', models.CharField(max_length=120)),
                ('contact_number', models.BigIntegerField(default=0)),
                ('vendor_for', models.CharField(max_length=50)),
                ('remarks', models.TextField(blank=True)),
            ],
        ),
    ]
