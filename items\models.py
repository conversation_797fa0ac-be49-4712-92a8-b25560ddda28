from django.db import models
from django.utils import timezone
from django.conf import settings

class Category(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()
    status = models.IntegerField(default=1)
    date_added = models.DateTimeField(default=timezone.now)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name





class Unit(models.Model):
    name = models.CharField(max_length=100)
    short_name = models.CharField(max_length=10)
    unit = models.CharField(max_length=30)
    date_added = models.DateTimeField(default=timezone.now)
    def __str__(self):
            return self.name


class Vendors(models.Model):
    name = models.CharField(max_length=100)
    address = models.CharField(max_length=200)
    contact = models.CharField(max_length=14)
    vendor_for = models.CharField(max_length=50)
    date_added = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return self.name
class Brand(models.Model):
    name = models.CharField(max_length=100)
    desc = models.TextField(max_length=300)
    date_added = models.DateTimeField(default=timezone.now)
    def __str__(self):
        return self.name

class MyProducts(models.Model):
    code = models.CharField(max_length=100, blank=True)
    product_name = models.CharField(max_length=100)
    category_id = models.ForeignKey(Category, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    price = models.IntegerField(default=0)
    cost = models.IntegerField(default=0)
    qty = models.IntegerField(default=0)
    product_image = models.ImageField(upload_to = f'product_images/%Y/%m', blank = True)
    product_desc = models.TextField()
    date_added = models.DateTimeField(default=timezone.now)
    date_updated = models.DateTimeField(default=timezone.now, null=True)
    status = models.IntegerField(default=1)

    def __str__(self):
        return self.product_name

        
class Inventory(models.Model):
    
    product_name = models.ForeignKey(MyProducts, on_delete=models.CASCADE)
    qty = models.IntegerField(default=0)
    date_added = models.DateTimeField(default=timezone.now)

    def __str__(self):
            return f"{self.product_name}"



from django.db import models

class Deals(models.Model):
    code = models.CharField(max_length=100, unique=True, verbose_name="Deal Name")
    menu_items = models.ManyToManyField('MyProducts', related_name='deals', blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    
    class Meta:
        verbose_name = "Deal"
        verbose_name_plural = "Deals"
        ordering = ['-created_at']

    def __str__(self):
        return self.code
    
    def item_count(self):
        """Get count of items in this deal"""
        return self.menu_items.count()
    
    def get_items_list(self):
        """Get formatted list of item names"""
        return ", ".join([item.product_name for item in self.menu_items.all()])

class RentalProduct(models.Model):
    product_name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    price_per_day = models.DecimalField(max_digits=10, decimal_places=2)
    quantity_available = models.IntegerField(default=1)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, null=True)
    image = models.ImageField(upload_to='rental_products/', blank=True, null=True)
    status = models.BooleanField(default=True)  # Available for rent
    
    def __str__(self):
        return self.product_name
        
class RentalTransaction(models.Model):
    RENTAL_STATUS = (
        ('Active', 'Active'),
        ('Returned', 'Returned'),
        ('Overdue', 'Overdue'),
    )
    
    product = models.ForeignKey(RentalProduct, on_delete=models.CASCADE, related_name='items_rentals')
    customer_name = models.CharField(max_length=200)
    customer_contact = models.CharField(max_length=20)
    quantity = models.IntegerField(default=1)
    rental_date = models.DateField()
    expected_return_date = models.DateField()
    actual_return_date = models.DateField(null=True, blank=True)
    price_per_day = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=RENTAL_STATUS, default='Active')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    deposit_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    damage_charges = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    damage_notes = models.TextField(blank=True)
    missing_items = models.IntegerField(default=0)
    missing_charges = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.customer_name} - {self.product.product_name}"
