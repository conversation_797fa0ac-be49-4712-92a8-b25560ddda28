from .base import *
import os

# Database Configuration - supports both PostgreSQL and SQLite
DEBUG = True

ALLOWED_HOSTS = ['127.0.0.1', 'localhost', '*', 'healthcheck.railway.app', '*.railway.app']

# Check if user wants to use SQLite instead of PostgreSQL
USE_SQLITE = os.environ.get('USE_SQLITE', 'false').lower() == 'true'

if USE_SQLITE:
    # SQLite Database Configuration
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR.parent / 'persistent_data' / 'db_data',
            'OPTIONS': {
                'timeout': 20,
                'check_same_thread': False,
            }
        }
    }
    
    # SQLite backup settings
    DBBACKUP_STORAGE_OPTIONS = {'location': BASE_DIR.parent / 'persistent_data' / 'db_backups'}
    
    # Ensure directories exist
    os.makedirs(BASE_DIR.parent / 'persistent_data', exist_ok=True)
    os.makedirs(BASE_DIR.parent / 'persistent_data' / 'db_backups', exist_ok=True)
    
else:
    # PostgreSQL Database Configuration
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': 'hall_db',
            'USER': 'postgres',
            'PASSWORD': 'admin',
            'HOST': 'localhost',
            'PORT': '5432',
        }
    }
    
    # Additional PostgreSQL optimizations
    CONN_MAX_AGE = 0
