{"version": 3, "sources": ["custom/fonts/_fonts.scss", "custom/structure/_topbar.scss", "app.css", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_vertical.scss", "custom/structure/_horizontal-nav.scss", "custom/structure/_layouts.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "custom/components/_accordion.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_forms.scss", "custom/components/_widgets.scss", "custom/components/_demos.scss", "custom/components/_print.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_calendar.scss", "custom/plugins/_color-picker.scss", "custom/plugins/_session-timeout.scss", "custom/plugins/_range-slider.scss", "custom/plugins/_sweatalert2.scss", "custom/plugins/_rating.scss", "custom/plugins/_parsley.scss", "custom/plugins/_select2.scss", "custom/plugins/_switch.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_bootstrap-touchspin.scss", "custom/plugins/_datatable.scss", "custom/plugins/_form-editors.scss", "custom/plugins/_form-upload.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_table-editable.scss", "custom/plugins/_apexcharts.scss", "custom/plugins/_chartist.scss", "custom/plugins/_flot.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_google-map.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_x-editable.scss", "custom/pages/_authentication.scss", "custom/pages/_ecommerce.scss", "custom/pages/_email.scss", "custom/pages/_chat.scss", "custom/pages/_coming-soon.scss", "custom/pages/_timeline.scss", "custom/pages/_extras-pages.scss"], "names": [], "mappings": "AAIA,0FCAA,aACI,SAAA,MACA,IAAA,EACA,MAAA,EACA,KAAA,EACA,QAAA,KACA,iBAAA,KAGJ,eACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,QACA,iBAAA,QAAA,gBAAA,cACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,OAAA,EAAA,KACA,OAAA,KACA,QAAA,EAAA,eAAA,EAAA,EACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBARJ,2CAYY,iBAAA,QAKZ,kBACI,QAAA,EAAA,OACA,WAAA,OACA,MAAA,MAGJ,MACI,YAAA,KADJ,eAIQ,QAAA,KAIR,WACI,QAAA,MAGJ,YACI,QAAA,KAIJ,oEAGY,QAAA,SAQZ,sBACI,WAAA,MAIJ,gBACI,WAAA,6BACA,oBAAA,OACA,iBAAA,QACA,OAAA,EAAA,MAAA,KAAA,MACA,QAAA,KAAA,KAAA,KAAA,KACA,MAAA,KACA,gBAAA,MAKJ,aACI,iBAAA,KACA,MAAA,QACA,QAAA,KACA,SAAA,SACA,IAAA,EACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,MAAA,KACA,MAAA,EACA,OAAA,KACA,QAAA,EAAA,KACA,kBAAA,uBAAA,UAAA,uBACA,mBAAA,IAAA,WAAA,IAZJ,kBAeM,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,MAAA,KAhBN,yBAmBM,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,iBAAA,QAAA,cAAA,QAAA,gBAAA,cACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,MAAA,KAtBN,2BAyBM,iBAAA,EAAA,SAAA,EAAA,EAAA,KAAA,EAAA,EACA,OAAA,KACA,QAAA,EACA,mBAAA,KAAA,WAAA,KACA,iBAAA,YA7BN,2BAgCM,MAAA,KACA,OAAA,KACA,YAAA,KACA,WAAA,OACA,MAAA,QACA,UAAA,KArCN,iCAwCQ,MAAA,QAKN,kBACE,kBAAA,mBAAA,UAAA,mBAKJ,kBAEQ,SAAA,SACA,QAAA,IAAA,EAHR,oBAKY,MAAA,QAKZ,yBAEI,aACI,KAAA,EAEJ,kBACI,MAAA,KAGJ,mBAGQ,QAAA,KAHR,mBAOQ,QAAA,cAKZ,cACI,QAAA,WAAA,eAAA,KAAA,eAGJ,aACI,OAAA,KACA,mBAAA,eAAA,WAAA,eACA,MAAA,QACA,OAAA,EACA,cAAA,EALJ,mBAQQ,MAAA,QAIR,qBACI,OAAA,KACA,MAAA,KACA,iBAAA,QACA,QAAA,IAGJ,aAEQ,UAAA,KACA,MAAA,QAHR,kBAOQ,SAAA,SACA,IAAA,KACA,MAAA,IAIR,0BAEQ,QAAA,OAAA,KAFR,gCAKY,iBAAA,QAMZ,oBACI,QAAA,MACA,cAAA,IACA,YAAA,KACA,WAAA,OACA,QAAA,KAAA,EAAA,IACA,QAAA,MACA,OAAA,IAAA,MAAA,YACA,MAAA,QARJ,wBAWQ,OAAA,KAXR,yBAeQ,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OAlBR,0BAsBQ,aAAA,QAKR,mEAGY,QAAA,QAKZ,oCAEQ,iBAAA,QAFR,kEAOgB,iBAAA,sBAPhB,kEAYY,WAAA,qBAZZ,oCAiBQ,MAAA,QAjBR,0CAoBY,MAAA,QApBZ,4CAyBQ,iBAAA,sBAzBR,oCA8BY,MAAA,QA9BZ,2CAqCY,MAAA,QArCZ,iDAiDY,iBAAA,sBACA,MAAA,KC1EZ,iFDwBA,wCAsDY,MAAA,qBAKZ,0CAEQ,WAAA,QAFR,mCAMQ,QAAA,KANR,oCAUQ,QAAA,MAIR,yBACI,yBAEQ,SAAA,OAFR,wCAKY,KAAA,eACA,MAAA,gBAMhB,yBAGI,kBACI,QAAA,MAIR,+CAEQ,MAAA,KAFR,2CAKQ,WAAA,KACA,QAAA,kBAAA,eAAA,KAAA,eAIR,yBACI,2CAEQ,WAAA,ME5VZ,4BAGQ,iBAAA,YACA,QAAA,EAJR,mBAQQ,MAAA,KACA,eAAA,UACA,YAAA,IACA,UAAA,eAIR,oBACI,QAAA,eAAA,EAGJ,oBAGQ,UAAA,KACA,YAAA,KACA,MAAA,QCxBR,QACI,OAAA,EACA,QAAA,KAAA,eACA,SAAA,SACA,MAAA,EACA,WAAA,IAAA,MAAA,QACA,MAAA,QACA,KAAA,MACA,OAAA,KACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBACA,iBAAA,KAGJ,yBACI,QACI,KAAA,GAKR,2BAEQ,KAAA,KAIR,qCAEQ,KAAA,YC5BR,WACI,iBAAA,KACA,mBAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,QAAA,MACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,MACA,QAAA,KACA,MAAA,gBACA,MAAA,OACA,IAAA,EACA,OAAA,EAXJ,6BAcQ,iBAAA,QACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,MAAA,QACA,WAAA,OACA,cAAA,IApBR,mCAuBY,iBAAA,QAMZ,kBACI,iBAAA,mBACA,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,OAAA,EACA,QAAA,KACA,QAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAGJ,8BAEQ,MAAA,EAFR,qCAKQ,QAAA,MC0BJ,4BDrBA,WACI,SAAA,KADJ,4BAGQ,OAAA,gBEtDZ,WACI,OAAA,EADJ,cAIQ,QAAA,MACA,MAAA,KALR,wBASQ,QAAA,KATR,sCAYY,QAAA,KAZZ,gCAgBY,QAAA,MAhBZ,0BAqBQ,SAAA,SACA,OAAA,EACA,SAAA,OACA,mCAAA,KAAA,2BAAA,KACA,4BAAA,KAAA,oBAAA,KACA,4BAAA,MAAA,CAAA,WAAA,oBAAA,MAAA,CAAA,WAKR,eACI,MAAA,MACA,QAAA,KACA,WAAA,KACA,OAAA,EACA,WAAA,EACA,SAAA,MACA,IAAA,KACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBAGJ,cACI,SAAA,SACA,WAAA,OACA,WAAA,4BACA,iBAAA,QACA,kBAAA,UACA,gBAAA,MACA,oBAAA,OACA,QAAA,KAAA,EARJ,wBAUQ,SAAA,SAVR,4BAYY,MAAA,KACA,OAAA,KACA,OAAA,IAAA,MAAA,QACA,QAAA,IAfZ,uCAkBY,SAAA,SACR,OAAA,IACA,MAAA,KACA,OAAA,KACA,QAAA,EACA,OAAA,IAAA,MAAA,YACA,cAAA,IACA,YAAA,MAKJ,cACI,YAAA,MACA,SAAA,OAFJ,uBAKQ,QAAA,EAAA,KAAA,KAAA,KACA,WAAA,KAKR,cACI,QAAA,EAAA,EAAA,KAAA,EADJ,0CAMgB,kBAAA,cAAA,UAAA,cANhB,+BAaY,QAAA,SACA,YAAA,wBACA,QAAA,MACA,MAAA,MACA,mBAAA,kBAAA,IAAA,WAAA,kBAAA,IAAA,WAAA,UAAA,IAAA,WAAA,UAAA,GAAA,CAAA,kBAAA,IACA,UAAA,KAlBZ,sBAyBgB,QAAA,MACA,QAAA,QAAA,OACA,MAAA,QACA,SAAA,SACA,UAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,OAAA,EAAA,KACA,cAAA,IAhChB,wBAmCoB,QAAA,aACA,UAAA,QACA,eAAA,OACA,UAAA,KACA,YAAA,WACA,eAAA,OACA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IA1CpB,4BA8CoB,MAAA,QA9CpB,8BAiDwB,MAAA,QAjDxB,2BAuDgB,WAAA,IAvDhB,gCA2DgB,QAAA,EA3DhB,qCAgEwB,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,KACA,MAAA,QACA,iBAAA,sBAnExB,4CAqE4B,QAAA,SACA,YAAA,wBACA,UAAA,KACA,YAAA,KACA,cAAA,IACA,eAAA,OACA,QAAA,aA3E5B,+CAgFwB,QAAA,EAhFxB,oDAoFgC,QAAA,MAAA,OAAA,MAAA,KACA,UAAA,KAWhC,YACI,QAAA,KAAA,eACA,eAAA,MACA,eAAA,KACA,OAAA,QACA,UAAA,KACA,eAAA,UACA,MAAA,QACA,YAAA,IAGJ,WACI,MAAA,kBADJ,aAGQ,MAAA,kBACA,iBAAA,kBAJR,eAMY,MAAA,kBANZ,aAUQ,MAAA,kBAVR,mBAaQ,MAAA,kBACA,iBAAA,kBAdR,qBAiBY,MAAA,kBAKZ,yBACI,eACI,QAAA,KAGJ,cACI,YAAA,YAGJ,mCAEQ,QAAA,OAMZ,iCAGQ,QAAA,KAHR,iCAQQ,YAAA,KARR,qCAYQ,MAAA,eAZR,sCAiBY,QAAA,KAjBZ,sCAqBY,QAAA,MArBZ,kCA2BQ,SAAA,SACA,MAAA,eACA,QAAA,ELwQN,6DKrSF,kDAiCY,SAAA,kBAjCZ,uDAqCY,QAAA,eArCZ,oDAyCY,OAAA,YLmQV,uDACA,6DK7SF,4DAkDgB,QAAA,eAlDhB,8DAsDgB,OAAA,kBAtDhB,iEA2DoB,QAAA,KA3DpB,sDAiEoB,SAAA,SACA,YAAA,OAlEpB,wDAqEwB,QAAA,KAAA,KACA,WAAA,KACA,mBAAA,KAAA,WAAA,KACA,OAAA,EAxExB,+DAAA,8DAAA,8DA6E4B,MAAA,QA7E5B,0DAiF4B,UAAA,QACA,YAAA,IAlF5B,6DAsF4B,QAAA,KACA,aAAA,KAvF5B,8DA6F4B,SAAA,SACA,MAAA,mBACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KAhG5B,mEAkGgC,QAAA,OAlGhC,+DAuG4B,QAAA,MACA,KAAA,KACA,SAAA,SACA,MAAA,MACA,OAAA,eACA,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBA5G5B,kEA+GgC,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBA/GhC,iEAmHgC,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,KACA,SAAA,SACA,MAAA,MACA,QAAA,EACA,MAAA,QACA,OAAA,EAzHhC,uEA4HoC,MAAA,QA5HpC,sDAoIoB,QAAA,IAAA,EACA,QAAA,KACA,QAAA,KACA,iBAAA,KAvIpB,kEA4IgC,QAAA,MACA,KAAA,MACA,OAAA,eACA,WAAA,MACA,SAAA,SACA,MAAA,MAjJhC,2EAuJgC,SAAA,SACA,MAAA,KACA,IAAA,KACA,kBAAA,eAAA,UAAA,eA1JhC,kEAiK4B,MAAA,QAW5B,sCAGQ,WAAA,IAHR,uCAQQ,WAAA,QARR,8CAgBoB,MAAA,QAhBpB,gDAmBwB,MAAA,QAnBxB,oDAuBwB,MAAA,QAvBxB,sDA0B4B,MAAA,QA1B5B,6DAmC4B,MAAA,QAnC5B,mEAsCgC,MAAA,QAtChC,0CAgDQ,WAAA,OAhDR,qFA6DgC,WAAA,QACA,MAAA,QA9DhC,uFAgEoC,MAAA,QAhEpC,wFAsEoC,MAAA,QAtEpC,8FAwEwC,MAAA,QAxExC,6EAgFwB,iBAAA,KAhFxB,yFA0FgC,MAAA,kBA1FhC,yFAAA,4FAmGoC,MAAA,kBAnGpC,yFAAA,4FAyGoC,MAAA,kBAzGpC,mCA0HQ,MAAA,kBA1HR,qCA4HY,MAAA,kBACA,iBAAA,kBA7HZ,uCA+HgB,MAAA,kBA/HhB,qCAmIY,MAAA,kBAnIZ,2CAsIY,MAAA,kBACA,iBAAA,kBAvIZ,6CA0IgB,MAAA,kBA1IhB,oCAgJQ,MAAA,QAKR,2CAEQ,YAAA,YAMR,gDAEQ,MAAA,MAFR,6CAKQ,MAAA,MACA,WAAA,OL4GN,oDKlHF,8DAUY,QAAA,eAVZ,4CAcQ,YAAA,MAdR,sCAiBQ,KAAA,MAjBR,sDAwBoB,QAAA,MAxBpB,mEA8BwB,aAAA,OA9BxB,0EAgC4B,QAAA,KAhC5B,kFAwCgC,aAAA,OAxChC,8DAkDY,YAAA,KAlDZ,6EAsDgB,WAAA,KAtDhB,uFA2DgC,QAAA,aA3DhC,wDAmEY,KAAA,KLmCZ,yCKxBY,QAAA,SACA,mBAAA,kBAAA,IAAA,WAAA,kBAAA,IAAA,WAAA,UAAA,IAAA,WAAA,UAAA,GAAA,CAAA,kBAAA,IL4BZ,oDKtBgB,kBAAA,cAAA,UAAA,cChoBhB,QACI,WAAA,KACA,QAAA,EAAA,eACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBACA,WAAA,KACA,SAAA,MACA,KAAA,EACA,MAAA,EACA,QAAA,IARJ,qBAWQ,OAAA,EACA,QAAA,EAZR,8BAkBY,UAAA,KACA,SAAA,SACA,QAAA,OAAA,OACA,MAAA,QArBZ,gCAuBgB,UAAA,KACA,IAAA,IACA,SAAA,SAzBhB,oCAAA,oCA4BgB,MAAA,QACA,iBAAA,YA7BhB,mCAkCY,MAAA,QAlCZ,0CAAA,yCAoCgB,MAAA,QACA,WAAA,IArChB,+CA2CgB,MAAA,QA3ChB,uCAkDoB,MAAA,QACA,iBAAA,YFKhB,0BEIA,8CNmrBF,4CMhrBU,UAAA,KFPR,yBEaA,sDAKoB,aAAA,EALpB,uBAYQ,QAAA,MAAA,OACA,UAAA,MAbR,oDAoBgB,KAAA,EACA,MAAA,KArBhB,iCAyBY,WAAA,EACA,cAAA,EAAA,EAAA,OAAA,OA1BZ,oDA8BoB,MAAA,KACA,kBAAA,gBAAA,iBAAA,UAAA,gBAAA,iBACA,SAAA,SAhCpB,0DAsCoB,SAAA,SACA,IAAA,YACA,KAAA,KACA,QAAA,KAzCpB,uCAgDgB,QAAA,MAhDhB,sEAsDQ,QAAA,MAIR,eACI,QAAA,MAIR,YACI,QAAA,aADJ,kBAIQ,aAAA,QACA,aAAA,MACA,aAAA,EAAA,EAAA,IAAA,IACA,QAAA,GACA,OAAA,KACA,QAAA,aACA,MAAA,IACA,IAAA,IACA,YAAA,KACA,kBAAA,eAAA,iBAAA,UAAA,eAAA,iBACA,yBAAA,IAAA,iBAAA,IACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,KF/EJ,6BEsFA,kEAMwB,MAAA,KACA,KAAA,MF7FxB,4BEwGA,6BAEQ,QAAA,MAFR,0CAIY,QAAA,MAJZ,8BASQ,QAAA,KAIR,QACI,WAAA,MACA,WAAA,KACA,QAAA,EAHJ,8BAMY,QAAA,OAAA,OANZ,iCAYY,iBAAA,YACA,OAAA,KACA,mBAAA,KAAA,WAAA,KACA,aAAA,KAfZ,uDAiBgB,MAAA,KAjBhB,4DAoBoB,OAAA,EApBpB,iCA0BY,SAAA,SACA,iBAAA,YA3BZ,wCAAA,wCA+BgB,MAAA,QA/BhB,2BAsCY,MAAA,KACA,SAAA,UFzKZ,yBEkLA,6EAGY,QAAA,MAHZ,8EAOY,QAAA,KAPZ,wDAWQ,iBAAA,QAXR,8EAegB,MAAA,qBAfhB,oFAAA,oFAkBoB,MAAA,qBAlBpB,uFAyBwB,MAAA,gCAW5B,wCAGQ,QAAA,KAHR,yCAOQ,QAAA,MAOR,uCAEQ,iBAAA,QAFR,qEAOgB,iBAAA,sBAPhB,qEAYY,WAAA,qBAZZ,8CAmBY,MAAA,qBAnBZ,uCAyBQ,MAAA,qBAzBR,6CA4BY,MAAA,KA5BZ,+CAiCQ,iBAAA,sBAjCR,uCAsCY,MAAA,qBAtCZ,qCA2CQ,QAAA,KA3CR,sCA+CQ,QAAA,MA/CR,oDAqDY,iBAAA,sBACA,MAAA,KN4iBZ,oFMlmBA,2CA0DY,MAAA,qBCzVZ,6BACI,iBAAA,QADJ,6CAGQ,iBAAA,QACA,UAAA,OACA,OAAA,EAAA,KACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBANR,0CAUQ,UAAA,OACA,OAAA,EAAA,KAXR,qCAeQ,OAAA,EAAA,KACA,UAAA,qBAhBR,uDAqBY,UAAA,oBAQZ,qEAAA,kEAAA,6DAEQ,UAAA,KAFR,sEAAA,oEAKQ,UAAA,OCrCR;;;;;;AAOC,cACG,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,cRm5BF,cQj5BI,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,cRk5BF,oBAFA,oBACA,sBQ74BI,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MAGJ,wCAEQ,iBAAA,qBAIR,0CAEQ,iBAAA,mBAGR,0CAEQ,iBAAA,oBAGR,uCAEQ,iBAAA,oBAGR,0CAEQ,iBAAA,oBAGR,yCAEQ,iBAAA,mBChKR,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,gBAEE,MAAA,KACA,OAAA,KAIF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KCrCF,8BAGY,WAAA,MAHZ,uDAWoB,QAAA,SAXpB,+BAkBQ,cAAA,IAIR,8BAEQ,OAAA,IAAA,MAAA,QACA,mBAAA,KAAA,WAAA,KAHR,qCAMQ,aAAA,KACA,SAAA,SAPR,uDAUY,SAAA,SACA,QAAA,aACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,UAAA,KACA,iBAAA,QACA,MAAA,KACA,cAAA,IACA,WAAA,OACA,KAAA,KACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBAtBZ,8DA8BoB,QAAA,SCpDpB,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAMJ,OACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,kBAAA,MAAA,eAAA,MAAA,YAAA,WAGF,YACE,iBAAA,EAAA,SAAA,EAAA,KAAA,EAQJ,kBACI,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QACA,WAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IATJ,wBAYQ,MAAA,QACA,iBAAA,QAKR,MACI,UAAA,KAGJ,MACI,UAAA,KAGJ,MACI,UAAA,MAGJ,MACI,UAAA,MAGJ,MACI,UAAA,MAKJ,YACI,SAAA,SACA,OAAA,KACA,MAAA,KACA,MAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,QAAA,GACA,iBAAA,KAKJ,QACI,iBAAA,EAAA,SAAA,EAAA,KAAA,EAOJ,8BAEQ,UAAA,KACA,QAAA,QAAA,QACA,WAAA,YAAA,0TAAA,MAAA,CAAA,IAAA,KAAA,UCnIR,WACI,SAAA,MACA,IAAA,EACA,KAAA,EACA,MAAA,EACA,OAAA,EACA,iBAAA,KACA,QAAA,KAGJ,QACI,MAAA,KACA,OAAA,KACA,SAAA,SACA,KAAA,IACA,IAAA,IACA,OAAA,MAAA,EAAA,EAAA,MAGJ,eACI,OAAA,EAAA,KACA,MAAA,KACA,OAAA,KACA,SAAA,SACA,kBAAA,cAAA,KAAA,SAAA,OAAA,KAAA,UAAA,cAAA,KAAA,SAAA,OAAA,KAGJ,WACI,MAAA,KACA,OAAA,KACA,SAAA,SACA,KAAA,EACA,IAAA,EACA,kBAAA,UAAA,GAAA,SAAA,YAAA,KAAA,UAAA,UAAA,GAAA,SAAA,YAAA,KANJ,kBAQQ,QAAA,GACA,QAAA,MACA,MAAA,IACA,OAAA,IACA,iBAAA,QACA,cAAA,KACA,kBAAA,iBAAA,GAAA,SAAA,YAAA,KAAA,UAAA,iBAAA,GAAA,SAAA,YAAA,KAdR,wBAkBQ,wBAAA,MAAA,gBAAA,MAlBR,+BAoBY,wBAAA,MAAA,gBAAA,MApBZ,wBAwBQ,wBAAA,IAAA,gBAAA,IAxBR,+BA0BY,wBAAA,IAAA,gBAAA,IA1BZ,wBA8BQ,wBAAA,KAAA,gBAAA,KA9BR,+BAgCY,wBAAA,KAAA,gBAAA,KAhCZ,wBAoCQ,wBAAA,KAAA,gBAAA,KApCR,+BAsCY,wBAAA,KAAA,gBAAA,KAtCZ,wBA0CQ,wBAAA,KAAA,gBAAA,KA1CR,+BA4CY,wBAAA,KAAA,gBAAA,KA5CZ,wBAgDQ,wBAAA,KAAA,gBAAA,KAhDR,+BAkDY,wBAAA,KAAA,gBAAA,KAKZ,iCACI,KACI,kBAAA,eAAA,UAAA,gBAFR,yBACI,KACI,kBAAA,eAAA,UAAA,gBAIR,6BACI,KAAA,IACI,kBAAA,eAAA,UAAA,gBAFR,qBACI,KAAA,IACI,kBAAA,eAAA,UAAA,gBAIR,oCACI,IACI,kBAAA,UAAA,UAAA,UAEJ,GAAA,KACI,kBAAA,SAAA,UAAA,UALR,4BACI,IACI,kBAAA,UAAA,UAAA,UAEJ,GAAA,KACI,kBAAA,SAAA,UAAA,UCjGR,kBACE,aAAA,EACA,QAAA,aACA,cAAA,MAHF,oCAKI,MAAA,MACA,YAAA,EACA,aAAA,OAPJ,oCAUI,QAAA,MAIJ,YACE,SAAA,SACA,WAAA,KAIF,kBACE,OAAA,QACA,cAAA,ECrBF,cACI,WAAA,IAAA,MAAA,QAGJ,kBACI,cAAA,IAAA,MAAA,QAGJ,kBACI,WAAA,QAGJ,WACI,YAAA,IAAA,MAAA,QAEJ,yBACI,WACI,YAAA,MAIR,qBACI,OAAA,MADJ,4BAGQ,iBAAA,kBACA,MAAA,eACA,OAAA,eACA,cAAA,cACA,OAAA,IACA,QAAA,GAIR,gCAEQ,SAAA,OACA,SAAA,SAHR,sCAAA,uCAKY,QAAA,GACA,SAAA,SACA,MAAA,IACA,OAAA,KACA,iBAAA,qBACA,KAAA,KACA,kBAAA,cAAA,UAAA,cACA,IAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAbZ,uCAiBY,KAAA,MACA,MAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAnBZ,6CA0BgB,KAAA,KAQhB,8BAGY,MAAA,QACA,QAAA,MACA,QAAA,KAAA,EACA,cAAA,IAAA,MAAA,QANZ,0CAWgB,YAAA,EAXhB,yCAiBgB,cAAA,EAShB,wBAEQ,QAAA,GACA,SAAA,SACA,OAAA,KACA,YAAA,IAAA,OAAA,QACA,IAAA,KACA,KAAA,EAIR,cACI,YAAA,KADJ,6BAKQ,SAAA,SACA,QAAA,EAAA,EAAA,KAAA,KANR,4CASY,SAAA,SACA,KAAA,MACA,IAAA,EACA,QAAA,EAZZ,wCAgBY,eAAA,EC3HZ,cACI,YAAA,KACA,cAAA,MAFJ,mBAKQ,cAAA,KACA,YAAA,IAMR,gBACI,UAAA,OAKJ,kBACI,SAAA,SACA,IAAA,KACA,MAAA,KACA,OAAA,KACA,KAAA,KACA,QAAA,EACA,QAAA,MAMJ,mBACE,WAAA,OACA,MAAA,QAFF,qBAKI,QAAA,MACA,UAAA,KACA,MAAA,QACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,OAAA,EAAA,KACA,cAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAfJ,6BAmBI,WAAA,KAnBJ,qCAuBQ,iBAAA,QACA,MAAA,KASR,gCAEQ,iBAAA,QACA,WAAA,KACA,UAAA,MACA,YAAA,IACA,QAAA,KAAA,KAOR,YACE,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,cAAA,OACA,QAAA,KACA,SAAA,OACA,cAAA,SACA,YAAA,OAPF,kBAUI,OAAA,QAIJ,kBACE,QAAA,MAIF,kBACE,QAAA,KADF,sCAGI,aAAA,kBAIJ,wBAEM,WAAA,KAFN,+BAIU,MAAA,KACA,OAAA,KACA,YAAA,eACA,UAAA,eACA,cAAA,cACA,iBAAA,8BACA,MAAA,kBACA,OAAA,IAAA,cClHV,ahB4/CE,QADA,eADA,gBADA,WgBx/CE,eAKI,QAAA,eAEJ,WhBu/CF,cAEA,cADA,WAEA,KgBr/CM,QAAA,EACA,OAAA,EAGJ,MACI,OAAA,GhBu5CR,iBiB76CE,SAAA,SACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,cAAA,KAAA,UAAA,KACA,iBAAA,MAAA,cAAA,MAAA,gBAAA,WACA,mBAAA,MAAA,cAAA,WACA,kBAAA,MAAA,eAAA,MAAA,YAAA,WAGF,mBACE,SAAA,OACA,MAAA,QACA,OAAA,QACA,UAAA,QACA,WAAA,QAGF,gBACE,UAAA,QACA,SAAA,SACA,SAAA,OACA,QAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,eACA,OAAA,eACA,QAAA,EAGF,kBACE,UAAA,kBACA,mBAAA,kBAAA,WAAA,kBACA,OAAA,eACA,SAAA,SACA,IAAA,EACA,KAAA,YACA,OAAA,EACA,MAAA,YACA,QAAA,EACA,OAAA,EACA,2BAAA,MAGF,2BACE,UAAA,QACA,mBAAA,qBAAA,WAAA,qBACA,SAAA,SACA,QAAA,MACA,OAAA,KACA,MAAA,KACA,WAAA,QACA,SAAA,KACA,UAAA,KACA,WAAA,KACA,gBAAA,KACA,QAAA,YAGF,8CjBwhDA,6CiBthDE,QAAA,KjB0hDF,yBiBvhDA,0BAEE,QAAA,IACA,QAAA,MAGF,uBACE,WAAA,KACA,UAAA,KACA,MAAA,KACA,eAAA,KAGF,wCACE,mBAAA,kBAAA,WAAA,kBACA,OAAA,KACA,MAAA,KACA,UAAA,IACA,SAAA,SACA,MAAA,KACA,WAAA,IACA,SAAA,OACA,QAAA,GACA,QAAA,EACA,OAAA,EACA,eAAA,KACA,iBAAA,QAAA,kBAAA,QAAA,UAAA,QACA,kBAAA,EAAA,YAAA,EACA,wBAAA,EAAA,WAAA,EAGF,gCACE,mBAAA,QAAA,WAAA,QACA,QAAA,MACA,QAAA,EACA,SAAA,SACA,IAAA,EACA,KAAA,EACA,OAAA,MACA,MAAA,MACA,WAAA,IACA,UAAA,IACA,SAAA,OACA,eAAA,KACA,QAAA,GAGF,iBACE,QAAA,EACA,SAAA,SACA,MAAA,EACA,OAAA,EACA,eAAA,KACA,SAAA,OjB06CF,uDiBt6CE,eAAA,KACA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,oBAAA,KjBy6CF,qDiBr6CE,eAAA,IAGF,qBACE,SAAA,SACA,MAAA,IACA,MAAA,IACA,WAAA,KAGF,4BACE,SAAA,SACA,QAAA,GACA,WAAA,QACA,cAAA,IACA,KAAA,EACA,MAAA,EACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAGF,8CAEE,QAAA,GACA,mBAAA,QAAA,GAAA,OAAA,WAAA,QAAA,GAAA,OAGF,oCACE,IAAA,EACA,MAAA,KAGF,gEACE,IAAA,IACA,OAAA,IAGF,sCACE,KAAA,EACA,OAAA,KAGF,kEACE,OAAA,KACA,KAAA,IACA,MAAA,IAGF,2DACE,MAAA,KACA,KAAA,EACA,IAAA,IACA,OAAA,IACA,WAAA,EACA,UAAA,KACA,MAAA,KjBi6CF,mEiB55CE,MAAA,KACA,KAAA,EAGF,yBACE,UAAA,IACA,SAAA,MACA,QAAA,EACA,WAAA,OACA,OAAA,MACA,MAAA,MACA,WAAA,OACA,WAAA,OAGF,0BACE,SAAA,MACA,KAAA,EACA,WAAA,OACA,WAAA,OACA,gBAAA,KAGF,eACE,OAAA,KCjNF,eAEM,UAAA,KACA,YAAA,KACA,eAAA,UAIN,wBAEM,WAAA,QACA,UAAA,KACA,YAAA,KACA,QAAA,KAAA,EACA,eAAA,UACA,YAAA,IAIN,yBlB4tDA,yBACA,iCACA,2BACA,yBACA,qBACA,mBACA,gBACA,gBACA,mBkBztDM,aAAA,QAXN,yBAcM,WAAA,QAIN,WACE,WAAA,KACA,aAAA,QACA,MAAA,QACA,eAAA,WACA,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,eACA,OAAA,elB4tDF,iBACA,mBkB1tDA,eAGE,iBAAA,QACA,MAAA,KACA,YAAA,KAGF,UACE,cAAA,IACA,OAAA,KACA,OAAA,KACA,UAAA,SACA,OAAA,IAAA,IACA,QAAA,IAAA,IACA,WAAA,OAGF,iCACE,WAAA,eACA,QAAA,IAAA,KAGF,UAAA,cACE,iBAAA,QAGF,sBACE,MAAA,KAGF,uBAAA,uBAGM,aAAA,QAKF,4BARJ,gBASM,QAAA,OATN,mBAaU,UAAA,KACA,YAAA,KACA,eAAA,UAGJ,4BlBgtDJ,2BkBluDF,yBlBiuDE,0BkB1sDY,MAAA,KACA,QAAA,MACA,WAAA,OACA,MAAA,KACA,OAAA,KAAA,EA3Bd,oBA+Bc,MAAA,KA/Bd,iCAmCc,QAAA,MAnCd,qBAwCU,eAAA,WAKV,mCACE,iBAAA,QAGF,+DACE,iBAAA,elB+kDF,6BkB1kDE,UAAA,clB6kDF,2CkBzkDE,YAAA,MCxIF,cACE,iBAAA,KACA,QAAA,KAFF,qBAII,QAAA,OAAA,MACE,UAAA,UACA,cAAA,MACA,YAAA,IACA,MAAA,QARN,uCAWQ,iBAAA,QAXR,+BAeQ,iBAAA,QACA,YAAA,IACA,aAAA,EAKR,sBACE,aAAA,IAAA,MAAA,QAGF,UACE,iBAAA,KACA,aAAA,kBACA,MAAA,QAHF,gBAKI,QAAA,EnB+sDJ,oBmBvsDI,UAAA,InB0sDJ,kDmBrsDM,wBAAA,YACA,2BAAA,YACA,uBAAA,cACA,0BAAA,cnBwsDN,qCmBnsDI,OAAA,IAAA,MAAA,QACA,YAAA,EACA,uBAAA,EACA,0BAAA,EACA,wBAAA,OACA,2BAAA,OC1DJ,+BAEQ,QAAA,KAFR,0CAMQ,MAAA,QACA,YAAA,IAPR,qCAWQ,iBAAA,KACA,MAAA,QACA,mBAAA,KAAA,WAAA,KCbR,KACI,YAAA,0BAGJ,qBrBm4DA,sBACA,wBAFA,oBqB53DQ,WAAA,kBACA,UAAA,KrBk4DR,6BACA,+BqB14DA,2BAcY,QAAA,KAdZ,sBAmBQ,WAAA,QACA,aAAA,QApBR,2BAwBQ,UAAA,KACA,MAAA,QrB63DR,qBqBt5DA,qBA8BQ,MAAA,QACA,WAAA,QACA,UAAA,KAhCR,wBAoCQ,OAAA,IAAA,MAAA,QACA,MAAA,KACA,OAAA,KACA,IAAA,KACA,iBAAA,eC3CR,8BAEI,UAAA,KACA,YAAA,IAIJ,eACE,UAAA,KAGF,2BAEI,aAAA,QACA,MAAA,QAHJ,sDAOM,iBAAA,QAPN,8CAWM,aAAA,oBAXN,0BAeI,aAAA,QACA,MAAA,QAIJ,oBAEI,mBAAA,KAAA,WAAA,KAIJ,2CAEI,WAAA,QAFJ,sEAIM,WAAA,QAJN,2FAAA,gGAMQ,WAAA,mBANR,gDAYI,WAAA,QAIJ,cACE,aAAA,QAAA,YAAA,QAAA,YCtDF,QACE,aAAA,KAGF,0BAAA,0BACE,UAAA,KAGF,0BACE,IAAA,EAGF,kBAEI,QAAA,aACA,eAAA,OAHJ,wBAMM,YAAA,IClBN,OACE,MAAA,QAGF,eACE,aAAA,QAGF,qBACE,QAAA,KACA,OAAA,EACA,QAAA,EAHF,4BAKI,QAAA,MALJ,wBAQI,UAAA,KACA,WAAA,KACA,MAAA,QACA,WAAA,ICnBJ,mBACE,QAAA,MADF,8CAGI,iBAAA,KACA,OAAA,IAAA,MAAA,QACA,OAAA,KALJ,oDAOM,QAAA,EAPN,2EAWM,YAAA,KACA,aAAA,KACA,MAAA,QAbN,wEAiBM,OAAA,KACA,MAAA,KACA,MAAA,IAnBN,0EAsBQ,aAAA,QAAA,YAAA,YAAA,YACA,aAAA,IAAA,IAAA,EAAA,IAvBR,8EA4BM,MAAA,QAKN,gFAMQ,aAAA,YAAA,YAAA,QAAA,sBACA,aAAA,EAAA,IAAA,IAAA,cAMR,sDAEM,QAAA,KACA,iBAAA,KAHN,6EAKU,OAAA,IAAA,MAAA,QACA,iBAAA,KACA,MAAA,QACA,QAAA,EARV,iFAYM,iBAAA,QAZN,yEAeM,iBAAA,QACA,MAAA,QAhBN,+EAkBU,iBAAA,QACA,MAAA,KAKV,yBACE,QAAA,IAAA,KAGF,kBACE,OAAA,IAAA,MAAA,gBACA,iBAAA,KACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBAGF,sBAEI,OAAA,IAAA,MAAA,QAIJ,gDAEI,WAAA,KACA,iBAAA,KACA,OAAA,IAAA,MAAA,kBAJJ,6EAOM,QAAA,IAAA,KAPN,uEAUM,OAAA,EACA,MAAA,QAXN,kGAaU,MAAA,QAbV,yFAaU,MAAA,QAbV,6FAaU,MAAA,QAbV,8FAaU,MAAA,QAbV,oFAaU,MAAA,QAbV,2EAiBM,iBAAA,QACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,QAAA,EAAA,IAKN,kFAGM,aAAA,QAHN,oDAQI,YAAA,IAMJ,mCACI,MAAA,KACA,MAAA,KACA,aAAA,KAHJ,uCAKI,MAAA,KACA,OAAA,KACA,cAAA,IAIJ,uCACE,WAAA,IAGF,kCzBq8DA,uCACA,qCyBn8DE,QAAA,aACA,UAAA,KACA,aAAA,IACA,MAAA,QANF,sCzB48DE,2CACA,yCyBp8DE,aAAA,IATJ,uDzBg9DI,4DACA,0DyBp8DI,QAAA,QACA,YAAA,sBAMR,wEzBk8DA,6EACA,2EyB/7DE,MAAA,qBAIF,iCACE,SAAA,OAMF,UACE,aAAA,IACA,OAAA,KACA,MAAA,KClLF,cACE,QAAA,KADF,oBAGI,UAAA,IACA,YAAA,EACA,MAAA,KACA,OAAA,KACA,iBAAA,QACA,iBAAA,KACA,cAAA,KACA,QAAA,UACA,OAAA,QACA,QAAA,aACA,WAAA,OACA,SAAA,SACA,YAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAhBJ,2BAkBM,MAAA,QACA,QAAA,qBACA,QAAA,MACA,YAAA,QACA,YAAA,IACA,UAAA,KACA,YAAA,KACA,SAAA,SACA,MAAA,IACA,OAAA,IACA,IAAA,KACA,WAAA,OACA,UAAA,WACA,SAAA,OACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAhCN,0BAoCM,QAAA,GACA,SAAA,SACA,KAAA,IACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,cAAA,KACA,OAAA,KACA,MAAA,KACA,IAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YA7CN,4BAkDI,iBAAA,QAIJ,4BACE,iBAAA,QADF,mCAGI,MAAA,KACA,QAAA,oBACA,MAAA,KACA,KAAA,IANJ,kCAUI,KAAA,KACA,iBAAA,QAIJ,yBACE,iBAAA,QAEF,gCAAA,wC1BymEA,2C0BvmEE,MAAA,KAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,mCACE,iBAAA,QAGF,iCACE,iBAAA,QAGF,eACE,aAAA,IADF,mCAAA,yCAGI,cAAA,IChHJ,YACE,OAAA,IAAA,MAAA,QACA,QAAA,IACA,QAAA,cAHF,wBAOQ,YAAA,IAPR,yCAAA,+BAAA,8CAAA,qCAAA,iCAAA,0CAAA,gDAAA,uCAAA,8BAAA,uCAAA,6CAAA,oCAaU,iBAAA,kBACA,iBAAA,KACA,mBAAA,KAAA,WAAA,KACA,MAAA,e3B8sER,qCACA,mC2B/tEF,oCAAA,kCAuBY,WAAA,Q3B2sEV,iCACA,iC2BnuEF,4BAAA,4BA8BY,MAAA,QACA,QAAA,GA/BZ,8BAAA,uCAAA,6CAAA,oCAmCY,iBAAA,QAOZ,6BAAA,6BAEI,QAAA,IC3CJ,2DAAA,wEAIQ,wBAAA,EACA,2BAAA,EALR,0DAAA,uEAaU,uBAAA,EACA,0BAAA,EAOR,+CAEI,MAAA,YACF,KAAA,eAHF,6CAMI,wBAAA,cACA,2BAAA,YACA,uBAAA,YACA,0BAAA,YATJ,+CAYI,wBAAA,YACF,2BAAA,cACA,uBAAA,YACA,0BAAA,YCvCJ,gBACE,OAAA,IAAA,MAAA,QAIF,6CAEI,WAAA,MAFJ,mDAIM,YAAA,KACA,aAAA,ECPN,aACI,OAAA,IAAA,MAAA,kBAGJ,oBAEQ,WAAA,IAAA,MAAA,kB9B6wER,4B8B/wEA,kB9BgxEA,oB8BxwEQ,iBAAA,eACA,WAAA,cATR,eAaQ,MAAA,kBAbR,2DAgBY,iBAAA,kBAhBZ,qBAsBY,iBAAA,kBAtBZ,2BA2BQ,aAAA,kBA3BR,kB9BgyEA,4BACA,2B8BhwEQ,WAAA,kBAjCR,eAqCQ,MAAA,kBArCR,mBAwCY,KAAA,kBAxCZ,4BA6CQ,iBAAA,eA7CR,sB9B6yEA,+BACA,+B8B3vEQ,MAAA,kBAnDR,2DAuDQ,aAAA,IAAA,MAAA,kBAGR,iBACI,QAAA,eC/DJ,UACE,WAAA,MACA,OAAA,IAAA,OAAA,QACA,WAAA,KACA,cAAA,IAJF,sBAOI,UAAA,KACA,MAAA,KCTJ,0CAGQ,SAAA,SAHR,gEASY,QAAA,GACA,MAAA,MACA,OAAA,IACA,WAAA,mBACA,SAAA,SACA,IAAA,KACA,YAAA,MAfZ,uDAsBY,QAAA,aACA,cAAA,KACA,QAAA,IAAA,EACA,MAAA,MACA,YAAA,KACA,MAAA,QACA,WAAA,OACA,SAAA,SACA,iBAAA,mBAEA,4BAhCZ,uDAiCgB,QAAA,MACA,OAAA,EAAA,KAAA,cACA,MAAA,OAnChB,gEAyCgB,QAAA,MACA,WAAA,IACA,YAAA,IAEA,4BA7ChB,gEA8CoB,QAAA,MA9CpB,2DAmDgB,iBAAA,YACA,MAAA,QApDhB,wEAuDoB,iBAAA,QACA,MAAA,KAxDpB,iDA+DQ,YAAA,KACA,aAAA,EACA,WAAA,KACA,cAAA,EAlER,oDAqEY,QAAA,aArEZ,sDAwEgB,QAAA,aACA,QAAA,OAAA,OACA,iBAAA,QACA,MAAA,KACA,cAAA,OA5EhB,+DAiFoB,OAAA,YACA,iBAAA,QAlFpB,yDAuFgB,MAAA,MAMhB,+BACI,YAAA,KACA,WAAA,MAIJ,0BACI,gEAIgB,WAAA,eCxGpB,+BAEI,QAAA,MAFJ,oCAKI,OAAA,eALJ,0CASM,iBAAA,QACA,MAAA,QACA,OAAA,IAAA,MAAA,QAXN,sDAaU,iBAAA,QACA,aAAA,QACA,MAAA,KACA,mBAAA,EAAA,EAAA,EAAA,IAAA,mBAAA,WAAA,EAAA,EAAA,EAAA,IAAA,mBAhBV,wCAoBM,MAAA,MApBN,uDAsBQ,MAAA,EACA,kBAAA,eAAA,UAAA,eACA,IAAA,eAxBR,2BA8BM,UAAA,KACA,YAAA,IA/BN,gCAoCI,aAAA,KACA,MAAA,kBArCJ,sCAwCM,iBAAA,kBAxCN,sCA4CM,QAAA,aACA,aAAA,IACA,SAAA,SA9CN,8CAgDQ,cAAA,IAAA,YACA,mBAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,YA7DR,6CAgEQ,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,KA1ER,qDA8EM,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAjFN,oEAoFQ,QAAA,IApFR,yEAyFQ,eAAA,KACA,QAAA,EA1FR,0EA+FQ,QAAA,QACA,YAAA,sBACA,YAAA,IAjGR,4EAsGQ,iBAAA,QACA,OAAA,YAvGR,2EA4GQ,iBAAA,QACA,aAAA,QA7GR,0EAgHQ,MAAA,KAhHR,uDAuHM,IAAA,eACA,iBAAA,QAxHN,6DA0HQ,MAAA,KjC82ER,uCADA,qDiCv+EA,qDAkIM,WAAA,QACA,aAAA,QACA,MAAA,KjC02EJ,6CADA,2DiC7+EF,2DAuIU,MAAA,KAMR,yBADF,kEAIQ,IAAA,iBAMR,iDAIQ,mBAAA,eAAA,WAAA,eAJR,oDAAA,oDAMU,mBAAA,eAAA,WAAA,eC5JV,mBAAA,oBAEI,OAAA,0BACA,QAAA,OAAA,MACA,OAAA,IAAA,MAAA,QACA,iBAAA,KACA,MAAA,QACA,cAAA,OAPJ,yBAAA,0BASM,QAAA,EACA,aAAA,QCXN,aACI,WAAA,eADJ,kBAGQ,YAAA,oCACA,KAAA,QAJR,gCAOQ,OAAA,EAAA,KnC0gFR,yBmCtgFA,0BAEI,YAAA,oCAGJ,0BACI,YAAA,IAGJ,qBACI,eAAA,KACA,OAAA,QAGJ,wBACI,MAAA,kBACA,YAAA,oCACA,UAAA,eAGJ,sBACI,KAAA,enCogFJ,uBmCjgFA,uBAGQ,YAAA,oCACA,KAAA,QCvCR,0BACI,MAAA,KAGJ,UACI,WAAA,MADJ,oBAIQ,KAAA,QACA,MAAA,QACA,UAAA,KACA,YAAA,EAIR,8CAEQ,MAAA,KACA,KAAA,KACA,UAAA,KAIR,SACI,OAAA,kBAGJ,yCpCiiFA,0CACA,2CACA,iDoC5hFgB,OAAA,QAPhB,yCpCuiFA,0CACA,2CACA,iDoC1hFgB,OAAA,QAfhB,yCpC6iFA,0CACA,2CACA,iDoCxhFgB,OAAA,QAvBhB,yCpCmjFA,0CACA,2CACA,iDoCthFgB,OAAA,QA/BhB,yCpCyjFA,0CACA,2CACA,iDoCphFgB,OAAA,QAvChB,yCpC+jFA,0CACA,2CACA,iDoClhFgB,OAAA,QA/ChB,yCpCqkFA,0CACA,2CACA,iDoChhFgB,OAAA,QAOhB,sBpC6gFA,2BoC1gFQ,KAAA,QAIR,sBpC0gFA,2BoCvgFQ,KAAA,QAIR,sBpCugFA,2BoCpgFQ,KAAA,QAIR,sBpCogFA,2BoCjgFQ,KAAA,QAIR,SACI,aAAA,IAGJ,kBACI,SAAA,SACA,QAAA,aACA,QAAA,EACA,UAAA,KACA,QAAA,IAAA,KACA,cAAA,IACA,WAAA,QACA,MAAA,QACA,WAAA,OACA,eAAA,KACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAZJ,+BAcQ,QAAA,EAIR,SACI,aAAA,IAGJ,UACI,aAAA,IChJJ,SACE,QAAA,IAAA,eACA,iBAAA,kBACA,OAAA,IAAA,MAAA,kBACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBACA,QAAA,IACA,MAAA,QACA,QAAA,EACA,cAAA,cACA,UAAA,eAGF,YAEM,iBAAA,sBAFN,WAKM,OAAA,KAIN,aACE,aAAA,IACA,YAAA,KACA,cAAA,KACA,UAAA,KACA,YAAA,IACA,MAAA,QAGF,oBAEM,cAAA,IAFN,wBAIU,cAAA,IAKV,uBAEM,OAAA,EAAA,KjC+BF,4BiCzBF,aACI,QAAA,MChDN,YACE,mBAAA,YAAA,WAAA,YACA,MAAA,eACA,OAAA,eACA,iBAAA,kBACA,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,QAAA,IAAA,eACA,cAAA,IACA,aAAA,kBAGF,UACE,MAAA,kBACA,UAAA,eACA,YAAA,eACA,YAAA,oCACA,YAAA,cCfF,OAAA,gBACE,OAAA,MACA,WAAA,QACA,cAAA,IAGF,eACE,QAAA,MACA,WAAA,OACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,QACA,cAAA,IACA,QAAA,KAAA,KAGF,qBACE,KAAA,IACA,YAAA,MACA,MAAA,EACA,OAAA,EACA,SAAA,SALF,2BAOI,OAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QAVJ,2BAaI,IAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,cAAA,KAAA,MAAA,QClCJ,kBACI,OAAA,KACA,WAAA,QACA,MAAA,QACA,YAAA,0BACA,UAAA,QACA,QAAA,IAAA,ICNJ,8BAEM,QAAA,aAIJ,kBACE,YAAA,IADF,mCAGI,YAAA,ICTN,UACI,SAAA,SACA,IAAA,KACA,MAAA,KAKJ,aACI,QAAA,MACA,MAAA,KACA,OAAA,KAGF,kBACE,QAAA,WACA,eAAA,OAGJ,mBACI,iBAAA,6BACA,OAAA,MACA,gBAAA,MACA,oBAAA,OAGJ,+BACI,iBAAA,QAOJ,YACI,eAAA,UACA,WAAA,gFACA,wBAAA,KACA,wBAAA,YACA,UAAA,MACA,YAAA,GACA,SAAA,SAOJ,YAEQ,MAAA,KACA,OAAA,KACA,YAAA,KACA,OAAA,IAAA,MACA,cAAA,IACA,WAAA,OACA,MAAA,MACA,UAAA,KACA,QAAA,aAVR,gBAaY,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,KACA,QAAA,GACA,MAAA,KACA,WAAA,MACA,cAAA,IACA,WAAA,QCnEZ,0BAEQ,cAAA,KACA,aAAA,KACA,OAAA,IAAA,MAAA,QAJR,yBAOQ,UAAA,KACA,SAAA,SACA,KAAA,KACA,IAAA,EACA,YAAA,KAKR,uBACI,QAAA,MACA,MAAA,QACA,YAAA,IACA,QAAA,IAAA,KAJJ,2CAOQ,iBAAA,QAPR,kCAWQ,OAAA,EAXR,6BAeQ,MAAA,QAIR,iBACI,QAAA,IAAA,EADJ,sBAIY,QAAA,MACA,QAAA,IAAA,KACA,MAAA,QANZ,6BAWgB,MAAA,QAUhB,mCAGY,OAAA,IAAA,EAHZ,0CAMgB,iBAAA,QAMhB,iBAEQ,QAAA,aACA,WAAA,OACA,MAAA,QAJR,qCAOY,OAAA,IACA,OAAA,IAAA,MAAA,QACA,cAAA,IATZ,wBAAA,uBAYY,MAAA,QAZZ,4CAAA,2CAcgB,aAAA,kBAMhB,eACI,OAAA,IAAA,MAAA,QAGJ,0BAEQ,MAAA,QAFR,4BAIY,MAAA,QACA,QAAA,IALZ,mCAUgB,MAAA,QAMhB,aACI,SAAA,SADJ,6BAIQ,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,IAAA,IACA,cAAA,IAAA,IAAA,IAAA,GAAA,CAAA,IAAA,IAAA,IAAA,IACA,MAAA,KACA,OAAA,KACA,MAAA,KACA,UAAA,KACA,WAAA,OAbR,2BAiBQ,SAAA,SACA,IAAA,EACA,MAAA,EAnBR,6BAqBY,QAAA,aACA,MAAA,KACA,OAAA,KACA,OAAA,IAAA,MAAA,QACA,YAAA,KACA,cAAA,IACA,WAAA,OACA,MAAA,QAKZ,qCAGY,cAAA,IAHZ,4CAKgB,iBAAA,QALhB,8CAUgB,MAAA,KAVhB,6BAgBQ,OAAA,IAAA,MAAA,QACA,QAAA,KAIR,sBAEQ,QAAA,IAAA,EAIR,yCAGY,MAAA,QAHZ,wDAMY,aAAA,KAOZ,wBACI,OAAA,IAAA,MAAA,QACA,iBAAA,KACA,cAAA,OAHJ,sCAKQ,aAAA,YACA,OAAA,KANR,8CAUQ,iBAAA,sBACA,aAAA,sBACA,MAAA,kBACA,UAAA,KACA,QAAA,IAAA,KACA,mBAAA,KAAA,WAAA,KAOR,kBACI,mBAAA,KAAA,WAAA,KADJ,yBAGQ,aAAA,kBAIR,2CAGQ,QAAA,GACA,MAAA,MACA,OAAA,IACA,WAAA,mBACA,SAAA,SACA,IAAA,KACA,YAAA,MATR,8CAeQ,QAAA,GACA,MAAA,MACA,OAAA,IACA,WAAA,mBACA,SAAA,SACA,IAAA,KACA,YAAA,MAKR,0BACI,uCAAA,oCAGQ,MAAA,MAHR,8CAAA,2CAKY,WAAA,eCrPhB,eACE,MAAA,MACA,MAAA,KACA,QAAA,KACA,cAAA,IAGF,gBACE,YAAA,MAGF,4BAEI,MAAA,QACA,YAAA,IAHJ,iBAMI,UAAA,KAIJ,yBACE,eACE,MAAA,KACA,MAAA,KAEF,gBACE,OAAA,GAKJ,aAEI,QAAA,MACA,MAAA,QACA,YAAA,KACA,QAAA,IAAA,IALJ,oBAOM,MAAA,QACA,YAAA,IAKN,cACE,QAAA,MACA,aAAA,EAFF,iBAKI,SAAA,SACA,QAAA,MACA,OAAA,KACA,YAAA,KACA,OAAA,QACA,4BAAA,IAAA,oBAAA,IAVJ,mBAaM,MAAA,QAbN,uBAiBM,WAAA,QACA,4BAAA,KAAA,oBAAA,KAlBN,2BAsBM,MAAA,KACA,SAAA,SAvBN,6BA2BM,MAAA,M5C67FA,oDACA,kC4Cz9FN,0CAgCQ,QAAA,MACA,MAAA,KAjCR,kCAqCQ,OAAA,IAAA,MAAA,YACA,cAAA,MACA,OAAA,KAAA,KAAA,EACA,OAAA,EACA,MAAA,EACA,YAAA,EACA,UAAA,EA3CR,oDA+CQ,OAAA,KAAA,KAAA,EAAA,KA/CR,0CAmDQ,WAAA,KACA,YAAA,IApDR,oCAwDQ,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,cAAA,SACA,SAAA,OACA,YAAA,OACA,cAAA,EA/DR,6BAoEM,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,OAAA,E5Ci7FA,mC4Cz/FN,sCA4EQ,SAAA,SACA,IAAA,EA7ER,sCAiFQ,KAAA,EACA,MAAA,MACA,cAAA,SACA,SAAA,OACA,YAAA,OArFR,mCAyFQ,MAAA,EACA,MAAA,MACA,aAAA,KA3FR,wBAAA,8BAiGM,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QAjGN,wBAsGI,iBAAA,QACA,YAAA,IACA,MAAA,QAxGJ,0BA0GQ,MAAA,QACA,YAAA,IA3GR,qCAkHI,OAAA,QACA,OAAA,KACA,MAAA,KACA,SAAA,SACA,QAAA,aACA,mBAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QAAA,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,QACA,cAAA,IAxHJ,2CA2HM,QAAA,EACA,OAAA,QA5HN,yDA+HM,QAAA,EA/HN,2CAmIM,SAAA,SACA,OAAA,KACA,MAAA,KACA,KAAA,EACA,OAAA,QACA,QAAA,EACA,cAAA,EACA,4BAAA,KAAA,oBAAA,KACA,IAAA,EA3IN,kDA6IQ,QAAA,SACA,YAAA,wBACA,IAAA,EACA,OAAA,KACA,MAAA,QACA,MAAA,KACA,SAAA,SACA,WAAA,MACA,KAAA,IACA,UAAA,KAMR,4BACE,6BACI,MAAA,OCzMF,yBADJ,kBAEM,UAAA,OAFN,6CAQQ,iBAAA,KAON,kCAGM,QAAA,GACA,SAAA,SACA,MAAA,IACA,OAAA,IACA,iBAAA,QACA,cAAA,IACA,MAAA,EATN,yBAcI,QAAA,IACA,mBAAA,KAAA,WAAA,KACA,UAAA,KAIJ,WACE,OAAA,EADF,uBAKQ,iBAAA,KACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBANR,gBAUM,QAAA,MACA,QAAA,KAAA,KACA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,WAAA,KAhBN,sBAkBQ,iBAAA,KACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBAQR,kCAGM,OAAA,KACA,MAAA,KACA,YAAA,KACA,mBAAA,KAAA,WAAA,KACA,QAAA,EACA,UAAA,KACA,iBAAA,QACA,cAAA,IAVN,wCAcM,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBACA,OAAA,IAAA,MAAA,QAMN,sBAEI,MAAA,KAFJ,mCAMI,SAAA,SACA,WAAA,OACA,cAAA,KARJ,0CAWM,iBAAA,KACA,SAAA,SACA,QAAA,EACA,QAAA,IAAA,KAdN,0CAkBM,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,IACA,KAAA,EACA,MAAA,EACA,iBAAA,QACA,IAAA,KAzBN,0CA4BM,UAAA,KA5BN,sCAgCI,cAAA,KACA,QAAA,aACA,SAAA,SAlCJ,kDAoCM,SAAA,SApCN,yDAsCQ,QAAA,GACH,SAAA,SACA,IAAA,KACA,MAAA,KACA,OAAA,IAAA,MAAA,YACA,aAAA,IAAA,MAAA,mBA3CL,kDAgDM,QAAA,KAAA,KACA,iBAAA,mBACA,cAAA,IAAA,IAAA,IAAA,EACA,SAAA,OAnDN,qEAuDQ,YAAA,IACA,MAAA,QACA,cAAA,IACA,SAAA,SA1DR,gDAgEM,MAAA,MAhEN,iEAkEQ,UAAA,KACA,QAAA,IACA,MAAA,QACA,4BArER,iEAsEU,QAAA,MAtEV,+DA2EQ,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBACA,OAAA,IAAA,MAAA,QA5ER,iDAiFM,UAAA,KAjFN,6CAuFM,MAAA,MAvFN,0DAyFQ,SAAA,SAzFR,iEA2FU,QAAA,GACH,SAAA,SACA,IAAA,KACA,KAAA,KACA,OAAA,IAAA,MAAA,YACA,YAAA,IAAA,MAAA,QAhGP,yDAoGQ,iBAAA,QACA,WAAA,MACA,cAAA,IAAA,IAAA,EAAA,IAtGR,uDAyGQ,MAAA,KAzGR,iFA+GY,MAAA,EACA,KAAA,KAWZ,oBACE,WAAA,IAAA,MAAA,QAGF,YACE,cAAA,KACA,iBAAA,kBACA,aAAA,kBACA,cAAA,MAGF,kBACE,SAAA,SACA,MAAA,KACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBAJF,uBAOM,UAAA,KACA,YAAA,KACA,QAAA,EAAA,IACA,QAAA,aAMJ,4BADF,WAEI,UAAA,MAKJ,yBACE,UAAA,KACA,SAAA,SACA,KAAA,KACA,IAAA,IACA,UAAA,KACA,YAAA,KAIF,0BACE,aAAA,KACA,cAAA,ICzPJ,gBACI,UAAA,KACA,WAAA,OAFJ,qBAIQ,UAAA,KACA,QAAA,MACA,YAAA,IAIR,YACI,MAAA,KACA,MAAA,IACA,QAAA,KAAA,IACA,OAAA,EAAA,KAAA,KAAA,KACA,iBAAA,KACA,cAAA,IACA,cAAA,OACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBAGJ,4BACI,YACI,MAAA,KCvBR,UACI,SAAA,SACA,MAAA,KACA,QAAA,KAAA,EAGF,wB/C8yGF,0BACA,yB+C5yGI,SAAA,SACA,MAAA,KACA,WAAA,OACA,QAAA,EAGF,0B/C6yGF,4BACA,2B+C3yGI,QAAA,aACA,MAAA,KACA,OAAA,KACA,OAAA,EACA,QAAA,KAAA,EACA,WAAA,OACA,WAAA,4BACA,iBAAA,QACA,kBAAA,UACA,gBAAA,MACA,cAAA,MACA,MAAA,KACA,eAAA,UAGF,yBAEI,OAAA,KAAA,EAFJ,6BAKI,SAAA,SACA,MAAA,KACA,QAAA,KAAA,EAPJ,mCASM,SAAA,SACA,QAAA,GACA,MAAA,IACA,OAAA,KACA,IAAA,EACA,KAAA,IACA,YAAA,KACA,WAAA,QAhBN,yBAoBI,OAAA,KAAA,KAAA,EAAA,KAIJ,6B/CwyGF,6C+CtyGI,WAAA,M/C0yGJ,4C+CvyGE,8BAEE,WAAA,KAGF,gCACE,QAAA,GACA,QAAA,MACA,SAAA,SACA,MAAA,KACA,OAAA,KACA,IAAA,KACA,WAAA,QACA,cAAA,KACA,QAAA,EAGF,mDACE,KAAA,KAGF,oDACE,MAAA,KAGF,wB/CmyGF,2B+CjyGI,SAAA,SACA,QAAA,aACA,OAAA,KACA,QAAA,KACA,OAAA,IAAA,MAAA,QACA,cAAA,IAGF,2BACE,MAAA,KACA,OAAA,KAAA,EACA,QAAA,EACA,OAAA,KACA,WAAA,OACA,WAAA,IAGF,+B/CiyGF,gC+C/xGI,QAAA,GACA,QAAA,MACA,SAAA,SACA,MAAA,EACA,OAAA,EACA,aAAA,MAGF,kD/CgyGF,mD+C9xGI,KAAA,KAGF,mD/C+xGF,oD+C7xGI,MAAA,KAGF,gD/C8xGF,iD+C5xGI,KAAA,IACA,YAAA,MAGF,+BACE,IAAA,KACA,aAAA,YAAA,YAAA,YAAA,QACA,aAAA,KAGF,gCACE,IAAA,KACA,aAAA,YAAA,YAAA,YAAA,QACA,aAAA,KAGF,mDACE,aAAA,YAAA,QAAA,YAAA,YAGF,oDACE,aAAA,YAAA,QAAA,YAAA,YAGF,gDACE,IAAA,MACA,aAAA,YAAA,YAAA,QAAA,YAGF,iDACE,IAAA,MACA,aAAA,YAAA,YAAA,QAAA,YACA,aAAA,KACA,QAAA,EAGF,0CACE,MAAA,KAGF,yBACE,oCACE,KAAA,K/CyxGJ,6BAGA,4CADA,8BADA,6C+CvxGE,wB/C0xGF,2BANA,0BACA,yB+C7wGI,WAAA,KAGF,mD/CkxGF,oD+ChxGI,KAAA,K/CoxGJ,4CADA,6C+ChxGE,wBAGE,YAAA,KAGF,yCACE,YAAA,EAGF,kDACE,KAAA,MACA,aAAA,YAAA,QAAA,YAAA,YAGF,mDACE,KAAA,MACA,aAAA,YAAA,QAAA,YAAA,YAGF,gD/CwwGF,iD+CtwGI,KAAA,KACA,YAAA,GCnNN,kBACI,QAAA,aACA,iBAAA,KACA,mBAAA,EAAA,IAAA,IAAA,qBAAA,WAAA,EAAA,IAAA,IAAA,qBACA,QAAA,IACA,cAAA,IALJ,qBAOQ,QAAA,aAKR,+BAGY,QAAA,IAAA,EASZ,wBAEQ,OAAA,EAAA,IAFR,wBAKQ,WAAA,OACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,MAAA,QARR,kCAUY,UAAA,KACA,cAAA,IACA,QAAA,MAZZ,+BAgBY,aAAA,QACA,iBAAA,YACA,MAAA,QAlBZ,yCAqBgB,MAAA,QAMhB,YACI,UAAA,MACA,4BAFJ,YAGQ,UAAA,MAIR,YACI,MAAA,QACA,SAAA,SAFJ,uBAKQ,SAAA,SACA,MAAA,MACA,KAAA,MACA,MAAA,EACA,OAAA,KAEA,4BAXR,uBAYY,MAAA,KACA,KAAA,MACA,OAAA", "file": "app.min.css", "sourcesContent": ["//\r\n// Google font - Inter\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');", "// \r\n// _header.scss\r\n// \r\n\r\n#page-topbar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: 1001;\r\n    background-color: $header-bg;\r\n}\r\n\r\n.navbar-header {\r\n    display: flex;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    height: $header-height;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2) 0 0;\r\n    box-shadow: $box-shadow;\r\n\r\n    .dropdown.show {\r\n        .header-item {\r\n            background-color: $gray-100;\r\n        }\r\n    }\r\n}\r\n\r\n.navbar-brand-box {\r\n    padding: 0 1.5rem;\r\n    text-align: center;\r\n    width: $navbar-brand-box-width;\r\n}\r\n\r\n.logo {\r\n    line-height: 70px;\r\n\r\n    .logo-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.logo-dark {\r\n    display: $display-block;\r\n}\r\n\r\n.logo-light {\r\n    display: $display-none;\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .mdi-fullscreen::before {\r\n            content: '\\F0294';\r\n        }\r\n    }\r\n}\r\n\r\n// page-title-box\r\n\r\n\r\n.page-content-wrapper{\r\n    margin-top: -90px;\r\n}\r\n\r\n\r\n.page-title-box{\r\n    background: url(../images/title-img.png);\r\n    background-position: center;\r\n    background-color: $primary;\r\n    margin: 0 -24px 23px -24px;\r\n    padding: 24px 24px 92px 24px;\r\n    color: $white;\r\n    background-size: cover;\r\n}\r\n\r\n\r\n/* Search */\r\n.search-wrap {\r\n    background-color: lighten($card-bg, 4%);\r\n    color: $dark;\r\n    z-index: 9997;\r\n    position: absolute;\r\n    top: 0;\r\n    display: flex;\r\n    width: 100%;\r\n    right: 0;\r\n    height: 70px;\r\n    padding: 0 15px;\r\n    transform: translate3d(0, -100%, 0);\r\n    transition: .3s;\r\n  \r\n    form {\r\n      display: flex;\r\n      width: 100%;\r\n    }\r\n    .search-bar {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n    }\r\n    .search-input {\r\n      flex: 1 1;\r\n      border: none;\r\n      outline: none;\r\n      box-shadow: none;\r\n      background-color: transparent;\r\n    }\r\n    .close-search {\r\n      width: 36px;\r\n      height: 64px;\r\n      line-height: 64px;\r\n      text-align: center;\r\n      color: inherit;\r\n      font-size: 24px;\r\n  \r\n      &:hover {\r\n        color: $danger;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .search-wrap.open {\r\n    transform: translate3d(0, 0, 0);\r\n  }\r\n\r\n// Mega menu\r\n\r\n.megamenu-list {\r\n    li{\r\n        position: relative;\r\n        padding: 5px 0px;\r\n        a{\r\n            color: $dropdown-color;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n\r\n    #page-topbar {\r\n        left: 0;\r\n    }\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n\r\n    .logo {\r\n\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.page-content {\r\n    padding: calc(#{$header-height}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n}\r\n\r\n.header-item {\r\n    height: $header-height;\r\n    box-shadow: none !important;\r\n    color: $header-item-color;\r\n    border: 0;\r\n    border-radius: 0px;\r\n\r\n    &:hover {\r\n        color: $header-item-color;\r\n    }\r\n}\r\n\r\n.header-profile-user {\r\n    height: 36px;\r\n    width: 36px;\r\n    background-color: $gray-300;\r\n    padding: 3px;\r\n}\r\n\r\n.noti-icon {\r\n    i {\r\n        font-size: 24px;\r\n        color: $header-item-color;\r\n    }\r\n\r\n    .badge {\r\n        position: absolute;\r\n        top: 20px;\r\n        right: 6px;\r\n    }\r\n}\r\n\r\n.notification-item {\r\n    .media {\r\n        padding: 0.75rem 1rem;\r\n\r\n        &:hover {\r\n            background-color: $gray-300;\r\n        }\r\n    }\r\n}\r\n\r\n// Dropdown with Icons\r\n.dropdown-icon-item {\r\n    display: block;\r\n    border-radius: 3px;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    padding: 15px 0 9px;\r\n    display: block;\r\n    border: 1px solid transparent;\r\n    color: $gray-600;\r\n\r\n    img {\r\n        height: 24px;\r\n    }\r\n\r\n    span {\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n    }\r\n\r\n    &:hover {\r\n        border-color: $gray-200;\r\n    }\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-toggle=\"fullscreen\"] {\r\n        .bx-fullscreen::before {\r\n            content: \"\\ea3f\";\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-topbar=\"dark\"] {\r\n    #page-topbar { \r\n        background-color: $header-dark-bg;\r\n    }\r\n    .navbar-header {\r\n        .dropdown.show {\r\n            .header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: $header-dark-item-color;\r\n    \r\n        &:hover {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: $header-dark-item-color;\r\n        }\r\n    }\r\n\r\n    .title-tooltip{\r\n        li{\r\n           i{\r\n            color: $header-dark-item-color;\r\n           }\r\n        }\r\n    }\r\n\r\n  \r\n\r\n  \r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .navbar-brand-box {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .navbar-header {\r\n        .dropdown {\r\n            position: static;\r\n\r\n            .dropdown-menu {\r\n                left: 10px !important;\r\n                right: 10px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 380px) {\r\n\r\n  \r\n    .navbar-brand-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n    .page-content {\r\n        margin-top: $header-height;\r\n        padding: calc(36px + #{$grid-gutter-width}) calc(#{$grid-gutter-width} / 2) $footer-height calc(#{$grid-gutter-width} / 2);\r\n    }    \r\n}\r\n\r\n@media (max-width: 992px) { \r\n    body[data-layout=\"horizontal\"] {\r\n        .page-content {\r\n            margin-top: 10px;\r\n        }    \r\n    }\r\n}", "/*\r\nTemplate Name: <PERSON><PERSON> -  <PERSON><PERSON> & Dashboard Template\r\nAuthor: Themesdesign\r\nVersion: 1.0.0\r\nContact: <EMAIL>\r\nFile: Main Css File\r\n*/\n@import url(\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap\");\n#page-topbar {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1001;\n  background-color: #ffffff; }\n\n.navbar-header {\n  display: flex;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 auto;\n  height: 70px;\n  padding: 0 calc(24px / 2) 0 0;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n  .navbar-header .dropdown.show .header-item {\n    background-color: #f9fafc; }\n\n.navbar-brand-box {\n  padding: 0 1.5rem;\n  text-align: center;\n  width: 260px; }\n\n.logo {\n  line-height: 70px; }\n  .logo .logo-sm {\n    display: none; }\n\n.logo-dark {\n  display: block; }\n\n.logo-light {\n  display: none; }\n\n.fullscreen-enable [data-toggle=\"fullscreen\"] .mdi-fullscreen::before {\n  content: '\\F0294'; }\n\n.page-content-wrapper {\n  margin-top: -90px; }\n\n.page-title-box {\n  background: url(../images/title-img.png);\n  background-position: center;\n  background-color: #525ce5;\n  margin: 0 -24px 23px -24px;\n  padding: 24px 24px 92px 24px;\n  color: #fff;\n  background-size: cover; }\n\n/* Search */\n.search-wrap {\n  background-color: white;\n  color: #343a40;\n  z-index: 9997;\n  position: absolute;\n  top: 0;\n  display: flex;\n  width: 100%;\n  right: 0;\n  height: 70px;\n  padding: 0 15px;\n  transform: translate3d(0, -100%, 0);\n  transition: .3s; }\n  .search-wrap form {\n    display: flex;\n    width: 100%; }\n  .search-wrap .search-bar {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    width: 100%; }\n  .search-wrap .search-input {\n    flex: 1 1;\n    border: none;\n    outline: none;\n    box-shadow: none;\n    background-color: transparent; }\n  .search-wrap .close-search {\n    width: 36px;\n    height: 64px;\n    line-height: 64px;\n    text-align: center;\n    color: inherit;\n    font-size: 24px; }\n    .search-wrap .close-search:hover {\n      color: #f14e4e; }\n\n.search-wrap.open {\n  transform: translate3d(0, 0, 0); }\n\n.megamenu-list li {\n  position: relative;\n  padding: 5px 0px; }\n  .megamenu-list li a {\n    color: #495057; }\n\n@media (max-width: 992px) {\n  #page-topbar {\n    left: 0; }\n  .navbar-brand-box {\n    width: auto; }\n  .logo span.logo-lg {\n    display: none; }\n  .logo span.logo-sm {\n    display: inline-block; } }\n\n.page-content {\n  padding: calc(70px) calc(24px / 2) 60px calc(24px / 2); }\n\n.header-item {\n  height: 70px;\n  box-shadow: none !important;\n  color: #636e75;\n  border: 0;\n  border-radius: 0px; }\n  .header-item:hover {\n    color: #636e75; }\n\n.header-profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: #eaedf1;\n  padding: 3px; }\n\n.noti-icon i {\n  font-size: 24px;\n  color: #636e75; }\n\n.noti-icon .badge {\n  position: absolute;\n  top: 20px;\n  right: 6px; }\n\n.notification-item .media {\n  padding: 0.75rem 1rem; }\n  .notification-item .media:hover {\n    background-color: #eaedf1; }\n\n.dropdown-icon-item {\n  display: block;\n  border-radius: 3px;\n  line-height: 34px;\n  text-align: center;\n  padding: 15px 0 9px;\n  display: block;\n  border: 1px solid transparent;\n  color: #74788d; }\n  .dropdown-icon-item img {\n    height: 24px; }\n  .dropdown-icon-item span {\n    display: block;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap; }\n  .dropdown-icon-item:hover {\n    border-color: #edf1f5; }\n\n.fullscreen-enable [data-toggle=\"fullscreen\"] .bx-fullscreen::before {\n  content: \"\\ea3f\"; }\n\nbody[data-topbar=\"dark\"] #page-topbar {\n  background-color: #1f293f; }\n\nbody[data-topbar=\"dark\"] .navbar-header .dropdown.show .header-item {\n  background-color: rgba(255, 255, 255, 0.05); }\n\nbody[data-topbar=\"dark\"] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\nbody[data-topbar=\"dark\"] .header-item {\n  color: #a3acc1; }\n  body[data-topbar=\"dark\"] .header-item:hover {\n    color: #a3acc1; }\n\nbody[data-topbar=\"dark\"] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25); }\n\nbody[data-topbar=\"dark\"] .noti-icon i {\n  color: #a3acc1; }\n\nbody[data-topbar=\"dark\"] .title-tooltip li i {\n  color: #a3acc1; }\n\nbody[data-topbar=\"dark\"] .app-search .form-control {\n  background-color: rgba(241, 245, 247, 0.07);\n  color: #fff; }\n\nbody[data-topbar=\"dark\"] .app-search span,\nbody[data-topbar=\"dark\"] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-sidebar=\"dark\"] .navbar-brand-box {\n  background: #1f293f; }\n\nbody[data-sidebar=\"dark\"] .logo-dark {\n  display: none; }\n\nbody[data-sidebar=\"dark\"] .logo-light {\n  display: block; }\n\n@media (max-width: 600px) {\n  .navbar-header .dropdown {\n    position: static; }\n    .navbar-header .dropdown .dropdown-menu {\n      left: 10px !important;\n      right: 10px !important; } }\n\n@media (max-width: 380px) {\n  .navbar-brand-box {\n    display: none; } }\n\nbody[data-layout=\"horizontal\"] .navbar-brand-box {\n  width: auto; }\n\nbody[data-layout=\"horizontal\"] .page-content {\n  margin-top: 70px;\n  padding: calc(36px + 24px) calc(24px / 2) 60px calc(24px / 2); }\n\n@media (max-width: 992px) {\n  body[data-layout=\"horizontal\"] .page-content {\n    margin-top: 10px; } }\n\n.page-title-box .breadcrumb {\n  background-color: transparent;\n  padding: 0; }\n\n.page-title-box h4 {\n  color: #fff;\n  text-transform: uppercase;\n  font-weight: 500;\n  font-size: 16px !important; }\n\n.topbar-social-icon {\n  padding: calc(38px / 2) 0; }\n\n.title-tooltip li i {\n  font-size: 20px;\n  margin-left: 10px;\n  color: #636e75; }\n\n.footer {\n  bottom: 0;\n  padding: 20px calc(24px / 2);\n  position: absolute;\n  right: 0;\n  border-top: 1px solid #edf1f5;\n  color: #74788d;\n  left: 260px;\n  height: 60px;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  background-color: #fff; }\n\n@media (max-width: 992px) {\n  .footer {\n    left: 0; } }\n\n.vertical-collpsed .footer {\n  left: 70px; }\n\nbody[data-layout=\"horizontal\"] .footer {\n  left: 0 !important; }\n\n.right-bar {\n  background-color: #fff;\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 280px;\n  z-index: 9999;\n  float: right !important;\n  right: -290px;\n  top: 0;\n  bottom: 0; }\n  .right-bar .right-bar-toggle {\n    background-color: #444c54;\n    height: 24px;\n    width: 24px;\n    line-height: 24px;\n    color: #edf1f5;\n    text-align: center;\n    border-radius: 50%; }\n    .right-bar .right-bar-toggle:hover {\n      background-color: #4b545c; }\n\n.rightbar-overlay {\n  background-color: rgba(52, 58, 64, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all .2s ease-out; }\n\n.right-bar-enabled .right-bar {\n  right: 0; }\n\n.right-bar-enabled .rightbar-overlay {\n  display: block; }\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto; }\n    .right-bar .slimscroll-menu {\n      height: auto !important; } }\n\n.metismenu {\n  margin: 0; }\n  .metismenu li {\n    display: block;\n    width: 100%; }\n  .metismenu .mm-collapse {\n    display: none; }\n    .metismenu .mm-collapse:not(.mm-show) {\n      display: none; }\n    .metismenu .mm-collapse.mm-show {\n      display: block; }\n  .metismenu .mm-collapsing {\n    position: relative;\n    height: 0;\n    overflow: hidden;\n    transition-timing-function: ease;\n    transition-duration: .35s;\n    transition-property: height, visibility; }\n\n.vertical-menu {\n  width: 260px;\n  z-index: 1001;\n  background: #ffffff;\n  bottom: 0;\n  margin-top: 0;\n  position: fixed;\n  top: 70px;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n.user-sidebar {\n  position: relative;\n  text-align: center;\n  background: url(../images/user-img.png);\n  background-color: #525ce5;\n  background-repeat: no-repeat;\n  background-size: cover;\n  background-position: center;\n  padding: 20px 0; }\n  .user-sidebar .user-img {\n    position: relative; }\n    .user-sidebar .user-img img {\n      width: 60px;\n      height: 60px;\n      border: 3px solid #23c58f;\n      padding: 5px; }\n    .user-sidebar .user-img .avatar-online {\n      position: absolute;\n      bottom: 4px;\n      width: 10px;\n      height: 10px;\n      z-index: 1;\n      border: 2px solid transparent;\n      border-radius: 50%;\n      margin-left: -15px; }\n\n.main-content {\n  margin-left: 260px;\n  overflow: hidden; }\n  .main-content .content {\n    padding: 0 15px 10px 15px;\n    margin-top: 70px; }\n\n#sidebar-menu {\n  padding: 0px 0 30px 0; }\n  #sidebar-menu .mm-active > .has-arrow:after {\n    transform: rotate(90deg); }\n  #sidebar-menu .has-arrow:after {\n    content: \"\\F0142\";\n    font-family: 'Material Design Icons';\n    display: block;\n    float: right;\n    transition: transform .2s;\n    font-size: 1rem; }\n  #sidebar-menu ul li a {\n    display: block;\n    padding: .625rem 1.2rem;\n    color: #27303f;\n    position: relative;\n    font-size: 14.5px;\n    transition: all .4s;\n    margin: 0px 17px;\n    border-radius: 3px; }\n    #sidebar-menu ul li a i {\n      display: inline-block;\n      min-width: 1.75rem;\n      padding-bottom: .125em;\n      font-size: 16px;\n      line-height: 1.40625rem;\n      vertical-align: middle;\n      color: #27303f;\n      transition: all .4s; }\n    #sidebar-menu ul li a:hover {\n      color: #525ce5; }\n      #sidebar-menu ul li a:hover i {\n        color: #525ce5; }\n  #sidebar-menu ul li .badge {\n    margin-top: 5px; }\n  #sidebar-menu ul li ul.sub-menu {\n    padding: 0; }\n    #sidebar-menu ul li ul.sub-menu li a {\n      padding: .4rem 1.5rem .4rem 2.8rem;\n      font-size: 14px;\n      color: #27303f;\n      background-color: transparent !important; }\n      #sidebar-menu ul li ul.sub-menu li a:before {\n        content: \"\\F09DF\";\n        font-family: 'Material Design Icons';\n        font-size: 20px;\n        line-height: 10px;\n        padding-right: 2px;\n        vertical-align: middle;\n        display: inline-block; }\n    #sidebar-menu ul li ul.sub-menu li ul.sub-menu {\n      padding: 0; }\n      #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n        padding: .4rem 1.5rem .4rem 4rem;\n        font-size: 14px; }\n\n.menu-title {\n  padding: 12px 20px !important;\n  letter-spacing: .05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 11px;\n  text-transform: uppercase;\n  color: #27303f;\n  font-weight: 600; }\n\n.mm-active {\n  color: #525ce5 !important; }\n  .mm-active > a {\n    color: #525ce5 !important;\n    background-color: #f5f7fa !important; }\n    .mm-active > a i {\n      color: #525ce5 !important; }\n  .mm-active > i {\n    color: #525ce5 !important; }\n  .mm-active .active {\n    color: #525ce5 !important;\n    background-color: #f5f7fa !important; }\n    .mm-active .active i {\n      color: #525ce5 !important; }\n\n@media (max-width: 992px) {\n  .vertical-menu {\n    display: none; }\n  .main-content {\n    margin-left: 0 !important; }\n  body.sidebar-enable .vertical-menu {\n    display: block; } }\n\n.vertical-collpsed .user-sidebar {\n  display: none; }\n\n.vertical-collpsed .main-content {\n  margin-left: 70px; }\n\n.vertical-collpsed .navbar-brand-box {\n  width: 70px !important; }\n\n.vertical-collpsed .logo span.logo-lg {\n  display: none; }\n\n.vertical-collpsed .logo span.logo-sm {\n  display: block; }\n\n.vertical-collpsed .vertical-menu {\n  position: absolute;\n  width: 70px !important;\n  z-index: 5; }\n  .vertical-collpsed .vertical-menu .simplebar-mask,\n  .vertical-collpsed .vertical-menu .simplebar-content-wrapper {\n    overflow: visible !important; }\n  .vertical-collpsed .vertical-menu .simplebar-scrollbar {\n    display: none !important; }\n  .vertical-collpsed .vertical-menu .simplebar-offset {\n    bottom: 0 !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .menu-title,\n  .vertical-collpsed .vertical-menu #sidebar-menu .badge,\n  .vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {\n    display: none !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {\n    height: inherit !important; }\n  .vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {\n    display: none; }\n  .vertical-collpsed .vertical-menu #sidebar-menu > ul > li {\n    position: relative;\n    white-space: nowrap; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {\n      padding: 15px 20px;\n      min-height: 55px;\n      transition: none;\n      margin: 0; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {\n        color: #525ce5; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n        font-size: 1.15rem;\n        margin-left: 4px; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n        display: none;\n        padding-left: 25px; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n      position: relative;\n      width: calc(190px + 70px);\n      background-color: #f5f7fa;\n      transition: none; }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {\n        display: inline; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n      display: block;\n      left: 70px;\n      position: absolute;\n      width: 190px;\n      height: auto !important;\n      box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {\n        box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1); }\n      .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n        box-shadow: none;\n        padding: 8px 20px;\n        position: relative;\n        width: 190px;\n        z-index: 6;\n        color: #27303f;\n        margin: 0; }\n        .vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n          color: #525ce5; }\n  .vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n    padding: 5px 0;\n    z-index: 9999;\n    display: none;\n    background-color: #ffffff; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n      display: block;\n      left: 190px;\n      height: auto !important;\n      margin-top: -36px;\n      position: absolute;\n      width: 190px; }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {\n      position: absolute;\n      right: 20px;\n      top: 12px;\n      transform: rotate(270deg); }\n    .vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {\n      color: #f9fafc; }\n\nbody[data-sidebar=\"dark\"] .user-sidebar {\n  background: none; }\n\nbody[data-sidebar=\"dark\"] .vertical-menu {\n  background: #1f293f; }\n\nbody[data-sidebar=\"dark\"] #sidebar-menu ul li a {\n  color: #8590a5; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li a i {\n    color: #8590a5; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li a:hover {\n    color: #d7e4ec; }\n    body[data-sidebar=\"dark\"] #sidebar-menu ul li a:hover i {\n      color: #d7e4ec; }\n\nbody[data-sidebar=\"dark\"] #sidebar-menu ul li ul.sub-menu li a {\n  color: #8590a5; }\n  body[data-sidebar=\"dark\"] #sidebar-menu ul li ul.sub-menu li a:hover {\n    color: #d7e4ec; }\n\nbody[data-sidebar=\"dark\"].vertical-collpsed {\n  min-height: 1400px; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n    background: #222d46;\n    color: #d7e4ec; }\n    body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n      color: #d7e4ec; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n    color: #8590a5; }\n    body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n      color: #525ce5; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n    background-color: white; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul > li > a.mm-active {\n    color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.mm-active > a, body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li.active > a {\n    color: #525ce5 !important; }\n  body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.mm-active, body[data-sidebar=\"dark\"].vertical-collpsed .vertical-menu #sidebar-menu ul li li a.active {\n    color: #525ce5 !important; }\n\nbody[data-sidebar=\"dark\"] .mm-active {\n  color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"] .mm-active > a {\n    color: #d7e4ec !important;\n    background-color: #2b364e !important; }\n    body[data-sidebar=\"dark\"] .mm-active > a i {\n      color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"] .mm-active > i {\n    color: #d7e4ec !important; }\n  body[data-sidebar=\"dark\"] .mm-active .active {\n    color: #d7e4ec !important;\n    background-color: #2b364e !important; }\n    body[data-sidebar=\"dark\"] .mm-active .active i {\n      color: #d7e4ec !important; }\n\nbody[data-sidebar=\"dark\"] .menu-title {\n  color: #8590a5; }\n\nbody[data-layout=\"horizontal\"] .main-content {\n  margin-left: 0 !important; }\n\nbody[data-sidebar-size=\"small\"] .navbar-brand-box {\n  width: 160px; }\n\nbody[data-sidebar-size=\"small\"] .vertical-menu {\n  width: 160px;\n  text-align: center; }\n  body[data-sidebar-size=\"small\"] .vertical-menu .has-arrow:after,\n  body[data-sidebar-size=\"small\"] .vertical-menu .badge {\n    display: none !important; }\n\nbody[data-sidebar-size=\"small\"] .main-content {\n  margin-left: 160px; }\n\nbody[data-sidebar-size=\"small\"] .footer {\n  left: 160px; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li a i {\n  display: block; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li a {\n  padding-left: 1.5rem; }\n  body[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li a:before {\n    display: none; }\n\nbody[data-sidebar-size=\"small\"] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-left: 1.5rem; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .main-content {\n  margin-left: 70px; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: left; }\n  body[data-sidebar-size=\"small\"].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n    display: inline-block; }\n\nbody[data-sidebar-size=\"small\"].vertical-collpsed .footer {\n  left: 70px; }\n\n[dir=\"rtl\"]\n#sidebar-menu .has-arrow:after {\n  content: \"\\F0141\";\n  transition: transform .2s; }\n\n[dir=\"rtl\"]\n#sidebar-menu .mm-active > .has-arrow:after {\n  transform: rotate(90deg); }\n\n.topnav {\n  background: #fff;\n  padding: 0 calc(24px / 2);\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  margin-top: 70px;\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 100; }\n  .topnav .topnav-menu {\n    margin: 0;\n    padding: 0; }\n  .topnav .navbar-nav .nav-link {\n    font-size: 15px;\n    position: relative;\n    padding: 1.2rem 1.5rem;\n    color: #27303f; }\n    .topnav .navbar-nav .nav-link i {\n      font-size: 15px;\n      top: 2px;\n      position: relative; }\n    .topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {\n      color: #525ce5;\n      background-color: transparent; }\n  .topnav .navbar-nav .dropdown-item {\n    color: #27303f; }\n    .topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {\n      color: #525ce5;\n      background: transparent; }\n  .topnav .navbar-nav .nav-item .nav-link.active {\n    color: #525ce5; }\n  .topnav .navbar-nav .dropdown.active > a {\n    color: #525ce5;\n    background-color: transparent; }\n\n@media (min-width: 1200px) {\n  body[data-layout=\"horizontal\"] .container-fluid,\n  body[data-layout=\"horizontal\"] .navbar-header {\n    max-width: 85%; } }\n\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 0; }\n  .topnav .dropdown-item {\n    padding: .5rem 1.5rem;\n    min-width: 180px; }\n  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {\n    left: 0px;\n    right: auto; }\n  .topnav .dropdown .dropdown-menu {\n    margin-top: 0;\n    border-radius: 0 0 0.25rem 0.25rem; }\n    .topnav .dropdown .dropdown-menu .arrow-down::after {\n      right: 15px;\n      transform: rotate(-135deg) translateY(-50%);\n      position: absolute; }\n    .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n      position: absolute;\n      top: 0 !important;\n      left: 100%;\n      display: none; }\n  .topnav .dropdown:hover > .dropdown-menu {\n    display: block; }\n  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {\n    display: block; }\n  .navbar-toggle {\n    display: none; } }\n\n.arrow-down {\n  display: inline-block; }\n  .arrow-down:after {\n    border-color: initial;\n    border-style: solid;\n    border-width: 0 0 1px 1px;\n    content: \"\";\n    height: .4em;\n    display: inline-block;\n    right: 5px;\n    top: 50%;\n    margin-left: 10px;\n    transform: rotate(-45deg) translateY(-50%);\n    transform-origin: top;\n    transition: all .3s ease-out;\n    width: .4em; }\n\n@media (max-width: 1199.98px) {\n  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto; } }\n\n@media (max-width: 991.98px) {\n  .navbar-brand-box .logo-dark {\n    display: block; }\n    .navbar-brand-box .logo-dark span.logo-sm {\n      display: block; }\n  .navbar-brand-box .logo-light {\n    display: none; }\n  .topnav {\n    max-height: 360px;\n    overflow-y: auto;\n    padding: 0; }\n    .topnav .navbar-nav .nav-link {\n      padding: 0.75rem 1.1rem; }\n    .topnav .dropdown .dropdown-menu {\n      background-color: transparent;\n      border: none;\n      box-shadow: none;\n      padding-left: 20px; }\n      .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {\n        width: auto; }\n        .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {\n          margin: 0px; }\n    .topnav .dropdown .dropdown-item {\n      position: relative;\n      background-color: transparent; }\n      .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {\n        color: #525ce5; }\n    .topnav .arrow-down::after {\n      right: 15px;\n      position: absolute; } }\n\n@media (min-width: 992px) {\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .navbar-brand-box .logo-dark {\n    display: block; }\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .navbar-brand-box .logo-light {\n    display: none; }\n  body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav {\n    background-color: #141b2d; }\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link {\n      color: rgba(255, 255, 255, 0.5); }\n      body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link:focus, body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav .nav-link:hover {\n        color: rgba(255, 255, 255, 0.9); }\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] .topnav .navbar-nav > .dropdown.active > a {\n      color: rgba(255, 255, 255, 0.9) !important; } }\n\nbody[data-layout=\"horizontal\"] .logo-dark {\n  display: none; }\n\nbody[data-layout=\"horizontal\"] .logo-light {\n  display: block; }\n\nbody[data-topbar=\"colored\"] #page-topbar {\n  background-color: #778beb; }\n\nbody[data-topbar=\"colored\"] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05); }\n\nbody[data-topbar=\"colored\"] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\nbody[data-topbar=\"colored\"] .title-tooltip li i {\n  color: rgba(255, 255, 255, 0.8); }\n\nbody[data-topbar=\"colored\"] .header-item {\n  color: rgba(255, 255, 255, 0.5); }\n  body[data-topbar=\"colored\"] .header-item:hover {\n    color: #fff; }\n\nbody[data-topbar=\"colored\"] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25); }\n\nbody[data-topbar=\"colored\"] .noti-icon i {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-topbar=\"colored\"] .logo-dark {\n  display: none; }\n\nbody[data-topbar=\"colored\"] .logo-light {\n  display: block; }\n\nbody[data-topbar=\"colored\"] .app-search .form-control {\n  background-color: rgba(241, 245, 247, 0.07);\n  color: #fff; }\n\nbody[data-topbar=\"colored\"] .app-search span,\nbody[data-topbar=\"colored\"] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5); }\n\nbody[data-layout-size=\"boxed\"] {\n  background-color: #f1f3f7; }\n  body[data-layout-size=\"boxed\"] #layout-wrapper {\n    background-color: #f5f7fa;\n    max-width: 1400px;\n    margin: 0 auto;\n    box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n  body[data-layout-size=\"boxed\"] #page-topbar {\n    max-width: 1400px;\n    margin: 0 auto; }\n  body[data-layout-size=\"boxed\"] .footer {\n    margin: 0 auto;\n    max-width: calc(1400px - 260px); }\n  body[data-layout-size=\"boxed\"].vertical-collpsed .footer {\n    max-width: calc(1400px - 70px); }\n\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] #page-topbar, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] #layout-wrapper, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .footer {\n  max-width: 100%; }\n\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .container-fluid, body[data-layout=\"horizontal\"][data-layout-size=\"boxed\"] .navbar-header {\n  max-width: 1400px; }\n\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent; }\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none; }\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%); }\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2); }\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4); }\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important; }\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1; }\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em; }\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em; }\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom; }\n\n.waves-input-wrapper.waves-button {\n  padding: 0; }\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1; }\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%; }\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms; }\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3); }\n\n.waves-block {\n  display: block; }\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4); }\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(82, 92, 229, 0.4); }\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(35, 197, 143, 0.4); }\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(91, 164, 229, 0.4); }\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(238, 177, 72, 0.4); }\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(241, 78, 78, 0.4); }\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem; }\n\n.avatar-sm {\n  height: 2.5rem;\n  width: 2.5rem; }\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem; }\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem; }\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem; }\n\n.mini-stat-icon {\n  width: 46px;\n  height: 46px; }\n\n.avatar-title {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%; }\n\n.custom-accordion .card + .card {\n  margin-top: 0.5rem; }\n\n.custom-accordion a.collapsed i.accor-plus-icon:before {\n  content: \"\\F0415\"; }\n\n.custom-accordion .card-header {\n  border-radius: 7px; }\n\n.custom-accordion-arrow .card {\n  border: 1px solid #edf1f5;\n  box-shadow: none; }\n\n.custom-accordion-arrow .card-header {\n  padding-left: 45px;\n  position: relative; }\n  .custom-accordion-arrow .card-header .accor-arrow-icon {\n    position: absolute;\n    display: inline-block;\n    width: 24px;\n    height: 24px;\n    line-height: 24px;\n    font-size: 16px;\n    background-color: #525ce5;\n    color: #fff;\n    border-radius: 50%;\n    text-align: center;\n    left: 10px;\n    top: 50%;\n    transform: translateY(-50%); }\n\n.custom-accordion-arrow a.collapsed i.accor-arrow-icon:before {\n  content: \"\\F0142\"; }\n\n.font-size-10 {\n  font-size: 10px !important; }\n\n.font-size-11 {\n  font-size: 11px !important; }\n\n.font-size-12 {\n  font-size: 12px !important; }\n\n.font-size-13 {\n  font-size: 13px !important; }\n\n.font-size-14 {\n  font-size: 14px !important; }\n\n.font-size-15 {\n  font-size: 15px !important; }\n\n.font-size-16 {\n  font-size: 16px !important; }\n\n.font-size-17 {\n  font-size: 17px !important; }\n\n.font-size-18 {\n  font-size: 18px !important; }\n\n.font-size-20 {\n  font-size: 20px !important; }\n\n.font-size-22 {\n  font-size: 22px !important; }\n\n.font-size-24 {\n  font-size: 24px !important; }\n\n.media {\n  display: flex;\n  align-items: flex-start; }\n\n.media-body {\n  flex: 1; }\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 2px);\n  display: block;\n  border: 1px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd;\n  text-align: center;\n  transition: all 0.4s; }\n  .social-list-item:hover {\n    color: #74788d;\n    background-color: #edf1f5; }\n\n.w-xs {\n  min-width: 80px; }\n\n.w-sm {\n  min-width: 95px; }\n\n.w-md {\n  min-width: 110px; }\n\n.w-lg {\n  min-width: 140px; }\n\n.w-xl {\n  min-width: 160px; }\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  opacity: 0.7;\n  background-color: #000; }\n\n.flex-1 {\n  flex: 1; }\n\n.alert-dismissible .btn-close {\n  font-size: 10px;\n  padding: 1.05rem 1.25rem;\n  background: transparent url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e\") center/1em auto no-repeat; }\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  z-index: 9999; }\n\n#status {\n  width: 40px;\n  height: 40px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin: -20px 0 0 -20px; }\n\n.spinner-chase {\n  margin: 0 auto;\n  width: 40px;\n  height: 40px;\n  position: relative;\n  animation: spinner-chase 2.5s infinite linear both; }\n\n.chase-dot {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  top: 0;\n  animation: chase-dot 2.0s infinite ease-in-out both; }\n  .chase-dot:before {\n    content: '';\n    display: block;\n    width: 25%;\n    height: 25%;\n    background-color: #525ce5;\n    border-radius: 100%;\n    animation: chase-dot-before 2.0s infinite ease-in-out both; }\n  .chase-dot:nth-child(1) {\n    animation-delay: -1.1s; }\n    .chase-dot:nth-child(1):before {\n      animation-delay: -1.1s; }\n  .chase-dot:nth-child(2) {\n    animation-delay: -1.0s; }\n    .chase-dot:nth-child(2):before {\n      animation-delay: -1.0s; }\n  .chase-dot:nth-child(3) {\n    animation-delay: -0.9s; }\n    .chase-dot:nth-child(3):before {\n      animation-delay: -0.9s; }\n  .chase-dot:nth-child(4) {\n    animation-delay: -0.8s; }\n    .chase-dot:nth-child(4):before {\n      animation-delay: -0.8s; }\n  .chase-dot:nth-child(5) {\n    animation-delay: -0.7s; }\n    .chase-dot:nth-child(5):before {\n      animation-delay: -0.7s; }\n  .chase-dot:nth-child(6) {\n    animation-delay: -0.6s; }\n    .chase-dot:nth-child(6):before {\n      animation-delay: -0.6s; }\n\n@keyframes spinner-chase {\n  100% {\n    transform: rotate(360deg); } }\n\n@keyframes chase-dot {\n  80%, 100% {\n    transform: rotate(360deg); } }\n\n@keyframes chase-dot-before {\n  50% {\n    transform: scale(0.4); }\n  100%, 0% {\n    transform: scale(1); } }\n\n.form-check-right {\n  padding-left: 0;\n  display: inline-block;\n  padding-right: 1.5em; }\n  .form-check-right .form-check-input {\n    float: right;\n    margin-left: 0;\n    margin-right: -1.5em; }\n  .form-check-right .form-check-label {\n    display: block; }\n\n.form-check {\n  position: relative;\n  text-align: left; }\n\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0; }\n\n.dash-summary {\n  border-top: 1px solid #eaedf1; }\n\n.dash-main-border {\n  border-bottom: 1px solid #eaedf1; }\n\n.dash-info-widget {\n  background: #f9fafc; }\n\n.dash-goal {\n  border-left: 1px solid #eaedf1; }\n\n@media (max-width: 768px) {\n  .dash-goal {\n    border-left: none; } }\n\n.carousel-indicators {\n  bottom: -20px; }\n  .carousel-indicators button {\n    background-color: #525ce5 !important;\n    width: 10px !important;\n    height: 10px !important;\n    border-radius: 50% !important;\n    margin: 5px;\n    opacity: 0.5; }\n\n.mini-stats-wid .mini-stat-icon {\n  overflow: hidden;\n  position: relative; }\n  .mini-stats-wid .mini-stat-icon:before, .mini-stats-wid .mini-stat-icon:after {\n    content: \"\";\n    position: absolute;\n    width: 8px;\n    height: 54px;\n    background-color: rgba(255, 255, 255, 0.1);\n    left: 16px;\n    transform: rotate(32deg);\n    top: -5px;\n    transition: all 0.4s; }\n  .mini-stats-wid .mini-stat-icon::after {\n    left: -12px;\n    width: 12px;\n    transition: all 0.2s; }\n\n.mini-stats-wid:hover .mini-stat-icon::after {\n  left: 60px; }\n\n.inbox-wid .inbox-list-item a {\n  color: #495057;\n  display: block;\n  padding: 11px 0px;\n  border-bottom: 1px solid #edf1f5; }\n\n.inbox-wid .inbox-list-item:first-child a {\n  padding-top: 0px; }\n\n.inbox-wid .inbox-list-item:last-child a {\n  border-bottom: 0px; }\n\n.activity-border:before {\n  content: \"\";\n  position: absolute;\n  height: 38px;\n  border-left: 2px dashed #eaedf1;\n  top: 40px;\n  left: 0px; }\n\n.activity-wid {\n  margin-left: 16px; }\n  .activity-wid .activity-list {\n    position: relative;\n    padding: 0 0 33px 30px; }\n    .activity-wid .activity-list .activity-icon {\n      position: absolute;\n      left: -20px;\n      top: 0px;\n      z-index: 2; }\n    .activity-wid .activity-list:last-child {\n      padding-bottom: 0px; }\n\n.button-items {\n  margin-left: -8px;\n  margin-bottom: -12px; }\n  .button-items .btn {\n    margin-bottom: 12px;\n    margin-left: 8px; }\n\n.mfp-popup-form {\n  max-width: 1140px; }\n\n.bs-example-modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block; }\n\n.icon-demo-content {\n  text-align: center;\n  color: #adb5bd; }\n  .icon-demo-content i {\n    display: block;\n    font-size: 24px;\n    color: #74788d;\n    width: 48px;\n    height: 48px;\n    line-height: 46px;\n    margin: 0px auto;\n    margin-bottom: 16px;\n    border-radius: 4px;\n    border: 1px solid #edf1f5;\n    transition: all 0.4s; }\n  .icon-demo-content .col-lg-4 {\n    margin-top: 24px; }\n    .icon-demo-content .col-lg-4:hover i {\n      background-color: #525ce5;\n      color: #fff; }\n\n.grid-structure .grid-container {\n  background-color: #f9fafc;\n  margin-top: 10px;\n  font-size: .8rem;\n  font-weight: 500;\n  padding: 10px 20px; }\n\n.card-radio {\n  background-color: #fff;\n  border: 2px solid #eaedf1;\n  border-radius: 0.25rem;\n  padding: 1rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap; }\n  .card-radio:hover {\n    cursor: pointer; }\n\n.card-radio-label {\n  display: block; }\n\n.card-radio-input {\n  display: none; }\n  .card-radio-input:checked + .card-radio {\n    border-color: #525ce5 !important; }\n\n.navs-carousel .owl-nav {\n  margin-top: 16px; }\n  .navs-carousel .owl-nav button {\n    width: 30px;\n    height: 30px;\n    line-height: 28px !important;\n    font-size: 20px !important;\n    border-radius: 50% !important;\n    background-color: rgba(82, 92, 229, 0.25) !important;\n    color: #525ce5 !important;\n    margin: 4px 8px !important; }\n\n@media print {\n  .vertical-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-header,\n  .footer {\n    display: none !important; }\n  .card-body,\n  .main-content,\n  .right-bar,\n  .page-content,\n  body {\n    padding: 0;\n    margin: 0; }\n  .card {\n    border: 0; } }\n\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start; }\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit; }\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0; }\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch; }\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%;\n  /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto;\n  /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%;\n  /* Not required for horizontal scroll to trigger */\n  max-height: 100%;\n  /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important; }\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none; }\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table; }\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none; }\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0; }\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1; }\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden; }\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none; }\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all; }\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px; }\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear; }\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear; }\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px; }\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px; }\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px; }\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px; }\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto; }\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0; }\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll; }\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none; }\n\n.custom-scroll {\n  height: 100%; }\n\n.fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase; }\n\n.fc th.fc-widget-header {\n  background: #f9fafc;\n  font-size: 13px;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase;\n  font-weight: 600; }\n\n.fc-unthemed .fc-content,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-list-heading td,\n.fc-unthemed .fc-list-view,\n.fc-unthemed .fc-popover,\n.fc-unthemed .fc-row,\n.fc-unthemed tbody,\n.fc-unthemed td,\n.fc-unthemed th,\n.fc-unthemed thead {\n  border-color: #f9fafc; }\n\n.fc-unthemed td.fc-today {\n  background: #fafbfc; }\n\n.fc-button {\n  background: #fff;\n  border-color: #edf1f5;\n  color: #495057;\n  text-transform: capitalize;\n  box-shadow: none;\n  padding: 6px 12px !important;\n  height: auto !important; }\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #525ce5;\n  color: #fff;\n  text-shadow: none; }\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center; }\n\n#external-events .external-event {\n  text-align: left !important;\n  padding: 8px 16px; }\n\n.fc-event, .fc-event-dot {\n  background-color: #525ce5; }\n\n.fc-event .fc-content {\n  color: #fff; }\n\n.fc .table-bordered td, .fc .table-bordered th {\n  border-color: #edf1f5; }\n\n@media (max-width: 575.98px) {\n  .fc .fc-toolbar {\n    display: block; } }\n\n.fc .fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase; }\n\n@media (max-width: 767.98px) {\n  .fc .fc-toolbar .fc-left,\n  .fc .fc-toolbar .fc-right,\n  .fc .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    text-align: center;\n    clear: both;\n    margin: 10px 0; }\n  .fc .fc-toolbar > * > * {\n    float: none; }\n  .fc .fc-toolbar .fc-today-button {\n    display: none; } }\n\n.fc .fc-toolbar .btn {\n  text-transform: capitalize; }\n\n.fc-bootstrap .fc-today.alert-info {\n  background-color: #eaedf1; }\n\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\n  background-color: #000 !important; }\n\n[dir=\"rtl\"] .fc-header-toolbar {\n  direction: ltr !important; }\n\n[dir=\"rtl\"] .fc-toolbar > * > :not(:first-child) {\n  margin-left: .75em; }\n\n.sp-container {\n  background-color: #fff;\n  z-index: 1000; }\n  .sp-container button {\n    padding: .25rem .5rem;\n    font-size: .71094rem;\n    border-radius: .2rem;\n    font-weight: 400;\n    color: #343a40; }\n    .sp-container button.sp-palette-toggle {\n      background-color: #f9fafc; }\n    .sp-container button.sp-choose {\n      background-color: #23c58f;\n      margin-left: 5px;\n      margin-right: 0; }\n\n.sp-palette-container {\n  border-right: 1px solid #edf1f5; }\n\n.sp-input {\n  background-color: #fff;\n  border-color: #ced4da !important;\n  color: #495057; }\n  .sp-input:focus {\n    outline: none; }\n\n[dir=\"rtl\"] .sp-alpha {\n  direction: rtl; }\n\n[dir=\"rtl\"] .sp-original-input-container .sp-add-on {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 4px !important;\n  border-bottom-left-radius: 4px !important; }\n\n[dir=\"rtl\"] input.spectrum.with-add-on {\n  border: 1px solid #ced4da;\n  border-left: 0;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem; }\n\n#session-timeout-dialog .close {\n  display: none; }\n\n#session-timeout-dialog .countdown-holder {\n  color: #f14e4e;\n  font-weight: 500; }\n\n#session-timeout-dialog .btn-default {\n  background-color: #fff;\n  color: #f14e4e;\n  box-shadow: none; }\n\n.irs {\n  font-family: var(--bs-font-sans-serif); }\n\n.irs--round .irs-bar,\n.irs--round .irs-to,\n.irs--round .irs-from,\n.irs--round .irs-single {\n  background: #525ce5 !important;\n  font-size: 11px; }\n\n.irs--round .irs-to:before,\n.irs--round .irs-from:before,\n.irs--round .irs-single:before {\n  display: none; }\n\n.irs--round .irs-line {\n  background: #eaedf1;\n  border-color: #eaedf1; }\n\n.irs--round .irs-grid-text {\n  font-size: 11px;\n  color: #adb5bd; }\n\n.irs--round .irs-min,\n.irs--round .irs-max {\n  color: #adb5bd;\n  background: #eaedf1;\n  font-size: 11px; }\n\n.irs--round .irs-handle {\n  border: 2px solid #525ce5;\n  width: 10px;\n  height: 16px;\n  top: 29px;\n  background-color: #fff !important; }\n\n.swal2-container .swal2-title {\n  font-size: 24px;\n  font-weight: 500; }\n\n.swal2-content {\n  font-size: 16px; }\n\n.swal2-icon.swal2-question {\n  border-color: #5ba4e5;\n  color: #5ba4e5; }\n\n.swal2-icon.swal2-success [class^=swal2-success-line] {\n  background-color: #23c58f; }\n\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: rgba(35, 197, 143, 0.3); }\n\n.swal2-icon.swal2-warning {\n  border-color: #eeb148;\n  color: #eeb148; }\n\n.swal2-styled:focus {\n  box-shadow: none; }\n\n.swal2-progress-steps .swal2-progress-step {\n  background: #525ce5; }\n  .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n    background: #525ce5; }\n    .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n      background: rgba(82, 92, 229, 0.3); }\n\n.swal2-progress-steps .swal2-progress-step-line {\n  background: #525ce5; }\n\n.swal2-loader {\n  border-color: #525ce5 transparent #525ce5 transparent; }\n\n.symbol {\n  border-color: #fff; }\n\n.rating-symbol-background, .rating-symbol-foreground {\n  font-size: 24px; }\n\n.rating-symbol-foreground {\n  top: 0px; }\n\n.rating-star > span {\n  display: inline-block;\n  vertical-align: middle; }\n  .rating-star > span.badge {\n    margin-left: 4px; }\n\n.error {\n  color: #f14e4e; }\n\n.parsley-error {\n  border-color: #f14e4e; }\n\n.parsley-errors-list {\n  display: none;\n  margin: 0;\n  padding: 0; }\n  .parsley-errors-list.filled {\n    display: block; }\n  .parsley-errors-list > li {\n    font-size: 12px;\n    list-style: none;\n    color: #f14e4e;\n    margin-top: 5px; }\n\n.select2-container {\n  display: block; }\n  .select2-container .select2-selection--single {\n    background-color: #fff;\n    border: 1px solid #ced4da;\n    height: 38px; }\n    .select2-container .select2-selection--single:focus {\n      outline: none; }\n    .select2-container .select2-selection--single .select2-selection__rendered {\n      line-height: 36px;\n      padding-left: 12px;\n      color: #495057; }\n    .select2-container .select2-selection--single .select2-selection__arrow {\n      height: 34px;\n      width: 34px;\n      right: 3px; }\n      .select2-container .select2-selection--single .select2-selection__arrow b {\n        border-color: #adb5bd transparent transparent transparent;\n        border-width: 6px 6px 0 6px; }\n    .select2-container .select2-selection--single .select2-selection__placeholder {\n      color: #495057; }\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent #adb5bd transparent !important;\n  border-width: 0 6px 6px 6px !important; }\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: #fff; }\n  .select2-container--default .select2-search--dropdown .select2-search__field {\n    border: 1px solid #ced4da;\n    background-color: #fff;\n    color: #74788d;\n    outline: none; }\n\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #525ce5; }\n\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: #f9fafc;\n  color: #16181b; }\n  .select2-container--default .select2-results__option[aria-selected=true]:hover {\n    background-color: #525ce5;\n    color: #fff; }\n\n.select2-results__option {\n  padding: 6px 12px; }\n\n.select2-dropdown {\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n.select2-search input {\n  border: 1px solid #eaedf1; }\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: #fff;\n  border: 1px solid #ced4da !important; }\n  .select2-container .select2-selection--multiple .select2-selection__rendered {\n    padding: 2px 10px; }\n  .select2-container .select2-selection--multiple .select2-search__field {\n    border: 0;\n    color: #495057; }\n    .select2-container .select2-selection--multiple .select2-search__field::placeholder {\n      color: #495057; }\n  .select2-container .select2-selection--multiple .select2-selection__choice {\n    background-color: #edf1f5;\n    border: 1px solid #eaedf1;\n    border-radius: 1px;\n    padding: 0 7px; }\n\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: #ced4da; }\n\n.select2-container--default .select2-results__group {\n  font-weight: 600; }\n\n.select2-result-repository__avatar {\n  float: left;\n  width: 60px;\n  margin-right: 10px; }\n  .select2-result-repository__avatar img {\n    width: 100%;\n    height: auto;\n    border-radius: 2px; }\n\n.select2-result-repository__statistics {\n  margin-top: 7px; }\n\n.select2-result-repository__forks,\n.select2-result-repository__stargazers,\n.select2-result-repository__watchers {\n  display: inline-block;\n  font-size: 11px;\n  margin-right: 1em;\n  color: #adb5bd; }\n  .select2-result-repository__forks .fa,\n  .select2-result-repository__stargazers .fa,\n  .select2-result-repository__watchers .fa {\n    margin-right: 4px; }\n    .select2-result-repository__forks .fa.fa-flash::before,\n    .select2-result-repository__stargazers .fa.fa-flash::before,\n    .select2-result-repository__watchers .fa.fa-flash::before {\n      content: \"\\f0e7\";\n      font-family: 'Font Awesome 5 Free'; }\n\n.select2-results__option--highlighted .select2-result-repository__forks,\n.select2-results__option--highlighted .select2-result-repository__stargazers,\n.select2-results__option--highlighted .select2-result-repository__watchers {\n  color: rgba(255, 255, 255, 0.8); }\n\n.select2-result-repository__meta {\n  overflow: hidden; }\n\n.img-flag {\n  margin-right: 7px;\n  height: 15px;\n  width: 18px; }\n\n/* CSS Switch */\ninput[switch] {\n  display: none; }\n  input[switch] + label {\n    font-size: 1em;\n    line-height: 1;\n    width: 56px;\n    height: 24px;\n    background-color: #ced4da;\n    background-image: none;\n    border-radius: 2rem;\n    padding: 0.16667rem;\n    cursor: pointer;\n    display: inline-block;\n    text-align: center;\n    position: relative;\n    font-weight: 500;\n    transition: all 0.1s ease-in-out; }\n    input[switch] + label:before {\n      color: #343a40;\n      content: attr(data-off-label);\n      display: block;\n      font-family: inherit;\n      font-weight: 500;\n      font-size: 12px;\n      line-height: 21px;\n      position: absolute;\n      right: 1px;\n      margin: 3px;\n      top: -2px;\n      text-align: center;\n      min-width: 1.66667rem;\n      overflow: hidden;\n      transition: all 0.1s ease-in-out; }\n    input[switch] + label:after {\n      content: '';\n      position: absolute;\n      left: 3px;\n      background-color: #edf1f5;\n      box-shadow: none;\n      border-radius: 2rem;\n      height: 20px;\n      width: 20px;\n      top: 2px;\n      transition: all 0.1s ease-in-out; }\n  input[switch]:checked + label {\n    background-color: #525ce5; }\n\ninput[switch]:checked + label {\n  background-color: #525ce5; }\n  input[switch]:checked + label:before {\n    color: #fff;\n    content: attr(data-on-label);\n    right: auto;\n    left: 3px; }\n  input[switch]:checked + label:after {\n    left: 33px;\n    background-color: #edf1f5; }\n\ninput[switch=\"bool\"] + label {\n  background-color: #f14e4e; }\n\ninput[switch=\"bool\"] + label:before, input[switch=\"bool\"]:checked + label:before,\ninput[switch=\"default\"]:checked + label:before {\n  color: #fff; }\n\ninput[switch=\"bool\"]:checked + label {\n  background-color: #23c58f; }\n\ninput[switch=\"default\"]:checked + label {\n  background-color: #a2a2a2; }\n\ninput[switch=\"primary\"]:checked + label {\n  background-color: #525ce5; }\n\ninput[switch=\"success\"]:checked + label {\n  background-color: #23c58f; }\n\ninput[switch=\"info\"]:checked + label {\n  background-color: #5ba4e5; }\n\ninput[switch=\"warning\"]:checked + label {\n  background-color: #eeb148; }\n\ninput[switch=\"danger\"]:checked + label {\n  background-color: #f14e4e; }\n\ninput[switch=\"dark\"]:checked + label {\n  background-color: #343a40; }\n\n.square-switch {\n  margin-right: 7px; }\n  .square-switch input[switch] + label, .square-switch input[switch] + label:after {\n    border-radius: 4px; }\n\n.datepicker {\n  border: 1px solid #f9fafc;\n  padding: 8px;\n  z-index: 999 !important; }\n  .datepicker table tr th {\n    font-weight: 500; }\n  .datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover {\n    background-color: #525ce5 !important;\n    background-image: none;\n    box-shadow: none;\n    color: #fff !important; }\n  .datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n  .datepicker table tr td span.focused,\n  .datepicker table tr td span:hover {\n    background: #edf1f5; }\n  .datepicker table tr td.new, .datepicker table tr td.old,\n  .datepicker table tr td span.new,\n  .datepicker table tr td span.old {\n    color: #adb5bd;\n    opacity: 0.6; }\n  .datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n    background-color: #eaedf1; }\n\n.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {\n  padding: 7px; }\n\n.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0; }\n\n.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0; }\n\n.bootstrap-touchspin .input-group-btn-vertical {\n  right: 0 !important;\n  left: 100% !important; }\n\n.bootstrap-touchspin .bootstrap-touchspin-up {\n  border-top-right-radius: 4px !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n  border-bottom-left-radius: 0 !important; }\n\n.bootstrap-touchspin .bootstrap-touchspin-down {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 4px !important;\n  border-top-left-radius: 0 !important;\n  border-bottom-left-radius: 0 !important; }\n\n.table-bordered {\n  border: 1px solid #edf1f5; }\n\ndiv.dataTables_wrapper div.dataTables_filter {\n  text-align: right; }\n  div.dataTables_wrapper div.dataTables_filter input {\n    margin-left: 0.5em;\n    margin-right: 0; }\n\n.tox-tinymce {\n  border: 2px solid #eaedf1 !important; }\n\n.tox .tox-statusbar {\n  border-top: 1px solid #eaedf1 !important; }\n\n.tox .tox-menubar,\n.tox .tox-edit-area__iframe,\n.tox .tox-statusbar {\n  background-color: #fff !important;\n  background: none !important; }\n\n.tox .tox-mbtn {\n  color: #495057 !important; }\n  .tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n    background-color: #eaedf1 !important; }\n\n.tox .tox-tbtn:hover {\n  background-color: #eaedf1 !important; }\n\n.tox .tox-toolbar__primary {\n  border-color: #eaedf1 !important; }\n\n.tox .tox-toolbar,\n.tox .tox-toolbar__overflow,\n.tox .tox-toolbar__primary {\n  background: #eaedf1 !important; }\n\n.tox .tox-tbtn {\n  color: #495057 !important; }\n  .tox .tox-tbtn svg {\n    fill: #495057 !important; }\n\n.tox .tox-edit-area__iframe {\n  background-color: #fff !important; }\n\n.tox .tox-statusbar a,\n.tox .tox-statusbar__path-item,\n.tox .tox-statusbar__wordcount {\n  color: #495057 !important; }\n\n.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\n  border-right: 1px solid #dbe0e7 !important; }\n\n.tox-tinymce-aux {\n  z-index: 1000 !important; }\n\n/* Dropzone */\n.dropzone {\n  min-height: 230px;\n  border: 2px dashed #ced4da;\n  background: #fff;\n  border-radius: 6px; }\n  .dropzone .dz-message {\n    font-size: 24px;\n    width: 100%; }\n\n.twitter-bs-wizard .twitter-bs-wizard-nav {\n  position: relative; }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {\n    content: \"\";\n    width: 189px;\n    height: 2px;\n    background: rgba(82, 92, 229, 0.2);\n    position: absolute;\n    top: 26px;\n    margin-left: 100px; }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n    display: inline-block;\n    border-radius: 30px;\n    padding: 4px 0px;\n    width: 200px;\n    line-height: 34px;\n    color: #525ce5;\n    text-align: center;\n    position: relative;\n    background-color: rgba(82, 92, 229, 0.2); }\n    @media (max-width: 991.98px) {\n      .twitter-bs-wizard .twitter-bs-wizard-nav .step-number {\n        display: block;\n        margin: 0 auto 8px !important;\n        width: 170px; } }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n    display: block;\n    margin-top: 8px;\n    font-weight: 600; }\n    @media (max-width: 575.98px) {\n      .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link .step-title {\n        display: none; } }\n  .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active {\n    background-color: transparent;\n    color: #495057; }\n    .twitter-bs-wizard .twitter-bs-wizard-nav .nav-link.active .step-number {\n      background-color: #525ce5;\n      color: #fff; }\n\n.twitter-bs-wizard .twitter-bs-wizard-pager-link {\n  padding-top: 24px;\n  padding-left: 0;\n  list-style: none;\n  margin-bottom: 0; }\n  .twitter-bs-wizard .twitter-bs-wizard-pager-link li {\n    display: inline-block; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li a {\n      display: inline-block;\n      padding: .47rem .75rem;\n      background-color: #525ce5;\n      color: #fff;\n      border-radius: .25rem; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.disabled a {\n      cursor: not-allowed;\n      background-color: #757dea; }\n    .twitter-bs-wizard .twitter-bs-wizard-pager-link li.next {\n      float: right; }\n\n.twitter-bs-wizard-tab-content {\n  padding-top: 24px;\n  min-height: 262px; }\n\n@media (max-width: 1024px) {\n  .twitter-bs-wizard .twitter-bs-wizard-nav .wizard-border:before {\n    background: transparent !important; } }\n\n.table-rep-plugin .btn-toolbar {\n  display: block; }\n\n.table-rep-plugin .table-responsive {\n  border: none !important; }\n\n.table-rep-plugin .btn-group .btn-default {\n  background-color: #74788d;\n  color: #f9fafc;\n  border: 1px solid #74788d; }\n  .table-rep-plugin .btn-group .btn-default.btn-primary {\n    background-color: #525ce5;\n    border-color: #525ce5;\n    color: #fff;\n    box-shadow: 0 0 0 2px rgba(82, 92, 229, 0.5); }\n\n.table-rep-plugin .btn-group.pull-right {\n  float: right; }\n  .table-rep-plugin .btn-group.pull-right .dropdown-menu {\n    right: 0;\n    transform: none !important;\n    top: 100% !important; }\n\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal; }\n\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px;\n  color: #495057 !important; }\n  .table-rep-plugin .checkbox-row:hover {\n    background-color: #f4f6f9 !important; }\n  .table-rep-plugin .checkbox-row label {\n    display: inline-block;\n    padding-left: 5px;\n    position: relative; }\n    .table-rep-plugin .checkbox-row label::before {\n      -o-transition: 0.3s ease-in-out;\n      -webkit-transition: 0.3s ease-in-out;\n      background-color: #fff;\n      border-radius: 3px;\n      border: 1px solid #eaedf1;\n      content: \"\";\n      display: inline-block;\n      height: 17px;\n      left: 0;\n      margin-left: -20px;\n      position: absolute;\n      transition: 0.3s ease-in-out;\n      width: 17px;\n      outline: none !important; }\n    .table-rep-plugin .checkbox-row label::after {\n      color: #edf1f5;\n      display: inline-block;\n      font-size: 11px;\n      height: 16px;\n      left: 0;\n      margin-left: -20px;\n      padding-left: 3px;\n      padding-top: 1px;\n      position: absolute;\n      top: -1px;\n      width: 16px; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"] {\n    cursor: pointer;\n    opacity: 0;\n    z-index: 1;\n    outline: none !important; }\n    .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label {\n      opacity: 0.65; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:focus + label::before {\n    outline-offset: -2px;\n    outline: none; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    content: \"\\f00c\";\n    font-family: 'Font Awesome 5 Free';\n    font-weight: 900; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:disabled + label::before {\n    background-color: #f9fafc;\n    cursor: not-allowed; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::before {\n    background-color: #525ce5;\n    border-color: #525ce5; }\n  .table-rep-plugin .checkbox-row input[type=\"checkbox\"]:checked + label::after {\n    color: #fff; }\n\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important;\n  background-color: #525ce5; }\n  .table-rep-plugin .fixed-solution .sticky-table-header table {\n    color: #fff; }\n\n.table-rep-plugin table.focus-on tbody tr.focused th,\n.table-rep-plugin table.focus-on tbody tr.focused td,\n.table-rep-plugin .sticky-table-header {\n  background: #525ce5;\n  border-color: #525ce5;\n  color: #fff; }\n  .table-rep-plugin table.focus-on tbody tr.focused th table,\n  .table-rep-plugin table.focus-on tbody tr.focused td table,\n  .table-rep-plugin .sticky-table-header table {\n    color: #fff; }\n\n@media (min-width: 992px) {\n  body[data-layout=\"horizontal\"] .fixed-solution .sticky-table-header {\n    top: 148px !important; } }\n\n.table-striped > tbody > tr:nth-of-type(odd).focused {\n  box-shadow: none !important; }\n  .table-striped > tbody > tr:nth-of-type(odd).focused td, .table-striped > tbody > tr:nth-of-type(odd).focused th {\n    box-shadow: none !important; }\n\n.table-edits input, .table-edits select {\n  height: calc(1.5em + 0.5rem + 2px);\n  padding: 0.25rem 0.5rem;\n  border: 1px solid #ced4da;\n  background-color: #fff;\n  color: #495057;\n  border-radius: 0.25rem; }\n  .table-edits input:focus, .table-edits select:focus {\n    outline: none;\n    border-color: #b1bbc4; }\n\n.apex-charts {\n  min-height: 10px !important; }\n  .apex-charts text {\n    font-family: var(--bs-font-sans-serif) !important;\n    fill: #adb5bd; }\n  .apex-charts .apexcharts-canvas {\n    margin: 0 auto; }\n\n.apexcharts-tooltip-title,\n.apexcharts-tooltip-text {\n  font-family: var(--bs-font-sans-serif) !important; }\n\n.apexcharts-legend-series {\n  font-weight: 500; }\n\n.apexcharts-gridline {\n  pointer-events: none;\n  stroke: #f8f9fa; }\n\n.apexcharts-legend-text {\n  color: #74788d !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-size: 13px !important; }\n\n.apexcharts-pie-label {\n  fill: #fff !important; }\n\n.apexcharts-yaxis text,\n.apexcharts-xaxis text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: #adb5bd; }\n\n.ct-golden-section:before {\n  float: none; }\n\n.ct-chart {\n  max-height: 300px; }\n  .ct-chart .ct-label {\n    fill: #adb5bd;\n    color: #adb5bd;\n    font-size: 12px;\n    line-height: 1; }\n\n.ct-chart.simple-pie-chart-chartist .ct-label {\n  color: #fff;\n  fill: #fff;\n  font-size: 16px; }\n\n.ct-grid {\n  stroke: rgba(52, 58, 64, 0.1); }\n\n.ct-chart .ct-series.ct-series-a .ct-bar,\n.ct-chart .ct-series.ct-series-a .ct-line,\n.ct-chart .ct-series.ct-series-a .ct-point,\n.ct-chart .ct-series.ct-series-a .ct-slice-donut {\n  stroke: #525ce5; }\n\n.ct-chart .ct-series.ct-series-b .ct-bar,\n.ct-chart .ct-series.ct-series-b .ct-line,\n.ct-chart .ct-series.ct-series-b .ct-point,\n.ct-chart .ct-series.ct-series-b .ct-slice-donut {\n  stroke: #23c58f; }\n\n.ct-chart .ct-series.ct-series-c .ct-bar,\n.ct-chart .ct-series.ct-series-c .ct-line,\n.ct-chart .ct-series.ct-series-c .ct-point,\n.ct-chart .ct-series.ct-series-c .ct-slice-donut {\n  stroke: #f14e4e; }\n\n.ct-chart .ct-series.ct-series-d .ct-bar,\n.ct-chart .ct-series.ct-series-d .ct-line,\n.ct-chart .ct-series.ct-series-d .ct-point,\n.ct-chart .ct-series.ct-series-d .ct-slice-donut {\n  stroke: #5ba4e5; }\n\n.ct-chart .ct-series.ct-series-e .ct-bar,\n.ct-chart .ct-series.ct-series-e .ct-line,\n.ct-chart .ct-series.ct-series-e .ct-point,\n.ct-chart .ct-series.ct-series-e .ct-slice-donut {\n  stroke: #23c58f; }\n\n.ct-chart .ct-series.ct-series-f .ct-bar,\n.ct-chart .ct-series.ct-series-f .ct-line,\n.ct-chart .ct-series.ct-series-f .ct-point,\n.ct-chart .ct-series.ct-series-f .ct-slice-donut {\n  stroke: #343a40; }\n\n.ct-chart .ct-series.ct-series-g .ct-bar,\n.ct-chart .ct-series.ct-series-g .ct-line,\n.ct-chart .ct-series.ct-series-g .ct-point,\n.ct-chart .ct-series.ct-series-g .ct-slice-donut {\n  stroke: #6f42c1; }\n\n.ct-series-a .ct-area,\n.ct-series-a .ct-slice-pie {\n  fill: #525ce5; }\n\n.ct-series-b .ct-area,\n.ct-series-b .ct-slice-pie {\n  fill: #23c58f; }\n\n.ct-series-c .ct-area,\n.ct-series-c .ct-slice-pie {\n  fill: #eeb148; }\n\n.ct-series-d .ct-area,\n.ct-series-d .ct-slice-pie {\n  fill: #23c58f; }\n\n.ct-area {\n  fill-opacity: .33; }\n\n.chartist-tooltip {\n  position: absolute;\n  display: inline-block;\n  opacity: 0;\n  min-width: 10px;\n  padding: 2px 10px;\n  border-radius: 3px;\n  background: #343a40;\n  color: #eaedf1;\n  text-align: center;\n  pointer-events: none;\n  z-index: 1;\n  transition: opacity .2s linear; }\n  .chartist-tooltip.tooltip-show {\n    opacity: 1; }\n\n.ct-line {\n  stroke-width: 3px; }\n\n.ct-point {\n  stroke-width: 7px; }\n\n/* Flot chart */\n.flotTip {\n  padding: 8px 12px !important;\n  background-color: #343a40 !important;\n  border: 1px solid #343a40 !important;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  z-index: 100;\n  color: #edf1f5;\n  opacity: 1;\n  border-radius: 3px !important;\n  font-size: 14px !important; }\n\n.legend div {\n  background-color: transparent !important; }\n\n.legend tr {\n  height: 30px; }\n\n.legendLabel {\n  padding-left: 5px;\n  line-height: 10px;\n  padding-right: 10px;\n  font-size: 13px;\n  font-weight: 500;\n  color: #adb5bd; }\n\n.legendColorBox div {\n  border-radius: 3px; }\n  .legendColorBox div div {\n    border-radius: 3px; }\n\n.float-lable-box table {\n  margin: 0 auto; }\n\n@media (max-width: 575.98px) {\n  .legendLabel {\n    display: none; } }\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #343a40 !important;\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #212529 !important; }\n\n.jqsfield {\n  color: #edf1f5 !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-weight: 500 !important; }\n\n.gmaps, .gmaps-panaroma {\n  height: 300px;\n  background: #f9fafc;\n  border-radius: 3px; }\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #525ce5;\n  border-radius: 4px;\n  padding: 10px 20px; }\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute; }\n  .gmaps-overlay_arrow.above {\n    bottom: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-top: 16px solid #525ce5; }\n  .gmaps-overlay_arrow.below {\n    top: -15px;\n    border-left: 16px solid transparent;\n    border-right: 16px solid transparent;\n    border-bottom: 16px solid #525ce5; }\n\n.jvectormap-label {\n  border: none;\n  background: #343a40;\n  color: #f9fafc;\n  font-family: var(--bs-font-sans-serif);\n  font-size: 0.875rem;\n  padding: 5px 8px; }\n\n.editable-input .form-control {\n  display: inline-block; }\n\n.editable-buttons {\n  margin-left: 7px; }\n  .editable-buttons .editable-cancel {\n    margin-left: 7px; }\n\n.home-btn {\n  position: absolute;\n  top: 15px;\n  right: 25px; }\n\n.home-center {\n  display: table;\n  width: 100%;\n  height: 100%; }\n\n.home-desc-center {\n  display: table-cell;\n  vertical-align: middle; }\n\n.authentication-bg {\n  background-image: url(../images/title-img.png);\n  height: 100vh;\n  background-size: cover;\n  background-position: center; }\n\n.authentication-bg .bg-overlay {\n  background-color: #525ce5; }\n\n.error-page {\n  text-transform: uppercase;\n  background: repeating-linear-gradient(45deg, #525ce5, #525ce5 10px, #23c58f 10px, #23c58f 20px);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  font-size: 120px;\n  line-height: .8;\n  position: relative; }\n\n.faq-icon i {\n  width: 30px;\n  height: 30px;\n  line-height: 28px;\n  border: 1px solid;\n  border-radius: 50%;\n  text-align: center;\n  float: right;\n  font-size: 16px;\n  display: inline-block; }\n\n.faq-icon:after {\n  content: \"\";\n  position: absolute;\n  width: 30px;\n  height: 30px;\n  opacity: 0.2;\n  right: 50px;\n  margin-top: -10px;\n  border-radius: 50%;\n  background: #525ce5; }\n\n.search-box .form-control {\n  border-radius: 30px;\n  padding-left: 40px;\n  border: 1px solid #eaedf1; }\n\n.search-box .search-icon {\n  font-size: 16px;\n  position: absolute;\n  left: 13px;\n  top: 0;\n  line-height: 38px; }\n\n.categories-group-list {\n  display: block;\n  color: #343a40;\n  font-weight: 500;\n  padding: 8px 16px; }\n  .categories-group-list[aria-expanded=\"true\"] {\n    background-color: #eaedf1; }\n  .categories-group-list:last-child {\n    border: 0; }\n  .categories-group-list:hover {\n    color: #343a40; }\n\n.categories-list {\n  padding: 8px 0px; }\n  .categories-list li a {\n    display: block;\n    padding: 4px 16px;\n    color: #495057; }\n  .categories-list li.active a {\n    color: #525ce5; }\n\n.product-detai-imgs .nav .nav-link {\n  margin: 7px 0px; }\n  .product-detai-imgs .nav .nav-link.active {\n    background-color: #eaedf1; }\n\n.product-color a {\n  display: inline-block;\n  text-align: center;\n  color: #495057; }\n  .product-color a .product-color-item {\n    margin: 7px;\n    border: 2px solid #edf1f5;\n    border-radius: 4px; }\n  .product-color a.active, .product-color a:hover {\n    color: #525ce5; }\n    .product-color a.active .product-color-item, .product-color a:hover .product-color-item {\n      border-color: #525ce5 !important; }\n\n.product-track {\n  border: 1px solid #edf1f5; }\n\n.ecommerce-sortby-list li {\n  color: #343a40; }\n  .ecommerce-sortby-list li a {\n    color: #495057;\n    padding: 4px; }\n  .ecommerce-sortby-list li.active a {\n    color: #525ce5; }\n\n.product-img {\n  position: relative; }\n  .product-img .product-ribbon {\n    position: absolute;\n    top: 0;\n    left: 0px;\n    padding: 6px 8px;\n    border-radius: 50% 50% 25% 75%/44% 68% 32% 56%;\n    width: 62px;\n    height: 60px;\n    color: #fff;\n    font-size: 15px;\n    text-align: center; }\n  .product-img .product-like {\n    position: absolute;\n    top: 0;\n    right: 0; }\n    .product-img .product-like a {\n      display: inline-block;\n      width: 40px;\n      height: 40px;\n      border: 2px solid #eaedf1;\n      line-height: 38px;\n      border-radius: 50%;\n      text-align: center;\n      color: #adb5bd; }\n\n.product-detail .nav-pills .nav-link {\n  margin-bottom: 7px; }\n  .product-detail .nav-pills .nav-link.active {\n    background-color: #eaedf1; }\n  .product-detail .nav-pills .nav-link .tab-img {\n    width: 5rem; }\n\n.product-detail .product-img {\n  border: 1px solid #edf1f5;\n  padding: 24px; }\n\n.product-desc-list li {\n  padding: 4px 0px; }\n\n.product-review-link .list-inline-item a {\n  color: #74788d; }\n\n.product-review-link .list-inline-item:not(:last-child) {\n  margin-right: 14px; }\n\n.product-cart-touchspin {\n  border: 1px solid #ced4da;\n  background-color: #fff;\n  border-radius: 0.25rem; }\n  .product-cart-touchspin .form-control {\n    border-color: transparent;\n    height: 32px; }\n  .product-cart-touchspin .input-group-btn .btn {\n    background-color: transparent !important;\n    border-color: transparent !important;\n    color: #525ce5 !important;\n    font-size: 16px;\n    padding: 3px 12px;\n    box-shadow: none; }\n\n.shipping-address {\n  box-shadow: none; }\n  .shipping-address.active {\n    border-color: #525ce5 !important; }\n\n.twitter-bs-wizard .chackout-border:before {\n  content: \"\";\n  width: 139px;\n  height: 2px;\n  background: rgba(82, 92, 229, 0.2);\n  position: absolute;\n  top: 26px;\n  margin-left: 100px; }\n\n.twitter-bs-wizard .add-product-border:before {\n  content: \"\";\n  width: 324px;\n  height: 2px;\n  background: rgba(82, 92, 229, 0.2);\n  position: absolute;\n  top: 26px;\n  margin-left: 100px; }\n\n@media (max-width: 1024px) {\n  .twitter-bs-wizard .chackout-border, .twitter-bs-wizard .add-product-border {\n    width: 180px; }\n    .twitter-bs-wizard .chackout-border:before, .twitter-bs-wizard .add-product-border:before {\n      background: transparent !important; } }\n\n/* ==============\r\n  Email\r\n===================*/\n.email-leftbar {\n  width: 236px;\n  float: left;\n  padding: 20px;\n  border-radius: 5px; }\n\n.email-rightbar {\n  margin-left: 260px; }\n\n.chat-user-box p.user-title {\n  color: #343a40;\n  font-weight: 600; }\n\n.chat-user-box p {\n  font-size: 12px; }\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none;\n    width: 100%; }\n  .email-rightbar {\n    margin: 0; } }\n\n.mail-list a {\n  display: block;\n  color: #74788d;\n  line-height: 24px;\n  padding: 8px 5px; }\n  .mail-list a.active {\n    color: #f14e4e;\n    font-weight: 500; }\n\n.message-list {\n  display: block;\n  padding-left: 0; }\n  .message-list li {\n    position: relative;\n    display: block;\n    height: 50px;\n    line-height: 50px;\n    cursor: default;\n    transition-duration: .3s; }\n    .message-list li a {\n      color: #74788d; }\n    .message-list li:hover {\n      background: #eaedf1;\n      transition-duration: .05s; }\n    .message-list li .col-mail {\n      float: left;\n      position: relative; }\n    .message-list li .col-mail-1 {\n      width: 320px; }\n      .message-list li .col-mail-1 .star-toggle,\n      .message-list li .col-mail-1 .checkbox-wrapper-mail,\n      .message-list li .col-mail-1 .dot {\n        display: block;\n        float: left; }\n      .message-list li .col-mail-1 .dot {\n        border: 4px solid transparent;\n        border-radius: 100px;\n        margin: 22px 26px 0;\n        height: 0;\n        width: 0;\n        line-height: 0;\n        font-size: 0; }\n      .message-list li .col-mail-1 .checkbox-wrapper-mail {\n        margin: 15px 10px 0 20px; }\n      .message-list li .col-mail-1 .star-toggle {\n        margin-top: 18px;\n        margin-left: 5px; }\n      .message-list li .col-mail-1 .title {\n        position: absolute;\n        top: 0;\n        left: 110px;\n        right: 0;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap;\n        margin-bottom: 0; }\n    .message-list li .col-mail-2 {\n      position: absolute;\n      top: 0;\n      left: 320px;\n      right: 0;\n      bottom: 0; }\n      .message-list li .col-mail-2 .subject,\n      .message-list li .col-mail-2 .date {\n        position: absolute;\n        top: 0; }\n      .message-list li .col-mail-2 .subject {\n        left: 0;\n        right: 200px;\n        text-overflow: ellipsis;\n        overflow: hidden;\n        white-space: nowrap; }\n      .message-list li .col-mail-2 .date {\n        right: 0;\n        width: 170px;\n        padding-left: 80px; }\n    .message-list li.active, .message-list li.active:hover {\n      box-shadow: inset 3px 0 0 #525ce5; }\n    .message-list li.unread {\n      background-color: #eaedf1;\n      font-weight: 500;\n      color: #292d32; }\n      .message-list li.unread a {\n        color: #292d32;\n        font-weight: 500; }\n  .message-list .checkbox-wrapper-mail {\n    cursor: pointer;\n    height: 20px;\n    width: 20px;\n    position: relative;\n    display: inline-block;\n    box-shadow: inset 0 0 0 1px #ced4da;\n    border-radius: 1px; }\n    .message-list .checkbox-wrapper-mail input {\n      opacity: 0;\n      cursor: pointer; }\n    .message-list .checkbox-wrapper-mail input:checked ~ label {\n      opacity: 1; }\n    .message-list .checkbox-wrapper-mail label {\n      position: absolute;\n      height: 20px;\n      width: 20px;\n      left: 0;\n      cursor: pointer;\n      opacity: 0;\n      margin-bottom: 0;\n      transition-duration: .05s;\n      top: 0; }\n      .message-list .checkbox-wrapper-mail label:before {\n        content: \"\\F012C\";\n        font-family: \"Material Design Icons\";\n        top: 0;\n        height: 20px;\n        color: #292d32;\n        width: 20px;\n        position: absolute;\n        margin-top: -16px;\n        left: 4px;\n        font-size: 13px; }\n\n@media (max-width: 575.98px) {\n  .message-list li .col-mail-1 {\n    width: 200px; } }\n\n@media (min-width: 992px) {\n  .chat-leftsidebar {\n    min-width: 380px; } }\n\n.chat-leftsidebar .chat-leftsidebar-nav .nav {\n  background-color: #fff; }\n\n.chat-noti-dropdown.active:before {\n  content: \"\";\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background-color: #f14e4e;\n  border-radius: 50%;\n  right: 0; }\n\n.chat-noti-dropdown .btn {\n  padding: 6px;\n  box-shadow: none;\n  font-size: 20px; }\n\n.chat-list {\n  margin: 0; }\n  .chat-list li.active a {\n    background-color: #fff;\n    box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n  .chat-list li a {\n    display: block;\n    padding: 14px 16px;\n    color: #74788d;\n    transition: all 0.4s;\n    border: 1px solid #edf1f5;\n    border-radius: 4px;\n    margin-top: 10px; }\n    .chat-list li a:hover {\n      background-color: #fff;\n      box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n.user-chat-nav .dropdown .nav-btn {\n  height: 40px;\n  width: 40px;\n  line-height: 34px;\n  box-shadow: none;\n  padding: 0;\n  font-size: 16px;\n  background-color: #f9fafc;\n  border-radius: 50%; }\n\n.user-chat-nav .dropdown .dropdown-menu {\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  border: 1px solid #edf1f5; }\n\n.chat-conversation li {\n  clear: both; }\n\n.chat-conversation .chat-day-title {\n  position: relative;\n  text-align: center;\n  margin-bottom: 24px; }\n  .chat-conversation .chat-day-title .title {\n    background-color: #fff;\n    position: relative;\n    z-index: 1;\n    padding: 6px 24px; }\n  .chat-conversation .chat-day-title:before {\n    content: \"\";\n    position: absolute;\n    width: 100%;\n    height: 1px;\n    left: 0;\n    right: 0;\n    background-color: #edf1f5;\n    top: 10px; }\n  .chat-conversation .chat-day-title .badge {\n    font-size: 12px; }\n\n.chat-conversation .conversation-list {\n  margin-bottom: 24px;\n  display: inline-block;\n  position: relative; }\n  .chat-conversation .conversation-list .arrow-left {\n    position: relative; }\n    .chat-conversation .conversation-list .arrow-left:before {\n      content: \"\";\n      position: absolute;\n      top: 10px;\n      right: 100%;\n      border: 7px solid transparent;\n      border-right: 7px solid rgba(82, 92, 229, 0.1); }\n  .chat-conversation .conversation-list .ctext-wrap {\n    padding: 12px 24px;\n    background-color: rgba(82, 92, 229, 0.1);\n    border-radius: 8px 8px 8px 0px;\n    overflow: hidden; }\n    .chat-conversation .conversation-list .ctext-wrap .conversation-name {\n      font-weight: 500;\n      color: #525ce5;\n      margin-bottom: 4px;\n      position: relative; }\n  .chat-conversation .conversation-list .dropdown {\n    float: right; }\n    .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n      font-size: 18px;\n      padding: 4px;\n      color: #74788d; }\n      @media (max-width: 575.98px) {\n        .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n          display: none; } }\n    .chat-conversation .conversation-list .dropdown .dropdown-menu {\n      box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n      border: 1px solid #edf1f5; }\n  .chat-conversation .conversation-list .chat-time {\n    font-size: 12px; }\n\n.chat-conversation .right .conversation-list {\n  float: right; }\n  .chat-conversation .right .conversation-list .arrow-right {\n    position: relative; }\n    .chat-conversation .right .conversation-list .arrow-right:before {\n      content: \"\";\n      position: absolute;\n      top: 10px;\n      left: 100%;\n      border: 7px solid transparent;\n      border-left: 7px solid #f9fafc; }\n  .chat-conversation .right .conversation-list .ctext-wrap {\n    background-color: #f9fafc;\n    text-align: right;\n    border-radius: 8px 8px 0px 8px; }\n  .chat-conversation .right .conversation-list .dropdown {\n    float: left; }\n  .chat-conversation .right .conversation-list.last-chat .conversation-list:before {\n    right: 0;\n    left: auto; }\n\n.chat-input-section {\n  border-top: 1px solid #edf1f5; }\n\n.chat-input {\n  border-radius: 30px;\n  background-color: #f9fafc !important;\n  border-color: #f9fafc !important;\n  padding-right: 120px; }\n\n.chat-input-links {\n  position: absolute;\n  right: 16px;\n  top: 50%;\n  transform: translateY(-50%); }\n  .chat-input-links li a {\n    font-size: 16px;\n    line-height: 36px;\n    padding: 0px 4px;\n    display: inline-block; }\n\n@media (max-width: 575.98px) {\n  .chat-send {\n    min-width: auto; } }\n\n.search-box .search-icon {\n  font-size: 16px;\n  position: absolute;\n  left: 13px;\n  top: 2px;\n  font-size: 15px;\n  line-height: 34px; }\n\n.search-box .form-control {\n  padding-left: 40px;\n  border-radius: 5px; }\n\n.counter-number {\n  font-size: 32px;\n  text-align: center; }\n  .counter-number span {\n    font-size: 16px;\n    display: block;\n    padding-top: 7px; }\n\n.coming-box {\n  float: left;\n  width: 21%;\n  padding: 14px 7px;\n  margin: 0px 12px 24px 12px;\n  background-color: #fff;\n  border-radius: 5px;\n  border-radius: 0.25rem;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1); }\n\n@media (max-width: 991.98px) {\n  .coming-box {\n    width: 40%; } }\n\n/************** vertical timeline **************/\n.timeline {\n  position: relative;\n  width: 100%;\n  padding: 30px 0; }\n\n.timeline .timeline-end,\n.timeline .timeline-start,\n.timeline .timeline-year {\n  position: relative;\n  width: 100%;\n  text-align: center;\n  z-index: 1; }\n\n.timeline .timeline-end p,\n.timeline .timeline-start p,\n.timeline .timeline-year p {\n  display: inline-block;\n  width: 80px;\n  height: 80px;\n  margin: 0;\n  padding: 30px 0;\n  text-align: center;\n  background: url(../images/user-img.png);\n  background-color: #525ce5;\n  background-repeat: no-repeat;\n  background-size: cover;\n  border-radius: 100px;\n  color: #fff;\n  text-transform: uppercase; }\n\n.timeline .timeline-year {\n  margin: 30px 0; }\n\n.timeline .timeline-continue {\n  position: relative;\n  width: 100%;\n  padding: 60px 0; }\n  .timeline .timeline-continue:after {\n    position: absolute;\n    content: \"\";\n    width: 1px;\n    height: 100%;\n    top: 0;\n    left: 50%;\n    margin-left: -1px;\n    background: #525ce5; }\n\n.timeline .timeline-date {\n  margin: 40px 10px 0 10px; }\n\n.timeline .row.timeline-left,\n.timeline .row.timeline-right .timeline-date {\n  text-align: right; }\n\n.timeline .row.timeline-right,\n.timeline .row.timeline-left .timeline-date {\n  text-align: left; }\n\n.timeline .timeline-date::after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 14px;\n  height: 14px;\n  top: 45px;\n  background: #525ce5;\n  border-radius: 15px;\n  z-index: 1; }\n\n.timeline .row.timeline-left .timeline-date::after {\n  left: -7px; }\n\n.timeline .row.timeline-right .timeline-date::after {\n  right: -7px; }\n\n.timeline .timeline-box,\n.timeline .timeline-launch {\n  position: relative;\n  display: inline-block;\n  margin: 15px;\n  padding: 20px;\n  border: 1px solid #edf1f5;\n  border-radius: 6px; }\n\n.timeline .timeline-launch {\n  width: 100%;\n  margin: 15px 0;\n  padding: 0;\n  border: none;\n  text-align: center;\n  background: transparent; }\n\n.timeline .timeline-box::after,\n.timeline .timeline-box::before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 0;\n  height: 0;\n  border-style: solid; }\n\n.timeline .row.timeline-left .timeline-box::after,\n.timeline .row.timeline-left .timeline-box::before {\n  left: 100%; }\n\n.timeline .row.timeline-right .timeline-box::after,\n.timeline .row.timeline-right .timeline-box::before {\n  right: 100%; }\n\n.timeline .timeline-launch .timeline-box::after,\n.timeline .timeline-launch .timeline-box::before {\n  left: 50%;\n  margin-left: -10px; }\n\n.timeline .timeline-box::after {\n  top: 26px;\n  border-color: transparent transparent transparent #f9fafc;\n  border-width: 10px; }\n\n.timeline .timeline-box::before {\n  top: 25px;\n  border-color: transparent transparent transparent #edf1f5;\n  border-width: 11px; }\n\n.timeline .row.timeline-right .timeline-box::after {\n  border-color: transparent #f9fafc transparent transparent; }\n\n.timeline .row.timeline-right .timeline-box::before {\n  border-color: transparent #edf1f5 transparent transparent; }\n\n.timeline .timeline-launch .timeline-box::after {\n  top: -20px;\n  border-color: transparent transparent #edf1f5 transparent; }\n\n.timeline .timeline-launch .timeline-box::before {\n  top: -19px;\n  border-color: transparent transparent #f9fafc transparent;\n  border-width: 10px;\n  z-index: 1; }\n\n.timeline .timeline-launch .timeline-text {\n  width: 100%; }\n\n@media (max-width: 767px) {\n  .timeline .timeline-continue::after {\n    left: 40px; }\n  .timeline .timeline-end,\n  .timeline .timeline-start,\n  .timeline .timeline-year,\n  .timeline .row.timeline-left,\n  .timeline .row.timeline-right .timeline-date,\n  .timeline .row.timeline-right,\n  .timeline .row.timeline-left .timeline-date,\n  .timeline .timeline-launch {\n    text-align: left; }\n  .timeline .row.timeline-left .timeline-date::after,\n  .timeline .row.timeline-right .timeline-date::after {\n    left: 47px; }\n  .timeline .timeline-box,\n  .timeline .row.timeline-right .timeline-date,\n  .timeline .row.timeline-left .timeline-date {\n    margin-left: 55px; }\n  .timeline .timeline-launch .timeline-box {\n    margin-left: 0; }\n  .timeline .row.timeline-left .timeline-box::after {\n    left: -20px;\n    border-color: transparent #f9fafc transparent transparent; }\n  .timeline .row.timeline-left .timeline-box::before {\n    left: -22px;\n    border-color: transparent #edf1f5 transparent transparent; }\n  .timeline .timeline-launch .timeline-box::after,\n  .timeline .timeline-launch .timeline-box::before {\n    left: 30px;\n    margin-left: 0; } }\n\n.pricing-nav-tabs {\n  display: inline-block;\n  background-color: #fff;\n  box-shadow: 0 2px 4px rgba(126, 142, 177, 0.1);\n  padding: 4px;\n  border-radius: 7px; }\n  .pricing-nav-tabs li {\n    display: inline-block; }\n\n.pricing-box .plan-features li {\n  padding: 7px 0px; }\n\n/*********************\r\n    Faqs\r\n**********************/\n.faq-nav-tabs .nav-item {\n  margin: 0px 8px; }\n\n.faq-nav-tabs .nav-link {\n  text-align: center;\n  margin-bottom: 8px;\n  border: 2px solid #edf1f5;\n  color: #495057; }\n  .faq-nav-tabs .nav-link .nav-icon {\n    font-size: 40px;\n    margin-bottom: 8px;\n    display: block; }\n  .faq-nav-tabs .nav-link.active {\n    border-color: #525ce5;\n    background-color: transparent;\n    color: #495057; }\n    .faq-nav-tabs .nav-link.active .nav-icon {\n      color: #525ce5; }\n\n.text-error {\n  font-size: 120px; }\n  @media (max-width: 575.98px) {\n    .text-error {\n      font-size: 86px; } }\n\n.error-text {\n  color: #f14e4e;\n  position: relative; }\n  .error-text .error-img {\n    position: absolute;\n    width: 120px;\n    left: -15px;\n    right: 0;\n    bottom: 47px; }\n    @media (max-width: 575.98px) {\n      .error-text .error-img {\n        width: 86px;\n        left: -12px;\n        bottom: 38px; } }\n", "// \r\n// Page-title\r\n// \r\n\r\n.page-title-box {\r\n\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: 0;\r\n    }\r\n\r\n    h4 {\r\n        color: $white;\r\n        text-transform: uppercase;\r\n        font-weight: 500;\r\n        font-size: 16px !important;\r\n    }\r\n}\r\n\r\n.topbar-social-icon{\r\n    padding: calc(#{$header-height - 32px} / 2) 0;\r\n}\r\n\r\n.title-tooltip{\r\n    li{\r\n       i{\r\n        font-size: 20px;\r\n        margin-left: 10px;\r\n        color: $header-item-color;\r\n       }\r\n    }\r\n}", "// \r\n// _footer.scss\r\n// \r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 20px calc(#{$grid-gutter-width} / 2);\r\n    position: absolute;\r\n    right: 0;\r\n    border-top: 1px solid $gray-200;\r\n    color: $footer-color;\r\n    left: $sidebar-width;\r\n    height: $footer-height;\r\n    box-shadow: $box-shadow;\r\n    background-color: $footer-bg;\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .footer {\r\n        left: 0;\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n    .footer {\r\n        left: $sidebar-collapsed-width;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .footer {\r\n        left: 0 !important;\r\n    }  \r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: $card-bg;\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .right-bar-toggle {\r\n        background-color: lighten($dark, 7%);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        color: $gray-200;\r\n        text-align: center;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n            background-color: lighten($dark, 10%);\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\r\n// _menu.scss\r\n// \r\n\r\n.metismenu {\r\n    margin: 0;\r\n\r\n    li {\r\n        display: block;\r\n        width: 100%;\r\n    }\r\n\r\n    .mm-collapse {\r\n        display: none;\r\n\r\n        &:not(.mm-show) {\r\n            display: none;\r\n        }\r\n\r\n        &.mm-show {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n\r\n.vertical-menu {\r\n    width: $sidebar-width;\r\n    z-index: 1001;\r\n    background: $sidebar-bg;\r\n    bottom: 0;\r\n    margin-top: 0;\r\n    position: fixed;\r\n    top: $header-height;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.user-sidebar{\r\n    position: relative;\r\n    text-align: center;\r\n    background: url(../images/user-img.png);\r\n    background-color: $primary;\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    background-position: center;\r\n    padding: 20px 0;\r\n    .user-img{\r\n        position: relative;\r\n        img{\r\n            width: 60px;\r\n            height: 60px;\r\n            border: 3px solid $success;\r\n            padding: 5px;\r\n        }\r\n        .avatar-online{\r\n            position: absolute;\r\n    bottom: 4px;\r\n    width: 10px;\r\n    height: 10px;\r\n    z-index: 1;\r\n    border: 2px solid transparent;\r\n    border-radius: 50%;\r\n    margin-left: -15px;\r\n        }\r\n    }\r\n}\r\n\r\n.main-content {\r\n    margin-left: $sidebar-width;\r\n    overflow: hidden;\r\n\r\n    .content {\r\n        padding: 0 15px 10px 15px;\r\n        margin-top: $header-height;\r\n    }\r\n}\r\n\r\n\r\n#sidebar-menu {\r\n    padding: 0px 0 30px 0;\r\n\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(90deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .has-arrow {\r\n        &:after {\r\n            content: \"\\F0142\";\r\n            font-family: 'Material Design Icons';\r\n            display: block;\r\n            float: right;\r\n            transition: transform .2s;\r\n            font-size: 1rem;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: .625rem 1.2rem;\r\n                color: $sidebar-menu-item-color;\r\n                position: relative;\r\n                font-size: 14.5px;\r\n                transition: all .4s;\r\n                margin: 0px 17px;\r\n                border-radius: 3px;\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    min-width: 1.75rem;\r\n                    padding-bottom: .125em;\r\n                    font-size: 16px;\r\n                    line-height: 1.40625rem;\r\n                    vertical-align: middle;\r\n                    color: $sidebar-menu-item-icon-color;\r\n                    transition: all .4s;\r\n                }\r\n\r\n                &:hover {\r\n                    color: $sidebar-menu-item-hover-color;\r\n\r\n                    i {\r\n                        color: $sidebar-menu-item-hover-color;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                margin-top: 5px;\r\n            }\r\n\r\n            ul.sub-menu {\r\n                padding: 0;\r\n\r\n                li {\r\n\r\n                    a {\r\n                        padding: .4rem 1.5rem .4rem 2.8rem;\r\n                        font-size: 14px;\r\n                        color: $sidebar-menu-sub-item-color;\r\n                        background-color: transparent !important;\r\n                        &:before{\r\n                            content: \"\\F09DF\";\r\n                            font-family: 'Material Design Icons';\r\n                            font-size: 20px;\r\n                            line-height: 10px;\r\n                            padding-right: 2px;\r\n                            vertical-align: middle;\r\n                            display: inline-block;\r\n                        }\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        padding: 0;\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 1.5rem .4rem 4rem;\r\n                                font-size: 14px;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.menu-title {\r\n    padding: 12px 20px !important;\r\n    letter-spacing: .05em;\r\n    pointer-events: none;\r\n    cursor: default;\r\n    font-size: 11px;\r\n    text-transform: uppercase;\r\n    color: $sidebar-menu-item-icon-color;\r\n    font-weight: $font-weight-semibold;\r\n}\r\n\r\n.mm-active {\r\n    color: $sidebar-menu-item-active-color !important;\r\n    > a{\r\n        color: $sidebar-menu-item-active-color !important;\r\n        background-color: $sidebar-menu-item-active-bg !important;\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    > i {\r\n        color: $sidebar-menu-item-active-color !important;\r\n    }\r\n    .active {\r\n        color: $sidebar-menu-item-active-color !important;\r\n        background-color: $sidebar-menu-item-active-bg !important;\r\n\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .vertical-menu {\r\n        display: none;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    body.sidebar-enable {\r\n        .vertical-menu {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n\r\n    .user-sidebar{\r\n        display: none;\r\n    }\r\n\r\n\r\n    .main-content {\r\n        margin-left: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        width: $sidebar-collapsed-width !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    // Side menu\r\n    .vertical-menu {\r\n        position: absolute;\r\n        width: $sidebar-collapsed-width !important;\r\n        z-index: 5;\r\n\r\n        .simplebar-mask,\r\n        .simplebar-content-wrapper {\r\n            overflow: visible !important;\r\n        }\r\n\r\n        .simplebar-scrollbar {\r\n            display: none !important;\r\n        }\r\n\r\n        .simplebar-offset {\r\n            bottom: 0 !important;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            .menu-title,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n\r\n            .has-arrow {\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 55px;\r\n                        transition: none;\r\n                        margin: 0;\r\n                        \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $sidebar-menu-item-hover-color;\r\n                        }\r\n\r\n                        i {\r\n                            font-size: 1.15rem;\r\n                            margin-left: 4px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$sidebar-collapsed-width});\r\n                            background-color: $sidebar-menu-item-active-bg;\r\n                            transition: none;\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        >ul {\r\n                            display: block;\r\n                            left: $sidebar-collapsed-width;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n                            }\r\n\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                color: $sidebar-menu-sub-item-color;\r\n                                margin: 0;\r\n\r\n                                &:hover {\r\n                                    color: $sidebar-menu-item-hover-color;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $sidebar-bg;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                height: auto !important;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li.active {\r\n                        a {\r\n                            color: $gray-100;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n\r\n    .user-sidebar{\r\n        background: none;\r\n    }\r\n\r\n\r\n    .vertical-menu {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    #sidebar-menu {\r\n    \r\n        ul {\r\n            li {\r\n                a {\r\n                    color: $sidebar-dark-menu-item-color;\r\n\r\n                    i {\r\n                        color: $sidebar-dark-menu-item-icon-color;\r\n                    }\r\n    \r\n                    &:hover {\r\n                        color: $sidebar-dark-menu-item-hover-color;\r\n\r\n                        i {\r\n                            color: $sidebar-dark-menu-item-hover-color;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n\r\n                        a {\r\n                            color: $sidebar-dark-menu-sub-item-color;\r\n\r\n                            &:hover {\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        min-height: 1400px;\r\n\r\n        // Side menu\r\n        .vertical-menu {\r\n\r\n            // Sidebar Menu\r\n            #sidebar-menu {\r\n\r\n                > ul {\r\n                    > li {\r\n                        \r\n                        &:hover {\r\n                            > a {\r\n                                background: lighten($sidebar-dark-bg, 2%);\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                                i{\r\n                                    color: $sidebar-dark-menu-item-hover-color;\r\n                                }\r\n                            }\r\n\r\n                            >ul {\r\n                                a{\r\n                                    color: $sidebar-dark-menu-sub-item-color;\r\n                                    &:hover{\r\n                                        color: $sidebar-menu-item-hover-color;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    ul{\r\n                        background-color: lighten($card-bg, 1%);\r\n                    }\r\n                    \r\n                }\r\n\r\n                ul{\r\n\r\n                    >li{\r\n                        >a{\r\n                            &.mm-active{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li{\r\n                        li{\r\n                            &.mm-active, &.active {\r\n                               > a{\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n                            }\r\n\r\n                            a{\r\n                                &.mm-active, &.active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                }\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                    \r\n                    \r\n                }\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n    \r\n    .mm-active {\r\n        color: $sidebar-dark-menu-item-active-color !important;\r\n        > a{\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            background-color: $sidebar-dark-menu-item-active-bg !important;\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n        }\r\n        .active {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            background-color: $sidebar-dark-menu-item-active-bg !important;\r\n\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $sidebar-dark-menu-item-icon-color;\r\n    }\r\n}\r\n\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n// Compact Sidebar\r\n\r\nbody[data-sidebar-size=\"small\"] {\r\n    .navbar-brand-box{\r\n        width: $sidebar-width-sm;\r\n    }\r\n    .vertical-menu{\r\n        width: $sidebar-width-sm;\r\n        text-align: center;\r\n\r\n        .has-arrow:after,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n    }\r\n    .main-content {\r\n        margin-left: $sidebar-width-sm;\r\n    }\r\n    .footer {\r\n        left: $sidebar-width-sm;\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul li {\r\n            a{\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n            ul.sub-menu {\r\n                li{\r\n                    a{\r\n                        padding-left: 1.5rem;\r\n                        &:before{\r\n                            display: none;\r\n                        }\r\n                    }\r\n                   \r\n                    \r\n                    ul.sub-menu {\r\n                        li {\r\n                            a{\r\n                                padding-left: 1.5rem;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.vertical-collpsed {\r\n        .main-content {\r\n            margin-left: $sidebar-collapsed-width;\r\n        }\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: left;\r\n                >ul{\r\n                    >li{\r\n                        >a {\r\n                            i{\r\n                                display: inline-block;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .footer {\r\n            left: $sidebar-collapsed-width;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// RTL\r\n[dir=\"rtl\"] \r\n#sidebar-menu {\r\n    .has-arrow{\r\n        &:after{\r\n            content: \"\\F0141\";\r\n            transition: transform .2s;\r\n        }\r\n    }\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(90deg);\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// _horizontal.scss\r\n// \r\n// \r\n// _horizontal.scss\r\n// \r\n\r\n.topnav {\r\n    background: $topnav-bg;\r\n    padding: 0 calc(#{$grid-gutter-width} / 2);\r\n    box-shadow: $box-shadow;\r\n    margin-top: $header-height;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    \r\n    .topnav-menu {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .navbar-nav {\r\n        \r\n        .nav-link {\r\n            font-size: 15px;\r\n            position: relative;\r\n            padding: 1.2rem 1.5rem;\r\n            color: $sidebar-menu-item-color;\r\n            i{\r\n                font-size: 15px;\r\n                top: 2px;\r\n                position: relative;\r\n            }\r\n            &:focus, &:hover{\r\n                color: $sidebar-menu-item-active-color;\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        \r\n        .dropdown-item{\r\n            color: $sidebar-menu-item-color;\r\n            &.active, &:hover{\r\n                color: $sidebar-menu-item-active-color;\r\n                background: transparent;\r\n            }\r\n        }\r\n        \r\n        .nav-item{\r\n            .nav-link.active{\r\n                color: $sidebar-menu-item-active-color;\r\n            }\r\n        }\r\n\r\n        .dropdown{\r\n            &.active{\r\n              >a {\r\n                    color: $sidebar-menu-item-active-color;\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n    body[data-layout=\"horizontal\"] {\r\n        .container-fluid,\r\n        .navbar-header {\r\n            max-width: 85%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            padding: .5rem 1.5rem;\r\n            min-width: 180px;\r\n        }\r\n\r\n        .dropdown {\r\n            &.mega-dropdown{\r\n                // position: static;\r\n                .mega-dropdown-menu{\r\n                    left: 0px;\r\n                    right: auto;\r\n                }\r\n            }\r\n            .dropdown-menu {\r\n                margin-top: 0;\r\n                border-radius: 0 0 $dropdown-border-radius $dropdown-border-radius;\r\n\r\n                .arrow-down {\r\n                    &::after {\r\n                        right: 15px;\r\n                        transform: rotate(-135deg) translateY(-50%);\r\n                        position: absolute;\r\n                    }\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu {\r\n                        position: absolute;\r\n                        top: 0 !important;\r\n                        left: 100%;\r\n                        display: none\r\n                    }\r\n                }\r\n            }\r\n\r\n            &:hover {\r\n                >.dropdown-menu {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown:hover>.dropdown-menu>.dropdown:hover>.dropdown-menu {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .navbar-toggle {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.arrow-down {\r\n    display: inline-block;\r\n\r\n    &:after {\r\n        border-color: initial;\r\n        border-style: solid;\r\n        border-width: 0 0 1px 1px;\r\n        content: \"\";\r\n        height: .4em;\r\n        display: inline-block;\r\n        right: 5px;\r\n        top: 50%;\r\n        margin-left: 10px;\r\n        transform: rotate(-45deg) translateY(-50%);\r\n        transform-origin: top;\r\n        transition: all .3s ease-out;\r\n        width: .4em;\r\n    }\r\n}\r\n\r\n\r\n\r\n@include media-breakpoint-down(xl) {\r\n    .topnav-menu {\r\n        .navbar-nav {\r\n            li {\r\n                &:last-of-type {\r\n                    .dropdown {\r\n                        .dropdown-menu {\r\n                            right: 100%;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(lg) {\r\n\r\n    .navbar-brand-box{\r\n        .logo-dark {\r\n            display: $display-block;\r\n            span.logo-sm{\r\n                display: $display-block;\r\n            }\r\n        }\r\n    \r\n        .logo-light {\r\n            display: $display-none;\r\n        }\r\n    }\r\n    \r\n    .topnav {\r\n        max-height: 360px;\r\n        overflow-y: auto;\r\n        padding: 0;\r\n        .navbar-nav {\r\n            .nav-link {\r\n                padding: 0.75rem 1.1rem;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                background-color: transparent;\r\n                border: none;\r\n                box-shadow: none;\r\n                padding-left: 20px;\r\n                &.dropdown-mega-menu-xl{\r\n                    width: auto;\r\n    \r\n                    .row{\r\n                        margin: 0px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .dropdown-item {\r\n                position: relative;\r\n                background-color: transparent;\r\n\r\n                &.active,\r\n                &:active {\r\n                    color: $sidebar-menu-item-active-color;\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-down {\r\n            &::after {\r\n                right: 15px;\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n        .navbar-brand-box{\r\n            .logo-dark {\r\n                display: $display-block;\r\n            }\r\n        \r\n            .logo-light {\r\n                display: $display-none;\r\n            }\r\n        }\r\n        .topnav{\r\n            background-color: #141b2d;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.5);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                    >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n//  Topbar \r\n\r\nbody[data-layout=\"horizontal\"]{\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n}\r\n\r\n\r\n\r\nbody[data-topbar=\"colored\"] {\r\n    #page-topbar { \r\n        background-color: tint-color($header-colored-bg, 20%);\r\n    }\r\n    .navbar-header {\r\n        .dropdown {\r\n            .show.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .title-tooltip{\r\n        li{\r\n           i{\r\n            color:  rgba($white,0.8);\r\n           }\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color:  rgba($white,0.5);\r\n    \r\n        &:hover {\r\n            color: $white;\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color:  rgba($white,0.5);\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba($topbar-search-bg,0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}", "// \r\n// _layouts.scss\r\n// \r\n\r\nbody[data-layout-size=\"boxed\"] {\r\n    background-color: $boxed-body-bg;\r\n    #layout-wrapper {\r\n        background-color: $body-bg;\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n        box-shadow: $box-shadow;\r\n    }\r\n\r\n    #page-topbar {\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .footer {\r\n        margin: 0 auto;\r\n        max-width: calc(#{$boxed-layout-width} - #{$sidebar-width});\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .footer {\r\n            max-width: calc(#{$boxed-layout-width} - #{$sidebar-collapsed-width});\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Horizontal Boxed Layout\r\n\r\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"]{\r\n    #page-topbar, #layout-wrapper, .footer {\r\n        max-width: 100%;\r\n    }\r\n    .container-fluid, .navbar-header {\r\n        max-width: $boxed-layout-width;\r\n    }\r\n}", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\n// avatar.scss\n//\n\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 2.5rem;\n  width: 2.5rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.mini-stat-icon{\n\n  width: 46px;\n  height: 46px;\n\n}\n\n.avatar-title {\n  align-items: center;\n  display: flex;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .card {\r\n        + .card {\r\n            margin-top: 0.5rem;\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0415\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .card-header{\r\n        border-radius: 7px;\r\n    }\r\n}\r\n\r\n.custom-accordion-arrow{\r\n    .card{\r\n        border: 1px solid $border-color;\r\n        box-shadow: none;\r\n    }\r\n    .card-header{\r\n        padding-left: 45px;\r\n        position: relative;\r\n\r\n        .accor-arrow-icon{\r\n            position: absolute;\r\n            display: inline-block;\r\n            width: 24px;\r\n            height: 24px;\r\n            line-height: 24px;\r\n            font-size: 16px;\r\n            background-color: $primary;\r\n            color: $white;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            left: 10px;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n        }\r\n    }\r\n\r\n    a {\r\n        &.collapsed {\r\n            i.accor-arrow-icon {\r\n                &:before {\r\n                    content: \"\\F0142\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "//\n// _helper.scss\n//\n\n\n.font-size-10 {\n    font-size: 10px !important;\n}\n\n.font-size-11 {\n    font-size: 11px !important;\n}\n\n.font-size-12 {\n    font-size: 12px !important;\n}\n\n.font-size-13 {\n    font-size: 13px !important;\n}\n\n.font-size-14 {\n    font-size: 14px !important;\n}\n\n.font-size-15 {\n    font-size: 15px !important;\n}\n\n.font-size-16 {\n    font-size: 16px !important;\n}\n\n.font-size-17 {\n    font-size: 17px !important;\n}\n\n.font-size-18 {\n    font-size: 18px !important;\n}\n\n.font-size-20 {\n    font-size: 20px !important;\n}\n\n.font-size-22 {\n    font-size: 22px !important;\n}\n\n.font-size-24 {\n    font-size: 24px !important;\n}\n\n\n// media\n\n.media{\n    display: flex;\n    align-items: flex-start;\n  }\n  \n  .media-body {\n    flex: 1;\n  }\n  \n\n\n\n// Social\n\n.social-list-item {\n    height: 2rem;\n    width: 2rem;\n    line-height: calc(2rem - 2px);\n    display: block;\n    border: 1px solid $gray-500;\n    border-radius: 50%;\n    color: $gray-500;\n    text-align: center;\n    transition: all 0.4s;\n\n    &:hover {\n        color: $gray-600;\n        background-color: $gray-200;\n    }\n}\n\n\n.w-xs {\n    min-width: 80px;\n}\n\n.w-sm {\n    min-width: 95px;\n}\n\n.w-md {\n    min-width: 110px;\n}\n\n.w-lg {\n    min-width: 140px;\n}\n\n.w-xl {\n    min-width: 160px;\n}\n\n// overlay\n\n.bg-overlay {\n    position: absolute;\n    height: 100%;\n    width: 100%;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    top: 0;\n    opacity: 0.7;\n    background-color: $black;\n}\n\n// flex-1\n\n.flex-1{\n    flex: 1;\n}\n\n\n\n// alert\n\n.alert-dismissible {\n    .btn-close {\n        font-size: 10px;\n        padding: $alert-padding-y * 1.4 $alert-padding-x;\n        background: transparent escape-svg($btn-close-bg-dark) center / $btn-close-width auto no-repeat;\n    }\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: $card-bg;\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    width: 40px;\r\n    height: 40px;\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner-chase {\r\n    margin: 0 auto;\r\n    width: 40px;\r\n    height: 40px;\r\n    position: relative;\r\n    animation: spinner-chase 2.5s infinite linear both;\r\n}\r\n\r\n.chase-dot {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0; \r\n    animation: chase-dot 2.0s infinite ease-in-out both; \r\n    &:before {\r\n        content: '';\r\n        display: block;\r\n        width: 25%;\r\n        height: 25%;\r\n        background-color: $primary;\r\n        border-radius: 100%;\r\n        animation: chase-dot-before 2.0s infinite ease-in-out both; \r\n    }\r\n\r\n    &:nth-child(1) { \r\n        animation-delay: -1.1s; \r\n        &:before{\r\n            animation-delay: -1.1s;\r\n        }\r\n    }\r\n    &:nth-child(2) { \r\n        animation-delay: -1.0s;\r\n        &:before{\r\n            animation-delay: -1.0s;\r\n        }\r\n    }\r\n    &:nth-child(3) { \r\n        animation-delay: -0.9s;\r\n        &:before{\r\n            animation-delay: -0.9s;\r\n        } \r\n    }\r\n    &:nth-child(4) { \r\n        animation-delay: -0.8s; \r\n        &:before{\r\n            animation-delay: -0.8s;\r\n        } \r\n    }\r\n    &:nth-child(5) { \r\n        animation-delay: -0.7s; \r\n        &:before{\r\n            animation-delay: -0.7s;\r\n        } \r\n    }\r\n    &:nth-child(6) { \r\n        animation-delay: -0.6s; \r\n        &:before{\r\n            animation-delay: -0.6s;\r\n        }\r\n    }\r\n}\r\n\r\n@keyframes spinner-chase {\r\n    100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot {\r\n    80%, 100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot-before {\r\n    50% {\r\n        transform: scale(0.4); \r\n    } \r\n    100%, 0% {\r\n        transform: scale(1.0); \r\n    } \r\n}", "//\r\n// Forms.scss\r\n//\r\n\r\n\r\n// checkbox input right\r\n\r\n.form-check-right{\r\n  padding-left: 0;\r\n  display: inline-block;\r\n  padding-right: $form-check-padding-start;;\r\n  .form-check-input{\r\n    float: right;\r\n    margin-left: 0;\r\n    margin-right: $form-check-padding-start * -1;\r\n  }\r\n  .form-check-label{\r\n    display: block;\r\n  }\r\n}\r\n\r\n.form-check{\r\n  position: relative;\r\n  text-align: left /*rtl: right*/;\r\n}\r\n\r\n\r\n.form-check-label{\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}", "// \r\n// Widgets.scss\r\n// \r\n\r\n// \r\n// Widgets.scss\r\n// \r\n\r\n.dash-summary{\r\n    border-top: 1px solid $gray-300;\r\n}\r\n\r\n.dash-main-border{\r\n    border-bottom: 1px solid $gray-300;\r\n}\r\n\r\n.dash-info-widget{\r\n    background: $dash-info-bg;\r\n}\r\n\r\n.dash-goal{\r\n    border-left: 1px solid $gray-300;\r\n}\r\n@media (max-width: 768px) {\r\n    .dash-goal{\r\n        border-left: none;\r\n    }\r\n}\r\n\r\n.carousel-indicators {\r\n    bottom: -20px;\r\n    button{\r\n        background-color: $primary !important;\r\n        width: 10px !important;\r\n        height: 10px !important;\r\n        border-radius: 50% !important;\r\n        margin: 5px;\r\n        opacity: 0.5;\r\n    }\r\n}\r\n\r\n.mini-stats-wid{\r\n    .mini-stat-icon{\r\n        overflow: hidden;\r\n        position: relative;\r\n        &:before, &:after{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 8px;\r\n            height: 54px;\r\n            background-color: rgba($white,.1);\r\n            left: 16px;\r\n            transform: rotate(32deg);\r\n            top: -5px;\r\n            transition: all 0.4s;\r\n        }\r\n\r\n        &::after{\r\n            left: -12px;\r\n            width: 12px;\r\n            transition: all 0.2s;\r\n        }\r\n    }\r\n\r\n    &:hover{\r\n        .mini-stat-icon{\r\n            &::after{\r\n                left: 60px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Inbox widget\r\n\r\n.inbox-wid{\r\n    .inbox-list-item{\r\n        a{\r\n            color: $body-color;\r\n            display: block;\r\n            padding: 11px 0px;\r\n            border-bottom: 1px solid $border-color;\r\n        }\r\n\r\n        &:first-child{\r\n            a{\r\n                padding-top: 0px;\r\n            }\r\n        }\r\n\r\n        &:last-child{\r\n            a{\r\n                border-bottom: 0px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// activity widget\r\n\r\n.activity-border{\r\n    &:before{\r\n        content: \"\";\r\n        position: absolute;\r\n        height: 38px;\r\n        border-left: 2px dashed $gray-300;\r\n        top: 40px;\r\n        left: 0px;\r\n    }\r\n}\r\n\r\n.activity-wid{\r\n    margin-left: 16px;\r\n\r\n    .activity-list{\r\n        \r\n        position: relative;\r\n        padding: 0 0 33px 30px;\r\n     \r\n        .activity-icon{\r\n            position: absolute;\r\n            left: -20px;\r\n            top: 0px;\r\n            z-index: 2;\r\n        }\r\n\r\n        &:last-child{\r\n            padding-bottom: 0px;\r\n        }\r\n    }\r\n\r\n \r\n   \r\n}\r\n", "// \r\n// _demos.scss\r\n// \r\n\r\n// Demo Only\r\n.button-items {\r\n    margin-left: -8px;\r\n    margin-bottom: -12px;\r\n    \r\n    .btn {\r\n        margin-bottom: 12px;\r\n        margin-left: 8px;\r\n    }\r\n}\r\n\r\n// Lightbox \r\n\r\n.mfp-popup-form {\r\n    max-width: 1140px;\r\n}\r\n\r\n// Modals\r\n\r\n.bs-example-modal {\r\n    position: relative;\r\n    top: auto;\r\n    right: auto;\r\n    bottom: auto;\r\n    left: auto;\r\n    z-index: 1;\r\n    display: block;\r\n  }\r\n\r\n\r\n\r\n// Icon demo ( Demo only )\r\n.icon-demo-content {\r\n  text-align: center;\r\n  color: $gray-500;\r\n\r\n  i{\r\n    display: block;\r\n    font-size: 24px;\r\n    color: $gray-600;\r\n    width: 48px;\r\n    height: 48px;\r\n    line-height: 46px;\r\n    margin: 0px auto;\r\n    margin-bottom: 16px;\r\n    border-radius: 4px;\r\n    border: 1px solid $border-color;\r\n    transition: all 0.4s;\r\n  }\r\n\r\n  .col-lg-4 {\r\n    margin-top: 24px;\r\n\r\n    &:hover {\r\n      i {\r\n        background-color: $primary;\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: $gray-100;\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n\r\n// card radio\r\n\r\n.card-radio{\r\n  background-color: $card-bg;\r\n  border: 2px solid $card-border-color;\r\n  border-radius: $border-radius;\r\n  padding: 1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n\r\n  &:hover{\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.card-radio-label{\r\n  display: block;\r\n}\r\n\r\n\r\n.card-radio-input{\r\n  display: none;\r\n  &:checked + .card-radio {\r\n    border-color: $primary !important;\r\n  }\r\n}\r\n\r\n.navs-carousel{\r\n  .owl-nav{\r\n      margin-top: 16px;\r\n      button{\r\n          width: 30px;\r\n          height: 30px;\r\n          line-height: 28px !important;\r\n          font-size: 20px !important;\r\n          border-radius: 50% !important;\r\n          background-color: rgba($primary, 0.25) !important;\r\n          color: $primary !important;\r\n          margin: 4px 8px !important;\r\n      }\r\n  }\r\n}", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .vertical-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-header,\r\n    .footer {\r\n        display: none !important;\r\n    }\r\n    .card-body,\r\n    .main-content,\r\n    .right-bar,\r\n    .page-content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .card{\r\n        border: 0;\r\n    }\r\n}", "[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: ' ';\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 6px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: '';\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}", "// \r\n// calendar.scss\r\n//\r\n\r\n.fc-toolbar {\r\n  h2 {\r\n      font-size: 16px;\r\n      line-height: 30px;\r\n      text-transform: uppercase;\r\n  }\r\n}\r\n\r\n.fc {\r\n  th.fc-widget-header {\r\n      background: $light;\r\n      font-size: 13px;\r\n      line-height: 20px;\r\n      padding: 10px 0;\r\n      text-transform: uppercase;\r\n      font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n.fc-unthemed{\r\n  .fc-content, \r\n  .fc-divider, \r\n  .fc-list-heading td, \r\n  .fc-list-view, \r\n  .fc-popover, \r\n  .fc-row, \r\n  tbody, \r\n  td, \r\n  th, \r\n  thead{\r\n      border-color: $light;\r\n  }\r\n  td.fc-today {\r\n      background: lighten($gray-200, 4%);\r\n  }\r\n}\r\n\r\n.fc-button {\r\n  background: $card-bg;\r\n  border-color: $border-color;\r\n  color: $gray-700;\r\n  text-transform: capitalize;\r\n  box-shadow: none;\r\n  padding: 6px 12px !important;\r\n  height: auto !important;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n  background-color: $primary;\r\n  color: $white;\r\n  text-shadow: none;\r\n}\r\n\r\n.fc-event {\r\n  border-radius: 2px;\r\n  border: none;\r\n  cursor: move;\r\n  font-size: 0.8125rem;\r\n  margin: 5px 7px;\r\n  padding: 5px 5px;\r\n  text-align: center;\r\n}\r\n\r\n#external-events .external-event {\r\n  text-align: left!important;\r\n  padding: 8px 16px;\r\n}\r\n\r\n.fc-event, .fc-event-dot{\r\n  background-color: $primary;\r\n}\r\n\r\n.fc-event .fc-content{\r\n  color: $white;\r\n}\r\n\r\n.fc {\r\n  .table-bordered {\r\n    td, th {\r\n      border-color: $table-group-separator-color;\r\n    }\r\n  }\r\n  \r\n  .fc-toolbar {\r\n    @media (max-width: 575.98px) {\r\n      display: block;\r\n    }\r\n    \r\n      h2 {\r\n          font-size: 16px;\r\n          line-height: 30px;\r\n          text-transform: uppercase;\r\n      }\r\n\r\n      @media (max-width: 767.98px) {\r\n\r\n          .fc-left,\r\n          .fc-right,\r\n          .fc-center {\r\n              float: none;\r\n              display: block;\r\n              text-align: center;\r\n              clear: both;\r\n              margin: 10px 0;\r\n          }\r\n\r\n          >*>* {\r\n              float: none;\r\n          }\r\n\r\n          .fc-today-button {\r\n              display: none;\r\n          }\r\n      }\r\n      \r\n      .btn {\r\n          text-transform: capitalize;\r\n      }\r\n\r\n  }\r\n}\r\n.fc-bootstrap .fc-today.alert-info{\r\n  background-color: $gray-300;\r\n}\r\n\r\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\r\n  background-color: $black !important;\r\n}\r\n\r\n// RTL\r\n[dir=\"rtl\"] .fc-header-toolbar {\r\n  direction: ltr !important;\r\n}\r\n\r\n[dir=\"rtl\"] .fc-toolbar>*>:not(:first-child) {\r\n  margin-left: .75em;\r\n}\r\n\r\n\r\n\r\n", "\r\n//\r\n// colorpicker.scss\r\n//\r\n\r\n.sp-container{\r\n  background-color: $dropdown-bg;\r\n  z-index: 1000;\r\n  button{\r\n    padding: .25rem .5rem;\r\n      font-size: .71094rem;\r\n      border-radius: .2rem;\r\n      font-weight: 400;\r\n      color: $dark;\r\n  \r\n      &.sp-palette-toggle{\r\n        background-color: $light;\r\n      }\r\n      \r\n      &.sp-choose{\r\n        background-color: $success;\r\n        margin-left: 5px;\r\n        margin-right: 0;\r\n      }\r\n  }\r\n}\r\n\r\n.sp-palette-container{\r\n  border-right: 1px solid $border-color;\r\n}\r\n\r\n.sp-input{\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color !important;\r\n  color: $input-color;\r\n  &:focus{\r\n    outline: none;\r\n  }\r\n}\r\n\r\n\r\n[dir=\"rtl\"]{\r\n\r\n  .sp-alpha{\r\n    direction: rtl;\r\n  }\r\n\r\n  .sp-original-input-container {\r\n    .sp-add-on{\r\n      border-top-right-radius: 0!important;\r\n      border-bottom-right-radius: 0!important;\r\n      border-top-left-radius: 4px!important;\r\n      border-bottom-left-radius: 4px!important\r\n    }\r\n  } \r\n\r\n  input.spectrum.with-add-on{\r\n    border: 1px solid $input-border-color;\r\n    border-left: 0;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n\r\n  }\r\n}", "//\r\n// session-timeout.scss\r\n//\r\n\r\n#session-timeout-dialog {\r\n    .close {\r\n        display: none;\r\n    }\r\n\r\n    .countdown-holder {\r\n        color: $danger;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $danger;\r\n        box-shadow: none;\r\n    }\r\n}", "//\r\n// Range slider\r\n//\r\n\r\n.irs {\r\n    font-family: $font-family-base;\r\n}\r\n\r\n.irs--round {\r\n\r\n    .irs-bar,\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        background: $primary !important;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-to,\r\n    .irs-from,\r\n    .irs-single {\r\n        &:before {\r\n            display: none;\r\n        }\r\n    }\r\n\r\n    .irs-line {\r\n        background: $gray-300;\r\n        border-color: $gray-300;\r\n    }\r\n\r\n    .irs-grid-text {\r\n        font-size: 11px;\r\n        color: $gray-500;\r\n    }\r\n\r\n    .irs-min,\r\n    .irs-max {\r\n        color: $gray-500;\r\n        background: $gray-300;\r\n        font-size: 11px;\r\n    }\r\n\r\n    .irs-handle {\r\n        border: 2px solid $primary;\r\n        width: 10px;\r\n        height: 16px;\r\n        top: 29px;\r\n        background-color: $card-bg !important;\r\n    }\r\n}", "\r\n//\r\n//  Sweetalert2\r\n//\r\n\r\n.swal2-container {\r\n  .swal2-title{\r\n    font-size: 24px;\r\n    font-weight: $font-weight-medium;\r\n  }  \r\n}\r\n\r\n.swal2-content{\r\n  font-size: 16px;\r\n}\r\n\r\n.swal2-icon{\r\n  &.swal2-question{\r\n    border-color: $info;\r\n    color: $info;\r\n  }\r\n  &.swal2-success {\r\n    [class^=swal2-success-line]{\r\n      background-color: $success;\r\n    }\r\n\r\n    .swal2-success-ring{\r\n      border-color: rgba($success, 0.3);\r\n    }\r\n  }\r\n  &.swal2-warning{\r\n    border-color: $warning;\r\n    color: $warning;\r\n  }\r\n}\r\n\r\n.swal2-styled{\r\n  &:focus{\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n.swal2-progress-steps {\r\n  .swal2-progress-step{\r\n    background: $primary;\r\n    &.swal2-active-progress-step{\r\n      background: $primary;\r\n      &~.swal2-progress-step, &~.swal2-progress-step-line{\r\n        background: rgba($primary, 0.3);\r\n      }\r\n    }\r\n  }\r\n\r\n  .swal2-progress-step-line{\r\n    background: $primary;\r\n  }\r\n}\r\n\r\n.swal2-loader{\r\n  border-color: $primary transparent $primary transparent;\r\n}\r\n", "\r\n//\r\n// Rating\r\n//\r\n\r\n.symbol{\r\n  border-color: $card-bg;\r\n}\r\n\r\n.rating-symbol-background, .rating-symbol-foreground {\r\n  font-size: 24px;\r\n}\r\n\r\n.rating-symbol-foreground {\r\n  top: 0px;\r\n}\r\n\r\n.rating-star{\r\n  > span{\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    &.badge{\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Parsley\r\n//\r\n\r\n.error {\r\n  color: $danger;\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-errors-list {\r\n  display: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  &.filled {\r\n    display: block;\r\n  }\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "\r\n//\r\n// Select 2\r\n//\r\n\r\n.select2-container {\r\n  display: block;\r\n  .select2-selection--single {\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color;\r\n    height: 38px;\r\n    &:focus{\r\n      outline: none;\r\n    }\r\n\r\n    .select2-selection__rendered {\r\n      line-height: 36px;\r\n      padding-left: 12px;\r\n      color: $input-color;\r\n    }\r\n\r\n    .select2-selection__arrow {\r\n      height: 34px;\r\n      width: 34px;\r\n      right: 3px;\r\n\r\n      b{\r\n        border-color: $gray-500 transparent transparent transparent;\r\n        border-width: 6px 6px 0 6px;\r\n      }\r\n    }\r\n\r\n    .select2-selection__placeholder{\r\n      color: $body-color;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--open {\r\n  .select2-selection--single {\r\n\r\n    .select2-selection__arrow {\r\n\r\n      b{\r\n        border-color: transparent transparent $gray-500 transparent !important;\r\n        border-width: 0 6px 6px 6px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default {\r\n  .select2-search--dropdown {\r\n      padding: 10px;\r\n      background-color: $dropdown-bg;\r\n      .select2-search__field {\r\n          border: 1px solid  $input-border-color;\r\n          background-color: $input-bg;\r\n          color: $gray-600;\r\n          outline: none;\r\n      }\r\n  }\r\n  .select2-results__option--highlighted[aria-selected] {\r\n      background-color: $primary;\r\n  }\r\n  .select2-results__option[aria-selected=true] {\r\n      background-color: $dropdown-link-active-bg;\r\n      color: $dropdown-link-active-color;\r\n      &:hover {\r\n          background-color: $primary;\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\n.select2-results__option {\r\n  padding: 6px 12px;\r\n}\r\n\r\n.select2-dropdown {\r\n  border: 1px solid $dropdown-border-color;\r\n  background-color: $dropdown-bg;\r\n  box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-search {\r\n  input{\r\n    border: 1px solid $gray-300;\r\n  }\r\n}\r\n\r\n.select2-container {\r\n  .select2-selection--multiple {\r\n    min-height: 38px;\r\n    background-color: $input-bg;\r\n    border: 1px solid $input-border-color !important;\r\n  \r\n    .select2-selection__rendered {\r\n      padding: 2px 10px;\r\n    }\r\n    .select2-search__field {\r\n      border: 0;\r\n      color: $input-color;\r\n      &::placeholder{\r\n          color: $input-color;\r\n      }\r\n  }\r\n    .select2-selection__choice {\r\n      background-color: $gray-200;\r\n      border: 1px solid $gray-300;\r\n      border-radius: 1px;\r\n      padding: 0 7px;\r\n    }\r\n  }\r\n}\r\n\r\n.select2-container--default{\r\n  &.select2-container--focus {\r\n    .select2-selection--multiple{\r\n      border-color: $gray-400;\r\n    }\r\n  }\r\n\r\n  .select2-results__group{\r\n    font-weight: $font-weight-semibold;\r\n  }\r\n}\r\n\r\n// ajax select\r\n\r\n.select2-result-repository__avatar{\r\n    float: left;\r\n    width: 60px;\r\n    margin-right: 10px;\r\n  img{\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 2px;\r\n  }\r\n}\r\n\r\n.select2-result-repository__statistics{\r\n  margin-top: 7px;\r\n}\r\n\r\n.select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  display: inline-block;\r\n  font-size: 11px;\r\n  margin-right: 1em;\r\n  color: $gray-500;\r\n\r\n  .fa{\r\n    margin-right: 4px;\r\n\r\n    &.fa-flash{\r\n      &::before{\r\n        content: \"\\f0e7\";\r\n        font-family: 'Font Awesome 5 Free';\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.select2-results__option--highlighted{\r\n  .select2-result-repository__forks, \r\n.select2-result-repository__stargazers, \r\n.select2-result-repository__watchers{\r\n  color: rgba($white, 0.8);\r\n}\r\n}\r\n\r\n.select2-result-repository__meta{\r\n  overflow: hidden;\r\n}\r\n\r\n\r\n// templating-select\r\n\r\n.img-flag{\r\n  margin-right: 7px;\r\n  height: 15px;\r\n  width: 18px;\r\n}\r\n\r\n", "//\r\n//  Sweetalert2\r\n//\r\n\r\n/* CSS Switch */\r\ninput[switch] {\r\n  display: none;\r\n  + label {\r\n    font-size: 1em;\r\n    line-height: 1;\r\n    width: 56px;\r\n    height: 24px;\r\n    background-color: $gray-400;\r\n    background-image: none;\r\n    border-radius: 2rem;\r\n    padding: 0.16667rem;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    text-align: center;\r\n    position: relative;\r\n    font-weight: $font-weight-medium;\r\n    transition: all 0.1s ease-in-out;\r\n    &:before {\r\n      color: $dark;\r\n      content: attr(data-off-label);\r\n      display: block;\r\n      font-family: inherit;\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n      line-height: 21px;\r\n      position: absolute;\r\n      right: 1px;\r\n      margin: 3px;\r\n      top: -2px;\r\n      text-align: center;\r\n      min-width: 1.66667rem;\r\n      overflow: hidden;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 3px;\r\n      background-color: $gray-200;\r\n      box-shadow: none;\r\n      border-radius: 2rem;\r\n      height: 20px;\r\n      width: 20px;\r\n      top: 2px;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n  }\r\n\r\n  &:checked + label {\r\n    background-color: $primary;\r\n  }\r\n}\r\n\r\ninput[switch]:checked + label {\r\n  background-color: $primary;\r\n  &:before {\r\n    color: $white;\r\n    content: attr(data-on-label);\r\n    right: auto;\r\n    left: 3px;\r\n  }\r\n\r\n  &:after {\r\n    left: 33px;\r\n    background-color: $gray-200;\r\n  }\r\n}\r\n\r\ninput[switch=\"bool\"] + label {\r\n  background-color: $danger;\r\n}\r\ninput[switch=\"bool\"] + label:before,input[switch=\"bool\"]:checked + label:before,\r\ninput[switch=\"default\"]:checked + label:before{\r\n  color: $white;\r\n}\r\n\r\ninput[switch=\"bool\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"default\"]:checked + label {\r\n  background-color: #a2a2a2;\r\n}\r\n\r\ninput[switch=\"primary\"]:checked + label {\r\n  background-color: $primary;\r\n}\r\n\r\ninput[switch=\"success\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"info\"]:checked + label {\r\n  background-color: $info;\r\n}\r\n\r\ninput[switch=\"warning\"]:checked + label {\r\n  background-color: $warning;\r\n}\r\n\r\ninput[switch=\"danger\"]:checked + label {\r\n  background-color: $danger;\r\n}\r\n\r\ninput[switch=\"dark\"]:checked + label {\r\n  background-color: $dark;\r\n}\r\n\r\n.square-switch{\r\n  margin-right: 7px;\r\n  input[switch]+label, input[switch]+label:after{\r\n    border-radius: 4px;\r\n  }\r\n}", "\r\n//\r\n// Datepicker\r\n//\r\n\r\n.datepicker {\r\n  border: 1px solid $gray-100;\r\n  padding: 8px;\r\n  z-index: 999 !important;\r\n  table{\r\n    tr{\r\n      th{\r\n        font-weight: 500;\r\n      }\r\n      td{\r\n        &.active, &.active:hover, .active.disabled, &.active.disabled:hover,\r\n        &.today,  &.today:hover, &.today.disabled, &.today.disabled:hover, \r\n        &.selected, &.selected:hover, &.selected.disabled, &.selected.disabled:hover{\r\n          background-color: $primary !important;\r\n          background-image: none;\r\n          box-shadow: none;\r\n          color: $white !important;\r\n        }\r\n\r\n        &.day.focused,\r\n        &.day:hover,\r\n        span.focused,\r\n        span:hover {\r\n            background: $gray-200;\r\n        }\r\n\r\n        &.new,\r\n        &.old,\r\n        span.new,\r\n        span.old {\r\n            color: $gray-500;\r\n            opacity: 0.6;\r\n        }\r\n\r\n        &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n            background-color: $gray-300;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-condensed{\r\n  >thead>tr>th, >tbody>tr>td {\r\n    padding: 7px;\r\n  }\r\n}", "\r\n//\r\n// Bootstrap touchspin\r\n//\r\n\r\n\r\n.bootstrap-touchspin{\r\n    &.input-group{\r\n      &>.input-group-prepend{\r\n        &>.btn, &>.input-group-text{\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  \r\n    &.input-group{\r\n      &>.input-group-append{\r\n        &>.btn, &>.input-group-text{\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .bootstrap-touchspin{\r\n    .input-group-btn-vertical{\r\n      right: 0 !important;\r\n    left: 100% !important;\r\n    }\r\n    .bootstrap-touchspin-up{\r\n      border-top-right-radius: 4px !important;\r\n      border-bottom-right-radius: 0 !important;\r\n      border-top-left-radius: 0 !important;\r\n      border-bottom-left-radius: 0 !important;\r\n    }\r\n    .bootstrap-touchspin-down{\r\n      border-top-right-radius: 0 !important;\r\n    border-bottom-right-radius: 4px !important;\r\n    border-top-left-radius: 0 !important;\r\n    border-bottom-left-radius: 0 !important;\r\n    }\r\n  }", "//\r\n// datatable.scss\r\n\r\n.table-bordered {\r\n  border: $table-border-width solid $table-border-color;\r\n}\r\n\r\n\r\ndiv.dataTables_wrapper {\r\n  div.dataTables_filter{\r\n    text-align: right;\r\n    input{\r\n      margin-left: 0.5em;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}", "//\r\n// Form editors.scss\r\n//\r\n\r\n// Tinymce \r\n\r\n.tox-tinymce {\r\n    border: 2px solid $gray-300 !important;\r\n}\r\n\r\n.tox {\r\n    .tox-statusbar {\r\n        border-top: 1px solid $gray-300 !important;\r\n    }\r\n\r\n    .tox-menubar,\r\n    .tox-edit-area__iframe,\r\n    .tox-statusbar {\r\n        background-color: $card-bg !important;\r\n        background: none !important;\r\n    }\r\n\r\n    .tox-mbtn {\r\n        color: $gray-700 !important;\r\n\r\n        &:hover:not(:disabled):not(.tox-mbtn--active) {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-tbtn {\r\n        &:hover {\r\n            background-color: $gray-300 !important;\r\n        }\r\n    }\r\n\r\n    .tox-toolbar__primary {\r\n        border-color: $gray-300 !important;\r\n    }\r\n\r\n    .tox-toolbar,\r\n    .tox-toolbar__overflow,\r\n    .tox-toolbar__primary {\r\n        background: $gray-300 !important;\r\n    }\r\n\r\n    .tox-tbtn {\r\n        color: $gray-700 !important;\r\n\r\n        svg {\r\n            fill: $gray-700 !important;\r\n        }\r\n    }\r\n\r\n    .tox-edit-area__iframe {\r\n        background-color: $card-bg !important;\r\n    }\r\n\r\n    .tox-statusbar a,\r\n    .tox-statusbar__path-item,\r\n    .tox-statusbar__wordcount {\r\n        color: $gray-700 !important;\r\n    }\r\n\r\n    &:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {\r\n        border-right: 1px solid darken($gray-300, 5%) !important;\r\n    }\r\n}\r\n.tox-tinymce-aux {\r\n    z-index: 1000 !important;\r\n}\r\n", "\r\n//\r\n// Form-Upload\r\n//\r\n\r\n/* Dropzone */\r\n.dropzone {\r\n  min-height: 230px;\r\n  border: 2px dashed $gray-400;\r\n  background: $card-bg;\r\n  border-radius: 6px;\r\n\r\n  .dz-message {\r\n    font-size: 24px;\r\n    width: 100%;\r\n  }\r\n}", "//\r\n// Form Wizard\r\n//\r\n// twitter-bs-wizard\r\n\r\n.twitter-bs-wizard {\r\n\r\n    .twitter-bs-wizard-nav {\r\n        position: relative;\r\n\r\n      \r\n\r\n       .wizard-border{\r\n        &:before {\r\n            content: \"\";\r\n            width: 189px;\r\n            height: 2px;\r\n            background: rgba($primary, 0.2);\r\n            position: absolute;\r\n            top: 26px;\r\n            margin-left: 100px;\r\n\r\n           \r\n        }\r\n       }\r\n\r\n        .step-number {\r\n            display: inline-block;\r\n            border-radius: 30px;\r\n            padding: 4px 0px;\r\n            width: 200px;\r\n            line-height: 34px;\r\n            color: $primary;\r\n            text-align: center;\r\n            position: relative;\r\n            background-color: rgba($primary, 0.2);\r\n\r\n            @media (max-width: 991.98px) {\r\n                display: block;\r\n                margin: 0 auto 8px !important;\r\n                width: 170px;\r\n            }\r\n        }\r\n\r\n        .nav-link {\r\n            .step-title {\r\n                display: block;\r\n                margin-top: 8px;\r\n                font-weight: $font-weight-bold;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            &.active {\r\n                background-color: transparent;\r\n                color: $gray-700;\r\n\r\n                .step-number {\r\n                    background-color: $primary;\r\n                    color: $white;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .twitter-bs-wizard-pager-link {\r\n        padding-top: 24px;\r\n        padding-left: 0;\r\n        list-style: none;\r\n        margin-bottom: 0;\r\n\r\n        li {\r\n            display: inline-block;\r\n\r\n            a {\r\n                display: inline-block;\r\n                padding: .47rem .75rem;\r\n                background-color: $primary;\r\n                color: $white;\r\n                border-radius: .25rem;\r\n            }\r\n\r\n            &.disabled {\r\n                a {\r\n                    cursor: not-allowed;\r\n                    background-color: lighten($primary, 8%);\r\n                }\r\n            }\r\n\r\n            &.next {\r\n                float: right;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard-tab-content {\r\n    padding-top: 24px;\r\n    min-height: 262px;\r\n}\r\n\r\n\r\n@media (max-width:  1024px) {\r\n    .twitter-bs-wizard {\r\n        .twitter-bs-wizard-nav{\r\n               .wizard-border{\r\n                &:before {\r\n                    background: transparent !important; \r\n                }\r\n              }\r\n        }\r\n\r\n    }\r\n}", "\r\n//\r\n// Responsive Table\r\n//\r\n\r\n.table-rep-plugin {\r\n  .btn-toolbar {\r\n    display: block;\r\n  }\r\n  .table-responsive {\r\n    border: none !important;\r\n  }\r\n  .btn-group{\r\n    .btn-default {\r\n      background-color: $secondary;\r\n      color: $light;\r\n      border: 1px solid $secondary;\r\n      &.btn-primary {\r\n          background-color: $primary;\r\n          border-color: $primary;\r\n          color: $white;\r\n          box-shadow: 0 0 0 2px rgba($primary, .5);\r\n      }\r\n  }\r\n    &.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        right: 0;\r\n        transform: none !important;\r\n        top: 100% !important;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    th {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n\r\n  .checkbox-row {\r\n    padding-left: 40px;\r\n    color: $dropdown-color !important;\r\n\r\n    &:hover{\r\n      background-color: lighten($gray-200, 2%) !important;\r\n    }\r\n\r\n    label {\r\n      display: inline-block;\r\n      padding-left: 5px;\r\n      position: relative;\r\n      &::before {\r\n        -o-transition: 0.3s ease-in-out;\r\n        -webkit-transition: 0.3s ease-in-out;\r\n        background-color: $white;\r\n        border-radius: 3px;\r\n        border: 1px solid $gray-300;\r\n        content: \"\";\r\n        display: inline-block;\r\n        height: 17px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        position: absolute;\r\n        transition: 0.3s ease-in-out;\r\n        width: 17px;\r\n        outline: none !important;\r\n      }\r\n      &::after {\r\n        color: $gray-200;\r\n        display: inline-block;\r\n        font-size: 11px;\r\n        height: 16px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        padding-left: 3px;\r\n        padding-top: 1px;\r\n        position: absolute;\r\n        top: -1px;\r\n        width: 16px;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      z-index: 1;\r\n      outline: none !important;\r\n\r\n      &:disabled + label {\r\n        opacity: 0.65;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:focus + label {\r\n      &::before {\r\n        outline-offset: -2px;\r\n        outline: none;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::after {\r\n        content: \"\\f00c\";\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 900;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:disabled + label {\r\n      &::before {\r\n        background-color: $gray-100;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::before {\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n      &::after {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .fixed-solution {\r\n    .sticky-table-header{\r\n      top: $header-height !important;\r\n      background-color: $primary;\r\n      table{\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  table.focus-on tbody tr.focused th,\r\n  table.focus-on tbody tr.focused td,\r\n  .sticky-table-header {\r\n      background: $primary;\r\n      border-color: $primary;\r\n      color: $white;\r\n\r\n      table {\r\n          color: $white;\r\n      }\r\n  }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n  @media (min-width: 992px) {\r\n    .fixed-solution {\r\n      .sticky-table-header{\r\n        top: $header-height + 78px !important;;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-striped{\r\n  >tbody{\r\n    >tr{\r\n      &:nth-of-type(odd).focused{\r\n        box-shadow: none !important;\r\n        td, th {\r\n          box-shadow: none !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Table editable\r\n//\r\n\r\n.table-edits{\r\n  input, select{\r\n    height: $input-height-sm;\r\n    padding: $input-padding-y-sm $input-padding-x-sm;\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    color: $input-color;\r\n    border-radius: $input-border-radius;\r\n    &:focus{\r\n      outline: none;\r\n      border-color: $input-focus-border-color;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// apexcharts.scss\r\n//\r\n.apex-charts {\r\n    min-height: 10px !important;\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n    .apexcharts-canvas {\r\n        margin: 0 auto;\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip-title,\r\n.apexcharts-tooltip-text {\r\n    font-family: $font-family-base !important;\r\n}\r\n\r\n.apexcharts-legend-series {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n.apexcharts-gridline {\r\n    pointer-events: none;\r\n    stroke: $apex-grid-color;\r\n}\r\n\r\n.apexcharts-legend-text {\r\n    color: $gray-600 !important;\r\n    font-family: $font-family-base !important;\r\n    font-size: 13px !important;\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: $white !important;\r\n}\r\n\r\n.apexcharts-yaxis,\r\n.apexcharts-xaxis {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: $gray-500;\r\n    }\r\n}", "//\r\n// chartist.scss\r\n//\r\n\r\n.ct-golden-section:before {\r\n    float: none;\r\n}\r\n\r\n.ct-chart {\r\n    max-height: 300px;\r\n   \r\n    .ct-label {\r\n        fill: $gray-500;\r\n        color: $gray-500;\r\n        font-size: 12px;\r\n        line-height: 1;\r\n    }\r\n}\r\n\r\n.ct-chart.simple-pie-chart-chartist {\r\n    .ct-label {\r\n        color: $white;\r\n        fill: $white;\r\n        font-size: 16px;\r\n    }\r\n}\r\n\r\n.ct-grid {\r\n    stroke: rgba($dark, 0.1);\r\n}\r\n\r\n.ct-chart {\r\n    .ct-series {\r\n        &.ct-series-a {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $primary;\r\n            }\r\n        }\r\n        &.ct-series-b {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $success;\r\n            }\r\n        }\r\n        &.ct-series-c {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $danger;\r\n            }\r\n        }\r\n        &.ct-series-d {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $info;\r\n            }\r\n        }\r\n        &.ct-series-e {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $success;\r\n            }\r\n        }\r\n        &.ct-series-f {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $dark;\r\n            }\r\n        }\r\n        &.ct-series-g {\r\n            .ct-bar,\r\n            .ct-line,\r\n            .ct-point,\r\n            .ct-slice-donut {\r\n                stroke: $purple;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.ct-series-a {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $primary;\r\n    }\r\n}\r\n\r\n.ct-series-b {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $success;\r\n    }\r\n}\r\n\r\n.ct-series-c {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $warning;\r\n    }\r\n}\r\n\r\n.ct-series-d {\r\n    .ct-area,\r\n    .ct-slice-pie {\r\n        fill: $success;\r\n    }\r\n}\r\n\r\n.ct-area {\r\n    fill-opacity: .33;\r\n}\r\n\r\n.chartist-tooltip {\r\n    position: absolute;\r\n    display: inline-block;\r\n    opacity: 0;\r\n    min-width: 10px;\r\n    padding: 2px 10px;\r\n    border-radius: 3px;\r\n    background: $dark;\r\n    color: $gray-300;\r\n    text-align: center;\r\n    pointer-events: none;\r\n    z-index: 1;\r\n    transition: opacity .2s linear;\r\n    &.tooltip-show {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.ct-line {\r\n    stroke-width: 3px;\r\n}\r\n\r\n.ct-point {\r\n    stroke-width: 7px;\r\n}", "\r\n\r\n/* Flot chart */\r\n\r\n.flotTip {\r\n  padding: 8px 12px !important;\r\n  background-color: $gray-800 !important;\r\n  border: $border-width solid $gray-800 !important;\r\n  box-shadow: $box-shadow;\r\n  z-index: 100;\r\n  color: $gray-200;\r\n  opacity: 1;\r\n  border-radius: 3px !important;\r\n  font-size: 14px !important;\r\n}\r\n\r\n.legend {\r\n  div {\r\n      background-color: transparent !important;\r\n  }\r\n  tr {\r\n      height: 30px;\r\n  }\r\n}\r\n\r\n.legendLabel {\r\n  padding-left: 5px;\r\n  line-height: 10px;\r\n  padding-right: 10px;\r\n  font-size: 13px;\r\n  font-weight: $font-weight-medium;\r\n  color: $gray-500;\r\n}\r\n\r\n.legendColorBox {\r\n  div {\r\n      border-radius: 3px;\r\n      div {\r\n          border-radius: 3px;\r\n      }\r\n  }\r\n}\r\n\r\n.float-lable-box {\r\n  table {\r\n      margin: 0 auto;\r\n  }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(sm) {\r\n  .legendLabel {\r\n      display: none;\r\n  }\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "\r\n//\r\n// Google map\r\n//\r\n\r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n  \r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $gray-800;\r\n    color: $gray-100;\r\n    font-family: $font-family-base;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// x editable.scss\r\n//\r\n\r\n.editable-input{\r\n    .form-control{\r\n      display: inline-block;\r\n    }\r\n  }\r\n  \r\n  .editable-buttons{\r\n    margin-left: 7px;\r\n    .editable-cancel{\r\n      margin-left: 7px;\r\n    }\r\n  }", "// \r\n// authentication.scss\r\n//\r\n\r\n.home-btn {\r\n    position: absolute;\r\n    top: 15px;\r\n    right: 25px;\r\n}\r\n\r\n\r\n\r\n.home-center {\r\n    display: table;\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  \r\n  .home-desc-center {\r\n    display: table-cell;\r\n    vertical-align: middle;\r\n  }\r\n\r\n.authentication-bg {\r\n    background-image: url(../images/title-img.png);\r\n    height: 100vh;\r\n    background-size: cover;\r\n    background-position: center;\r\n}\r\n\r\n.authentication-bg .bg-overlay {\r\n    background-color: $primary;\r\n}\r\n\r\n\r\n// Erorr\r\n\r\n\r\n.error-page {\r\n    text-transform: uppercase;\r\n    background: repeating-linear-gradient(45deg, $primary, $primary 10px, $success 10px, $success 20px);\r\n    -webkit-background-clip: text;\r\n    -webkit-text-fill-color: transparent;\r\n    font-size: 120px;\r\n    line-height: .8;\r\n    position: relative;\r\n}\r\n\r\n\r\n// FAQS\r\n\r\n\r\n.faq-icon{\r\n    i {\r\n        width: 30px;\r\n        height: 30px;\r\n        line-height: 28px;\r\n        border: 1px solid;\r\n        border-radius: 50%;\r\n        text-align: center;\r\n        float: right;\r\n        font-size: 16px;\r\n        display: inline-block;\r\n    }\r\n    &:after{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 30px;\r\n            height: 30px;\r\n            opacity: 0.2;\r\n            right: 50px;\r\n            margin-top: -10px;\r\n            border-radius: 50%;\r\n            background: $primary;\r\n        }\r\n}\r\n\r\n", "// \r\n// ecommerce.scss\r\n//\r\n\r\n// product\r\n\r\n.search-box{\r\n    .form-control{\r\n        border-radius: 30px;\r\n        padding-left: 40px;\r\n        border: 1px solid $gray-300;\r\n    }\r\n    .search-icon{\r\n        font-size: 16px;    \r\n        position: absolute;\r\n        left: 13px;\r\n        top: 0;\r\n        line-height: 38px;\r\n    }\r\n}\r\n\r\n\r\n.categories-group-list{\r\n    display: block;\r\n    color: $dark;\r\n    font-weight: $font-weight-medium;\r\n    padding: 8px 16px;\r\n\r\n    &[aria-expanded=\"true\"]{\r\n        background-color: $gray-300;\r\n    }\r\n\r\n    &:last-child{\r\n        border: 0;\r\n    }\r\n\r\n    &:hover{\r\n        color: $dark;\r\n    }\r\n}\r\n\r\n.categories-list{\r\n    padding: 8px 0px;\r\n    li{\r\n        a{\r\n            display: block;\r\n            padding: 4px 16px;\r\n            color: $body-color;\r\n        }\r\n\r\n        &.active{\r\n            a{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n\r\n// Product Details\r\n\r\n.product-detai-imgs{\r\n    .nav{\r\n        .nav-link{\r\n            margin: 7px 0px;\r\n\r\n            &.active{\r\n                background-color: $gray-300;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-color{\r\n    a{\r\n        display: inline-block;\r\n        text-align: center;\r\n        color: $body-color;\r\n\r\n        .product-color-item{\r\n            margin: 7px;\r\n            border: 2px solid $border-color;\r\n            border-radius: 4px;\r\n        }\r\n        &.active, &:hover{\r\n            color: $primary;\r\n            .product-color-item{\r\n                border-color: $primary !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-track{\r\n    border: 1px solid $border-color;\r\n}\r\n\r\n.ecommerce-sortby-list{\r\n    li{\r\n        color: $dark;\r\n        a{\r\n            color: $body-color;\r\n            padding: 4px;\r\n        }\r\n\r\n        &.active{\r\n            a{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-img{\r\n    position: relative;\r\n    \r\n    .product-ribbon{\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0px;\r\n        padding: 6px 8px;\r\n        border-radius: 50% 50% 25% 75%/44% 68% 32% 56%;\r\n        width: 62px;\r\n        height: 60px;\r\n        color: $white;\r\n        font-size: 15px;\r\n        text-align: center;\r\n    }\r\n\r\n    .product-like{\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        a{\r\n            display: inline-block;\r\n            width: 40px;\r\n            height: 40px;\r\n            border: 2px solid $gray-300;\r\n            line-height: 38px;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            color: $gray-500;\r\n        }\r\n    }\r\n}\r\n\r\n.product-detail{\r\n    .nav-pills{\r\n        .nav-link{\r\n            margin-bottom: 7px;\r\n            &.active{\r\n                background-color: $gray-300;\r\n            }\r\n\r\n\r\n            .tab-img{\r\n                width: 5rem;\r\n            }\r\n        }\r\n    }\r\n\r\n    .product-img{\r\n        border: 1px solid $border-color;\r\n        padding: 24px;\r\n    }\r\n}\r\n\r\n.product-desc-list{\r\n    li{\r\n        padding: 4px 0px;\r\n    }\r\n}\r\n\r\n.product-review-link{\r\n    .list-inline-item{\r\n        a{\r\n            color: $gray-600;\r\n        }\r\n        &:not(:last-child){\r\n            margin-right: 14px;\r\n        }\r\n    }           \r\n}\r\n\r\n// ecommerce cart\r\n\r\n.product-cart-touchspin{\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    border-radius: $border-radius;\r\n    .form-control{\r\n        border-color: transparent;\r\n        height: 32px\r\n    }\r\n    \r\n    .input-group-btn .btn{\r\n        background-color: transparent !important;\r\n        border-color: transparent !important;\r\n        color: $primary !important;\r\n        font-size: 16px;\r\n        padding: 3px 12px;\r\n        box-shadow: none;\r\n    }\r\n\r\n}\r\n\r\n// ecommerce checkout\r\n\r\n.shipping-address{\r\n    box-shadow: none;\r\n    &.active{\r\n        border-color: $primary !important;\r\n    }\r\n}\r\n\r\n.twitter-bs-wizard {\r\n.chackout-border{\r\n    &:before {\r\n        content: \"\";\r\n        width: 139px;\r\n        height: 2px;\r\n        background: rgba($primary, 0.2);\r\n        position: absolute;\r\n        top: 26px;\r\n        margin-left: 100px;\r\n    }\r\n   }\r\n\r\n   .add-product-border{\r\n    &:before {\r\n        content: \"\";\r\n        width: 324px;\r\n        height: 2px;\r\n        background: rgba($primary, 0.2);\r\n        position: absolute;\r\n        top: 26px;\r\n        margin-left: 100px;\r\n    }\r\n   }\r\n}\r\n\r\n@media (max-width:  1024px) {\r\n    .twitter-bs-wizard {\r\n\r\n           .chackout-border, .add-product-border{\r\n            width: 180px;\r\n            &:before {\r\n                background: transparent !important; \r\n            }\r\n          }\r\n    }\r\n}", "/* ==============\r\n  Email\r\n===================*/\r\n.email-leftbar {\r\n  width: 236px;\r\n  float: left;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.email-rightbar {\r\n  margin-left: 260px;\r\n}\r\n\r\n.chat-user-box {\r\n  p.user-title {\r\n    color: $dark;\r\n    font-weight: 600;\r\n  }\r\n  p {\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .email-leftbar {\r\n    float: none;\r\n    width: 100%;\r\n  }\r\n  .email-rightbar {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.mail-list {\r\n  a {\r\n    display: block;\r\n    color: $gray-600;\r\n    line-height: 24px;\r\n    padding: 8px 5px;\r\n    &.active {\r\n      color: $danger;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: $gray-600;\r\n    }\r\n\r\n    &:hover {\r\n      background: $gray-300;\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n    &.active,\r\n    &.active:hover {\r\n      box-shadow: inset 3px 0 0 $primary;\r\n    }\r\n\r\n    \r\n  &.unread  {\r\n    background-color: $gray-300;\r\n    font-weight: 500;\r\n    color: darken($dark,5%);\r\n      a{\r\n        color: darken($dark,5%);\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    box-shadow: inset 0 0 0 1px $gray-400;\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      height: 20px;\r\n      width: 20px;\r\n      left: 0;\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      margin-bottom: 0;\r\n      transition-duration: .05s;\r\n      top: 0;\r\n      &:before {\r\n        content: \"\\F012C\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 0;\r\n        height: 20px;\r\n        color: darken($dark,5%);\r\n        width: 20px;\r\n        position: absolute;\r\n        margin-top: -16px;\r\n        left: 4px;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 575.98px) { \r\n  .message-list li .col-mail-1 {\r\n      width: 200px;\r\n  }\r\n}", "// \r\n// Chat.scss\r\n//\r\n\r\n.chat-leftsidebar{\r\n    @media (min-width: 992px) {\r\n      min-width: 380px;\r\n    }\r\n  \r\n  \r\n    .chat-leftsidebar-nav{\r\n      .nav{\r\n        background-color: $card-bg;\r\n      }\r\n  \r\n     \r\n    }\r\n  }\r\n  \r\n  .chat-noti-dropdown{\r\n    &.active{\r\n      &:before{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 8px;\r\n        height: 8px;\r\n        background-color: $danger;\r\n        border-radius: 50%;\r\n        right: 0;\r\n      }\r\n    }\r\n  \r\n    .btn{\r\n      padding: 6px;\r\n      box-shadow: none;\r\n      font-size: 20px;\r\n    }\r\n  }\r\n  \r\n  .chat-list{\r\n    margin: 0;\r\n    li{\r\n      &.active{\r\n        a{\r\n          background-color: $card-bg;\r\n          box-shadow: $box-shadow;\r\n        }\r\n      }\r\n      a{\r\n        display: block;\r\n        padding: 14px 16px;\r\n        color: $gray-600;\r\n        transition: all 0.4s;\r\n        border: 1px solid $border-color;\r\n        border-radius: 4px;\r\n        margin-top: 10px;\r\n        &:hover{\r\n          background-color: $card-bg;\r\n          box-shadow: $box-shadow;\r\n  \r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  \r\n  .user-chat-nav{\r\n    .dropdown{\r\n      .nav-btn{\r\n        height: 40px;\r\n        width: 40px;\r\n        line-height: 34px;\r\n        box-shadow: none;\r\n        padding: 0;\r\n        font-size: 16px;\r\n        background-color: $light;\r\n        border-radius: 50%;\r\n      }\r\n  \r\n      .dropdown-menu{\r\n        box-shadow: $box-shadow;\r\n        border: 1px solid $border-color\r\n      }\r\n    }\r\n  }\r\n  \r\n  \r\n  .chat-conversation{\r\n    li{\r\n      clear: both;\r\n    }\r\n  \r\n    .chat-day-title{\r\n      position: relative;\r\n      text-align: center;\r\n      margin-bottom: 24px;\r\n  \r\n      .title{\r\n        background-color: $card-bg;\r\n        position: relative;\r\n        z-index: 1;\r\n        padding: 6px 24px;\r\n      }\r\n  \r\n      &:before{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 100%;\r\n        height: 1px;\r\n        left: 0;\r\n        right: 0;\r\n        background-color: $border-color;\r\n        top: 10px;\r\n      }\r\n      .badge{\r\n        font-size: 12px;\r\n      }\r\n    }\r\n    .conversation-list{\r\n      margin-bottom: 24px;\r\n      display: inline-block;\r\n      position: relative;\r\n      .arrow-left{\r\n        position: relative;\r\n        &:before{\r\n          content: \"\";\r\n       position: absolute;\r\n       top: 10px;\r\n       right: 100%;\r\n       border: 7px solid transparent;\r\n       border-right: 7px solid rgba($primary,0.1);\r\n         }\r\n      }\r\n  \r\n      .ctext-wrap{\r\n        padding: 12px 24px;\r\n        background-color: rgba($primary,0.1);\r\n        border-radius: 8px 8px 8px 0px;\r\n        overflow: hidden;\r\n       \r\n  \r\n        .conversation-name{\r\n          font-weight: 500;\r\n          color: $primary;\r\n          margin-bottom: 4px;\r\n          position: relative;\r\n      \r\n        }\r\n      }\r\n  \r\n      .dropdown{\r\n        float: right;\r\n        .dropdown-toggle{\r\n          font-size: 18px;\r\n          padding: 4px;\r\n          color: $gray-600;\r\n          @media (max-width: 575.98px) {\r\n            display: none;\r\n          }\r\n        }\r\n  \r\n        .dropdown-menu{\r\n          box-shadow: $box-shadow;\r\n          border: 1px solid $border-color\r\n        }\r\n      }\r\n  \r\n      .chat-time{\r\n        font-size: 12px;\r\n      }\r\n    }\r\n  \r\n    .right{\r\n      .conversation-list{\r\n        float: right;\r\n        .arrow-right{\r\n          position: relative;\r\n          &:before{\r\n            content: \"\";\r\n         position: absolute;\r\n         top: 10px;\r\n         left: 100%;\r\n         border: 7px solid transparent;\r\n         border-left: 7px solid $light;\r\n           }\r\n        }\r\n        .ctext-wrap{\r\n          background-color: $light;\r\n          text-align: right;\r\n          border-radius: 8px 8px 0px 8px;\r\n        }\r\n        .dropdown{\r\n          float: left;\r\n        }\r\n  \r\n        &.last-chat{\r\n          .conversation-list{\r\n            &:before{\r\n              right: 0;\r\n              left: auto;\r\n            }\r\n          }\r\n        }\r\n      }\r\n  \r\n    }\r\n  \r\n  \r\n  }\r\n  \r\n  .chat-input-section{\r\n    border-top: 1px solid $border-color;\r\n  }\r\n  \r\n  .chat-input{\r\n    border-radius: 30px;\r\n    background-color: $light !important;\r\n    border-color:  $light !important;\r\n    padding-right: 120px;\r\n  }\r\n  \r\n  .chat-input-links{\r\n    position: absolute;\r\n    right: 16px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    li{\r\n      a{\r\n        font-size: 16px;\r\n        line-height: 36px;\r\n        padding: 0px 4px;\r\n        display: inline-block;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .chat-send{\r\n    @media (max-width: 575.98px) {\r\n      min-width: auto;\r\n    }\r\n  }\r\n  \r\n  \r\n  .search-box .search-icon {\r\n    font-size: 16px;\r\n    position: absolute;\r\n    left: 13px;\r\n    top: 2px;\r\n    font-size: 15px;\r\n    line-height: 34px;\r\n   \r\n  }\r\n  \r\n  .search-box .form-control {\r\n    padding-left: 40px;\r\n    border-radius: 5px;\r\n  }\r\n  \r\n  ", "// \r\n// coming-soon.scss\r\n//\r\n\r\n\r\n\r\n.counter-number {\r\n    font-size: 32px;\r\n    text-align: center;\r\n    span {\r\n        font-size: 16px;\r\n        display: block;\r\n        padding-top: 7px;\r\n    }\r\n}\r\n\r\n.coming-box {\r\n    float: left;\r\n    width: 21%;\r\n    padding: 14px 7px;\r\n    margin: 0px $grid-gutter-width/2 $grid-gutter-width $grid-gutter-width/2;\r\n    background-color: $white;\r\n    border-radius: 5px;\r\n    border-radius: $card-inner-border-radius;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n@media (max-width: 991.98px) { \r\n    .coming-box {\r\n        width: 40%;\r\n    }\r\n }", "// \r\n// timeline.scss\r\n//\r\n/************** vertical timeline **************/ \r\n\r\n\r\n.timeline {\r\n    position: relative;\r\n    width: 100%;\r\n    padding: 30px 0;\r\n  }\r\n  \r\n  .timeline .timeline-end,\r\n  .timeline .timeline-start,\r\n  .timeline .timeline-year {\r\n    position: relative;\r\n    width: 100%;\r\n    text-align: center;\r\n    z-index: 1;\r\n  }\r\n  \r\n  .timeline .timeline-end p,\r\n  .timeline .timeline-start p,\r\n  .timeline .timeline-year p {\r\n    display: inline-block;\r\n    width: 80px;\r\n    height: 80px;\r\n    margin: 0;\r\n    padding: 30px 0;\r\n    text-align: center;\r\n    background: url(../images/user-img.png);\r\n    background-color: $primary;\r\n    background-repeat: no-repeat;\r\n    background-size: cover;\r\n    border-radius: 100px;\r\n    color: $white;\r\n    text-transform: uppercase;\r\n  }\r\n  \r\n  .timeline {\r\n    .timeline-year {\r\n      margin: 30px 0;\r\n    }\r\n    .timeline-continue {\r\n      position: relative;\r\n      width: 100%;\r\n      padding: 60px 0;\r\n      &:after {\r\n        position: absolute;\r\n        content: \"\";\r\n        width: 1px;\r\n        height: 100%;\r\n        top: 0;\r\n        left: 50%;\r\n        margin-left: -1px;\r\n        background: $primary;\r\n      }\r\n    }\r\n    .timeline-date {\r\n      margin: 40px 10px 0 10px;\r\n    }\r\n  }\r\n  \r\n  .timeline .row.timeline-left,\r\n  .timeline .row.timeline-right .timeline-date {\r\n    text-align: right;\r\n  }\r\n  \r\n  .timeline .row.timeline-right,\r\n  .timeline .row.timeline-left .timeline-date {\r\n    text-align: left;\r\n  }\r\n  \r\n  .timeline .timeline-date::after {\r\n    content: \"\";\r\n    display: block;\r\n    position: absolute;\r\n    width: 14px;\r\n    height: 14px;\r\n    top: 45px;\r\n    background: $primary;\r\n    border-radius: 15px;\r\n    z-index: 1;\r\n  }\r\n  \r\n  .timeline .row.timeline-left .timeline-date::after {\r\n    left: -7px;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-date::after {\r\n    right: -7px;\r\n  }\r\n  \r\n  .timeline .timeline-box,\r\n  .timeline .timeline-launch {\r\n    position: relative;\r\n    display: inline-block;\r\n    margin: 15px;\r\n    padding: 20px;\r\n    border: 1px solid $gray-200;\r\n    border-radius: 6px;\r\n  }\r\n  \r\n  .timeline .timeline-launch {\r\n    width: 100%;\r\n    margin: 15px 0;\r\n    padding: 0;\r\n    border: none;\r\n    text-align: center;\r\n    background: transparent;\r\n  }\r\n  \r\n  .timeline .timeline-box::after,\r\n  .timeline .timeline-box::before {\r\n    content: \"\";\r\n    display: block;\r\n    position: absolute;\r\n    width: 0;\r\n    height: 0;\r\n    border-style: solid;\r\n  }\r\n  \r\n  .timeline .row.timeline-left .timeline-box::after,\r\n  .timeline .row.timeline-left .timeline-box::before {\r\n    left: 100%;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-box::after,\r\n  .timeline .row.timeline-right .timeline-box::before {\r\n    right: 100%;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-box::after,\r\n  .timeline .timeline-launch .timeline-box::before {\r\n    left: 50%;\r\n    margin-left: -10px;\r\n  }\r\n  \r\n  .timeline .timeline-box::after {\r\n    top: 26px;\r\n    border-color: transparent transparent transparent $light;\r\n    border-width: 10px;\r\n  }\r\n  \r\n  .timeline .timeline-box::before {\r\n    top: 25px;\r\n    border-color: transparent transparent transparent $gray-200;\r\n    border-width: 11px;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-box::after {\r\n    border-color: transparent $light transparent transparent;\r\n  }\r\n  \r\n  .timeline .row.timeline-right .timeline-box::before {\r\n    border-color: transparent $gray-200 transparent transparent;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-box::after {\r\n    top: -20px;\r\n    border-color: transparent transparent $gray-200 transparent;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-box::before {\r\n    top: -19px;\r\n    border-color: transparent transparent $light transparent;\r\n    border-width: 10px;\r\n    z-index: 1;\r\n  }\r\n  \r\n  .timeline .timeline-launch .timeline-text {\r\n    width: 100%;\r\n  }\r\n  \r\n  @media (max-width: 767px) {\r\n    .timeline .timeline-continue::after {\r\n      left: 40px;\r\n    }\r\n  \r\n    .timeline .timeline-end,\r\n    .timeline .timeline-start,\r\n    .timeline .timeline-year,\r\n    .timeline .row.timeline-left,\r\n    .timeline .row.timeline-right .timeline-date,\r\n    .timeline .row.timeline-right,\r\n    .timeline .row.timeline-left .timeline-date,\r\n    .timeline .timeline-launch {\r\n      text-align: left;\r\n    }\r\n  \r\n    .timeline .row.timeline-left .timeline-date::after,\r\n    .timeline .row.timeline-right .timeline-date::after {\r\n      left: 47px;\r\n    }\r\n  \r\n    .timeline .timeline-box,\r\n    .timeline .row.timeline-right .timeline-date,\r\n    .timeline .row.timeline-left .timeline-date {\r\n      margin-left: 55px;\r\n    }\r\n  \r\n    .timeline .timeline-launch .timeline-box {\r\n      margin-left: 0;\r\n    }\r\n  \r\n    .timeline .row.timeline-left .timeline-box::after {\r\n      left: -20px;\r\n      border-color: transparent $light transparent transparent;\r\n    }\r\n  \r\n    .timeline .row.timeline-left .timeline-box::before {\r\n      left: -22px;\r\n      border-color: transparent $gray-200 transparent transparent;\r\n    }\r\n  \r\n    .timeline .timeline-launch .timeline-box::after,\r\n    .timeline .timeline-launch .timeline-box::before {\r\n      left: 30px;\r\n      margin-left: 0;\r\n    }\r\n  }\r\n  ", "// \r\n// Extras pages.scss\r\n//\r\n\r\n\r\n// pricing\r\n\r\n.pricing-nav-tabs{\r\n    display: inline-block;\r\n    background-color: $card-bg;\r\n    box-shadow: $box-shadow;\r\n    padding: 4px;\r\n    border-radius: 7px;\r\n    li{\r\n        display: inline-block;\r\n    }\r\n}\r\n\r\n\r\n.pricing-box{\r\n    .plan-features{\r\n        li{\r\n            padding: 7px 0px;\r\n        }\r\n    }\r\n}\r\n\r\n/*********************\r\n    Faqs\r\n**********************/ \r\n\r\n.faq-nav-tabs{\r\n    .nav-item{\r\n        margin: 0px 8px;\r\n    }\r\n    .nav-link{\r\n        text-align: center;\r\n        margin-bottom: 8px;\r\n        border: 2px solid $border-color;\r\n        color: $body-color;\r\n        .nav-icon{\r\n            font-size: 40px;\r\n            margin-bottom: 8px;\r\n            display: block;\r\n        }\r\n\r\n        &.active{\r\n            border-color: $primary;\r\n            background-color: transparent;\r\n            color: $body-color;\r\n\r\n            .nav-icon{\r\n                color: $primary;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.text-error{\r\n    font-size: 120px;\r\n    @media (max-width: 575.98px) {\r\n        font-size: 86px;\r\n    }\r\n}\r\n\r\n.error-text{\r\n    color: $danger;\r\n    position: relative;\r\n\r\n    .error-img{\r\n        position: absolute;\r\n        width: 120px;\r\n        left: -15px;\r\n        right: 0;\r\n        bottom: 47px;\r\n\r\n        @media (max-width: 575.98px) {\r\n            width: 86px;\r\n            left: -12px;\r\n            bottom: 38px;\r\n        }\r\n    }\r\n}"]}