import json
from django.core.management.base import BaseCommand, CommandError
from django.apps import apps
from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist

class Command(BaseCommand):
    help = 'Import data from a JSON file into a specified model'

    def add_arguments(self, parser):
        parser.add_argument('json_file', type=str, help='Path to the JSON file')
        parser.add_argument('app_name', type=str, help='Name of the Django app containing the model')
        parser.add_argument('model_name', type=str, help='Name of the model to import data into')
        parser.add_argument('--update', action='store_true', help='Update existing records based on unique fields')

    def handle(self, *args, **options):
        json_file = options['json_file']
        app_name = options['app_name']
        model_name = options['model_name']
        update_existing = options['update']

        try:
            # Get the model
            model = apps.get_model(app_name, model_name)
        except LookupError:
            raise CommandError(f'Model {model_name} not found in app {app_name}')

        try:
            # Read JSON file
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except FileNotFoundError:
            raise CommandError(f'JSON file {json_file} not found')
        except json.JSONDecodeError:
            raise CommandError(f'Invalid JSON format in {json_file}')

        if not isinstance(data, list):
            data = [data]  # Convert single object to list

        created_count = 0
        updated_count = 0
        error_count = 0

        with transaction.atomic():
            for item in data:
                try:
                    # Handle foreign key relationships
                    for field in model._meta.fields:
                        if field.is_relation:
                            field_name = field.name
                            if field_name in item:
                                try:
                                    # Get the related model
                                    related_model = field.related_model
                                    # Try to get the related instance
                                    related_instance = related_model.objects.get(id=item[field_name])
                                    item[field_name] = related_instance
                                except ObjectDoesNotExist:
                                    raise CommandError(f'Related {field.related_model.__name__} with id {item[field_name]} does not exist')

                    if update_existing:
                        # Try to find unique fields
                        unique_fields = [f.name for f in model._meta.fields if f.unique]
                        if unique_fields:
                            filter_kwargs = {
                                field: item[field]
                                for field in unique_fields
                                if field in item
                            }
                            obj, created = model.objects.update_or_create(
                                defaults=item,
                                **filter_kwargs
                            )
                            if created:
                                created_count += 1
                            else:
                                updated_count += 1
                        else:
                            # If no unique fields, just create
                            model.objects.create(**item)
                            created_count += 1
                    else:
                        # Just create new records
                        model.objects.create(**item)
                        created_count += 1
                except Exception as e:
                    error_count += 1
                    self.stderr.write(f'Error processing record: {str(e)}')

        self.stdout.write(self.style.SUCCESS(
            f'Successfully processed JSON data:\n'
            f'Created: {created_count}\n'
            f'Updated: {updated_count}\n'
            f'Errors: {error_count}'
        )) 