import json
from django.core.management.base import BaseCommand
from items.models import Category, Brand, Unit

class Command(BaseCommand):
    help = 'Initialize master data (categories, brands, units)'

    def handle(self, *args, **options):
        try:
            with open('initial_data.json', 'r') as f:
                data = json.load(f)

            # Create categories
            for cat_data in data['categories']:
                Category.objects.get_or_create(
                    name=cat_data['name'],
                    defaults={
                        'description': cat_data['description'],
                        'status': cat_data['status']
                    }
                )
            self.stdout.write(self.style.SUCCESS('Categories created successfully'))

            # Create brands
            for brand_data in data['brands']:
                Brand.objects.get_or_create(
                    name=brand_data['name'],
                    defaults={
                        'desc': brand_data['desc']
                    }
                )
            self.stdout.write(self.style.SUCCESS('Brands created successfully'))

            # Create units
            for unit_data in data['units']:
                Unit.objects.get_or_create(
                    name=unit_data['name'],
                    defaults={
                        'short_name': unit_data['short_name'],
                        'unit': unit_data['unit']
                    }
                )
            self.stdout.write(self.style.SUCCESS('Units created successfully'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}')) 