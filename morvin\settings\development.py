from .base import *
import os

DEBUG = True

ALLOWED_HOSTS = ['127.0.0.1', 'localhost', 'healthcheck.railway.app', '*.railway.app', 'software.sultanatmarquee.com', '.sultanatmarquee.com']

# Check if user wants to use SQLite instead of PostgreSQL
USE_SQLITE = os.environ.get('USE_SQLITE', 'false').lower() == 'true'

if USE_SQLITE:
    # SQLite Database Configuration
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR.parent / 'persistent_data' / 'db_data',
            'OPTIONS': {
                'timeout': 20,
                'check_same_thread': False,
            }
        }
    }
    
    # SQLite backup settings
    DBBACKUP_STORAGE_OPTIONS = {'location': BASE_DIR.parent / 'persistent_data' / 'db_backups'}
    
    # Ensure directories exist
    os.makedirs(BASE_DIR.parent / 'persistent_data', exist_ok=True)
    os.makedirs(BASE_DIR.parent / 'persistent_data' / 'db_backups', exist_ok=True)
    
else:
    # PostgreSQL Database Configuration
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': 'hall_db',
            'USER': 'postgres',
            'PASSWORD': 'admin',
            'HOST': 'localhost',
            'PORT': '5432',
            'OPTIONS': {
                'connect_timeout': 10,
            }
        }
    }
    
    # Additional PostgreSQL optimizations
    CONN_MAX_AGE = 0

# CSRF Trusted Origins
CSRF_TRUSTED_ORIGINS = [
    'https://software.sultanatmarquee.com',
    'https://*.railway.app',
    'http://localhost',
    'http://127.0.0.1',
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]


