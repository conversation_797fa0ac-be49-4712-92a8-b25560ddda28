/* Mobile Responsiveness Improvements */

/* Ensure proper mobile viewport behavior */
@media (max-width: 991.98px) {
    /* Hide sidebar by default on mobile */
    .vertical-menu {
        display: none;
        position: fixed;
        top: 70px;
        left: 0;
        height: calc(100vh - 70px);
        width: 260px;
        z-index: 1050;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        overflow-y: auto;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    /* Show sidebar when enabled */
    body.sidebar-enable .vertical-menu {
        display: block;
        transform: translateX(0);
    }

    /* Overlay for mobile sidebar */
    body.sidebar-enable::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1049;
    }

    /* Main content should not have left margin on mobile */
    .main-content {
        margin-left: 0 !important;
    }

    /* Navbar adjustments for mobile */
    .navbar-header {
        padding: 0 15px;
    }

    /* Make sure header elements are properly spaced */
    .navbar-brand-box {
        width: auto !important;
    }

    /* Ensure menu button is visible and properly sized */
    #vertical-menu-btn {
        display: inline-block !important;
        font-size: 18px;
        padding: 8px 12px;
        margin-right: 10px;
    }

    /* Page content padding adjustments for mobile */
    .page-content {
        padding: 15px;
    }

    .page-title-box {
        padding: 15px 0;
    }

    /* Table responsiveness */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Card adjustments */
    .card {
        margin-bottom: 15px;
    }

    /* Form adjustments */
    .form-group {
        margin-bottom: 1rem;
    }

    /* Button adjustments */
    .btn {
        margin-bottom: 5px;
    }

    /* Modal adjustments */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100vw - 20px);
    }
}

/* Tablet specific adjustments */
@media (min-width: 768px) and (max-width: 991.98px) {
    .vertical-menu {
        width: 280px;
    }

    .page-content {
        padding: 20px;
    }
}

/* Small mobile devices */
@media (max-width: 575.98px) {
    .vertical-menu {
        width: 100%;
        max-width: 280px;
    }

    .navbar-header {
        padding: 0 10px;
    }

    .page-content {
        padding: 10px;
    }

    .page-title h4 {
        font-size: 1.2rem;
    }

    .breadcrumb {
        font-size: 0.85rem;
    }

    /* Stack buttons vertically on very small screens */
    .btn-group-vertical .btn {
        width: 100%;
        margin-bottom: 5px;
    }
}

/* Fix for dark mode */
.dark-mode .vertical-menu {
    background: #1f293f;
    color: #fff;
}

.dark-mode body.sidebar-enable::before {
    background: rgba(0, 0, 0, 0.7);
}

/* Animation improvements */
.vertical-menu {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body.sidebar-enable .vertical-menu {
    transform: translateX(0);
}

/* Ensure proper z-index layering */
.vertical-menu {
    z-index: 1050;
}

body.sidebar-enable::before {
    z-index: 1049;
}

.navbar-header {
    z-index: 1051;
}

/* Fix for touch devices */
@media (hover: none) and (pointer: coarse) {
    .vertical-menu a:hover {
        background-color: rgba(0, 0, 0, 0.1);
    }
}

/* Landscape orientation fixes */
@media (max-width: 991.98px) and (orientation: landscape) {
    .vertical-menu {
        height: 100vh;
        top: 0;
        padding-top: 70px;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .vertical-menu {
        transition: none;
    }
} 