import os

# Default to SQLite settings unless specifically overridden
# This ensures your data is always safe during deployments

# Set USE_SQLITE environment variable to control database choice
os.environ.setdefault('USE_SQLITE', 'true')

# Load the appropriate settings module
settings_module = os.environ.get('DJANGO_SETTINGS_MODULE', 'morvin.settings.development')

if 'sqlite' in settings_module:
    from .sqlite import *
elif 'postgresql' in settings_module:
    from .postgresql import *
elif 'production' in settings_module:
    from .production import *
elif 'development' in settings_module:
    from .development import *
else:
    # Default fallback to development settings
    from .development import *
