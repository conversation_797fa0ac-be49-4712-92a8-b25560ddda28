from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
from django.contrib.auth import get_user_model
from django.db import IntegrityError

class Command(BaseCommand):
    help = 'Initialize the database completely for Railway deployment'

    def handle(self, *args, **options):
        try:
            # Ensure all migrations are applied
            self.stdout.write("🔄 Running migrations...")
            call_command('migrate', verbosity=0, interactive=False)
            
            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                self.stdout.write(f"✅ Database has {len(tables)} tables")
                
                # Check for essential tables
                table_names = [table[0] for table in tables]
                essential_tables = ['django_session', 'auth_user', 'django_content_type']
                
                for table in essential_tables:
                    if table in table_names:
                        self.stdout.write(f"✅ Table '{table}' exists")
                    else:
                        self.stdout.write(f"❌ Table '{table}' missing")
            
            # Create superuser if needed
            User = get_user_model()
            if User.objects.filter(is_superuser=True).exists():
                self.stdout.write("✅ Superuser already exists")
            else:
                try:
                    User.objects.create_superuser(
                        username='admin',
                        email='<EMAIL>',
                        password='admin123'
                    )
                    self.stdout.write("✅ Superuser created (admin/admin123)")
                except Exception as e:
                    self.stdout.write(f"⚠️  Could not create superuser: {e}")
            
            self.stdout.write("🎉 Database initialization complete!")
            
        except Exception as e:
            self.stdout.write(f"❌ Database initialization failed: {e}")
            raise 