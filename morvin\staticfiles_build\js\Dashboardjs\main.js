// Calender
var data = calenderEvents;  

var formattedEvents = [];
  
  // Assuming serializedEvents is an object with numeric keys
  for (var key in data) {
      if (calenderEvents.hasOwnProperty(key)) {
          var event = calenderEvents[key];
          formattedEvents.push({
              title: event.bill_no + " - " + event.customer_name,
              start: event.event_date,
              allDay: true,
              extendedProps: {
                  billNo: event.bill_no,
                  customerName: event.customer_name
              }
          });
      }
  }
  
  console.log("Formatted events:", formattedEvents);


      var calendarEl = document.getElementById('calendar');
      var calendar = new FullCalendar.Calendar(calendarEl, {
        //   plugins: ["bootstrap", "interaction", "dayGrid", "timeGrid"],
          editable: true,
          droppable: true,
          selectable: true,
          defaultView: "dayGridMonth",
          themeSystem: "bootstrap",
          header: {
              left: "prev,next today",
              center: "title",
              right: "dayGridMonth,timeGridWeek,timeGridDay,listMonth",
          },
          events: formattedEvents,

          eventClick: function (e) {
            a.modal("show");

            
            var value = serializedEvents;

            console.log(typeof(value));
            l = e.event;

            var customerName = l.title;
            var eventDate = l.start.toISOString().split('T')[0];
            var eventTime = 'Night'; 



            var eventTime = null;
            for (var i = 0; i < value.length; i++) {
                var event = value[i].fields;
                if (event.event_title === customerName) {
                  eventTime = event.event_time;
                  break;
                }
              }
              
              if (eventTime !== null) {
                console.log("Event time for " + customerName + ": " + eventTime);
              } else {
                console.log("Event not found for customer: " + customerName);
              }
                    
      
            console.log(customerName);
            console.log(eventDate);
            console.log(eventTime);

            v("#Customer_Name").val(customerName);
            v("#Event_Date").val(eventDate); 
            v("#Event_Time").val(eventTime);
        },
        
          dateClick: function (e) {
              o(e);
          }
      });

      calendar.render();
























/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////







// Sales bar chart

var montly_sale = document.getElementById("sale").getAttribute("data-my-variable");

// // Parse the values as JSON arrays
var totalSales = JSON.parse(montly_sale);
const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
const salesChartMonthlyCtx = document.getElementById('salesChartMonthly').getContext('2d');
const expenseHeadsCtx = document.getElementById('expense-heads').getContext('2d');

const salesChartMonthly = new Chart(salesChartMonthlyCtx, {
    type: 'bar',
    data: {
        labels: months,
        datasets: [{
            label: 'Monthly Sales',

            data: totalSales,
            backgroundColor: [

                'rgba(51, 255, 189, 0.5)'
            ],
            borderColor: [

                'rgba(0, 0, 0, 0.5)'
            ],
            borderWidth: 2

        }],
    },
});



// Expense heads Pie Chart

var expense_heads = document.getElementById("expense_heads").getAttribute("data-my-variable");

// console.log(expense_heads)
// Split the string into an array of substrings using a comma as the delimiter
var expense_headsArray = expense_heads.split(",");

// Use map() to convert the substrings to integers using parseInt()
var expense_headsIntegers = expense_headsArray.map(function (str) {
    return parseInt(str, 10) || 0; // Use 10 for decimal base
});

// console.log(expense_headsIntegers);

const heads = ['Construction and repairs', 'Daily expensess' , 'Other expenses', 'Salaries', 'Event expense'];
const expenseHeads = new Chart(expenseHeadsCtx, {
    type: 'pie',
    data: {
        labels: heads,
        datasets: [{
            data: expense_headsIntegers,
            backgroundColor: [
                '#7FB3D5', // Moderate Soft Blue
                '#A3CDA5', // Moderate Soft Green
                '#E6C76A', // Moderate Soft Yellow
                '#F5B24D', // Moderate Soft Orange
                '#BFA2E1'  // Moderate Soft Purple
            ],
            hoverBackgroundColor: [
                '#7FB3D5', // Moderate Soft Blue
                '#A3CDA5', // Moderate Soft Green
                '#E6C76A', // Moderate Soft Yellow
                '#F5B24D', // Moderate Soft Orange
                '#BFA2E1'  // Moderate Soft Purple
            ],
            borderWidth: 0
        }],
    },

});




// Area Chart For Expense



var montly_expense = document.getElementById("expense").getAttribute("data-my-variable");

var totalExpense = JSON.parse(montly_expense);

// console.log(totalExpense)

// Create the chart options
var options1 = {
    chart: { type: "area", sparkline: { enabled: true } },
    series: [{ data: totalExpense }],
    stroke: { curve: "smooth", width: 2 },
    colors: ["#525ce5"],
    tooltip: {
        fixed: { enabled: false },
        x: { show: false },
        y: {
            title: {
                formatter: function (e) {
                    return "";
                },
            },
        },
        marker: { show: false },
    },
};

// Function to set the chart height based on screen width
function setChartHeight(chart) {
    var chartHeight = window.innerWidth >= 1800 ? 250 : 80;
    chart.updateOptions({
        chart: {
            height: chartHeight
        }
    });
}

// Create and render the chart
var chart = new ApexCharts(document.querySelector("#stastics-chartss"), options1);

chart.render();

// Call the setChartHeight function initially
setChartHeight(chart);

// Update the chart height when the window is resized
window.addEventListener('resize', function () {
    setChartHeight(chart);
});


