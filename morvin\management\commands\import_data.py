import os
import json
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import transaction

class Command(BaseCommand):
    help = 'Import SQLite data to PostgreSQL for Railway deployment'

    def handle(self, *args, **options):
        
        # Check if import should run
        if not os.environ.get('RUN_IMPORT', 'false').lower() == 'true':
            self.stdout.write(
                self.style.WARNING('RUN_IMPORT not set. Skipping data import.')
            )
            return
        
        json_file = 'railway_migration_data.json'
        
        self.stdout.write(
            self.style.SUCCESS('🚀 Starting data import to Railway PostgreSQL...')
        )
        
        if not os.path.exists(json_file):
            self.stdout.write(
                self.style.ERROR(f'❌ Data file not found: {json_file}')
            )
            return
        
        try:
            # Load and import data
            with transaction.atomic():
                self.stdout.write('🔄 Importing data...')
                call_command('loaddata', json_file, verbosity=2)
                
            self.stdout.write(
                self.style.SUCCESS('✅ Data import completed successfully!')
            )
            
            # Verify import
            from django.contrib.auth.models import User
            user_count = User.objects.count()
            superuser_count = User.objects.filter(is_superuser=True).count()
            
            self.stdout.write(f"👥 Total users: {user_count}")
            self.stdout.write(f"👑 Superusers: {superuser_count}")
            
            if superuser_count > 0:
                superuser = User.objects.filter(is_superuser=True).first()
                self.stdout.write(f"🔑 Superuser username: {superuser.username}")
                self.stdout.write(
                    self.style.WARNING('⚠️  Reset superuser password: python manage.py changepassword ' + superuser.username)
                )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Import failed: {e}')
            )
