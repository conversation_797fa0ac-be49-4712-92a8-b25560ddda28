from django.urls import path
from django.views.generic import TemplateView
from ecommerce import views

urlpatterns = [

    path('expense_summary', views.ExpenseSummary.as_view(),name='expense_summary'),
    path('get-product-price/', views.get_product_price, name='get_product_price'),
    # Ecommerce
    
    path('hall-sale-report', views.HallSaleSummary.as_view(),name='hall-sale-report'),
    path('hall-expense-report', views.HallExpenseSummary.as_view(),name='hall-expense-report'),
    
    path('kitchen-sale-report', views.KitchenSaleSummary.as_view(),name='kitchen-sale-report'),
    path('kitchen-expense-report', views.KitchenExpenseSummary.as_view(),name='kitchen-expense-report'),
    
    path('summaries', views.Summary.as_view(),name='summaries'),

    path('ecommerce_product', views.Products.as_view(),name='ecommerce_product'),
    path('ecommerce_product_detail', views.ProductsDetail.as_view(),name='ecommerce_product_detail'),
    path('product-list', views.ProductList.as_view(),name='ecommerce-product-list'),
    path('event-sale', views.Eventsale.as_view(),name='event-sale'),
    path('tentativeSales', views.Tentative.as_view(),name='tentativeSales'),


    
    path('update-event-sale/<int:sale_id>', views.UpdateEventsale.as_view(),name='update-event-sale'),
    path('update-event-expense/<int:expense_id>', views.UpdateEventExpense.as_view(),name='update-event-expense'),
    
    
    
    path('update-product/<int:product_id>', views.UpdateProducts.as_view(),name='update-product'),
    path('delete-product/<int:product_id>/', views.DeleteProducts, name='delete-product'),
    path('delete-unit/<int:unit_id>/', views.DeleteUnit, name='delete-unit'),
    path('update-unit/<int:unit_id>/', views.UpdateUnit.as_view(), name='update-unit'),
    path('update-category/<int:category_id>/', views.UpdateCategory.as_view(), name='update-category'),
    path('delete-category/<int:category_id>/', views.DeleteCategory, name='delete-category'),
    path('update-brand/<int:brand_id>/', views.UpdateBrand.as_view(), name='update-brand'),
    path('delete-brand/<int:brand_id>/', views.DeleteBrand, name='delete-brand'),  
    path('delete-expense/<int:expense_id>/', views.DeleteExpense, name='delete-expense'),   



    path('delete-sale/<int:sale_id>/', views.delete_sale, name='delete-sale'),
    path('kitchen-sale', views.Kitchensale.as_view(),name='kitchen-sale'),
    path('kitchen-expense', views.Kitchenexpense.as_view(),name='kitchen-expense'),
    path('kitchen-expense-update/<int:kitchen_expense_id>/', views.KitchenexpenseUpdate.as_view(),name='kitchen-expense-update'),
    path('kitchen-expense-delete/<int:kitchen_expense_id>/', views.delete_kitchen_expense,name='kitchen-expense-delete'),

    path('monthly-expense-summary', views.product_list, name='monthly-expense-summary'),

    path('event-expense', views.Eventexpense.as_view(),name='event-expense'),
    path('ecommerce_add_category', views.ProductsAddCategory.as_view(),name='ecommerce_add_category'),
    path('ecommerce_checkout', views.ProductsCheckout.as_view(),name='ecommerce_checkout'),
    path('ecommerce_shops', views.ProductsShops.as_view(),name='ecommerce_shops'),
    path('ecommerce_add_product', views.ProductsAddProduct.as_view(),name='ecommerce_add_product'),
    path('ecommerce_add_inventory', views.ProductsAddInventory.as_view(),name='ecommerce_add_inventory'),
    path('ecommerce_add_brand', views.ProductsAddBrand.as_view(),name='ecommerce_add_brand'),
    path('ecommerce_add_unit', views.ProductsAddUnit.as_view(),name='ecommerce_add_unit'),

    # path('ecommerce_add_product', views.ProductsAddProduct.as_view(),name='ecommerce_add_product'),
    path('calendar/', views.Calendar.as_view(),name='calendar'),
    path('update_food_menu/<int:pk>', views.UpdateFoodMenu.as_view(), name='update-deals'),
    path('event-sale/<int:event_id>/', views.EventSaleDetailView.as_view(), name='event_sale_detail'),
  
    # Rental Management URLs
    path('rental/products/', views.RentalProductList.as_view(), name='rental_product_list'),
    path('rental/products/add/', views.add_rental_product, name='add-rental-product'),
    path('rental/products/update/<int:product_id>/', views.update_rental_product, name='update-rental-product'),
    path('rental/products/delete/<int:product_id>/', views.delete_rental_product, name='delete-rental-product'),
    path('rental/issue/', views.RentalIssue.as_view(), name='rental_issue'),
    path('rental/return/<int:rental_id>/', views.RentalReturn.as_view(), name='rental_return'),
    path('rental/active/', views.ActiveRentalsList.as_view(), name='active_rentals_list'),
    path('rental/returned/', views.ReturnedRentalsList.as_view(), name='returned_rentals_list'),
    path('rental/invoice/<int:rental_id>/', views.RentalInvoice.as_view(), name='rental_invoice'),
    path('rental/update/<int:rental_id>/', views.update_rental, name='update-rental'),
    path('rental/update-returned/<int:rental_id>/', views.update_returned_rental, name='update-returned-rental'),
    path('rental/delete/<int:rental_id>/', views.delete_rental, name='delete_rental'),  # Fixed URL pattern name
    path('rental/detail/<int:rental_id>/', views.RentalDetail.as_view(), name='rental_detail'),
    path('rental/issue-invoice/<int:rental_id>/', views.RentalIssueInvoice.as_view(), name='rental_issue_invoice'),
    path('rental/return-invoice/<int:rental_id>/', views.RentalReturnInvoice.as_view(), name='rental_return_invoice'),
    path('rental/thermal-invoice/<int:rental_id>/', views.RentalThermalInvoice.as_view(), name='rental_thermal_invoice'),
    path('rental/return-thermal-invoice/<int:rental_id>/', views.RentalReturnThermalInvoice.as_view(), name='rental_return_thermal_invoice'),
    path('rental/return-all/<int:rental_id>/', views.ReturnAllRental.as_view(), name='return_all_rental'),
    path('rental/reports/active/', views.ActiveRentalsReport.as_view(), name='active_rentals_report'),
    path('rental/reports/history/', views.RentalHistoryReport.as_view(), name='rental_history_report'),
    path('rental/reports/overdue/', views.OverdueRentalsReport.as_view(), name='overdue_rentals_report'),
    path('rental/reports/frequent-renters/', views.FrequentRentersReport.as_view(), name='frequent_renters_report'),
    path('rental/reports/returned-items/', views.ReturnedItemsReport.as_view(), name='returned_items_report'),
    path('rental/reports/damaged-items/', views.DamagedItemsReport.as_view(), name='damaged_items_report'),
    path('rental/reports/missing-items/', views.MissingItemsReport.as_view(), name='missing_items_report'),
    # Debug URL
    path('rental/debug/', views.debug_rental_data, name='debug_rental_data'),
    
    # Rental Reports URLs
    path('rental/reports/rental-income/', views.RentalIncomeReport.as_view(), name='rental_income_report'),
    path('rental/reports/damage-missing-charges/', views.DamageMissingChargesReport.as_view(), name='damage_missing_charges_report'),
    path('rental/reports/active-rentals/', views.ActiveRentalsReport.as_view(), name='active_rentals_report'),
    path('rental/reports/rental-history/', views.RentalHistoryReport.as_view(), name='rental_history_report'),
    path('rental/reports/overdue-rentals/', views.OverdueRentalsReport.as_view(), name='overdue_rentals_report'),
    path('rental/reports/frequent-renters/', views.FrequentRentersReport.as_view(), name='frequent_renters_report'),
    path('rental/reports/returned-items/', views.ReturnedItemsReport.as_view(), name='returned_items_report'),
    path('rental/reports/damaged-items/', views.DamagedItemsReport.as_view(), name='damaged_items_report'),
    path('rental/reports/missing-items/', views.MissingItemsReport.as_view(), name='missing_items_report'),
    path('upcoming-events/', views.UpcomingEventsView.as_view(), name='upcoming_events'),
    path('event/<int:pk>/', views.EventDetailView.as_view(), name='event_detail'),
    path('tentative-events/', views.TentativeEventsView.as_view(), name='tentative_events'),
    
    # Public Calendar URLs
    path('public-calendar/', views.PublicCalendar.as_view(), name='public_calendar'),
    path('calendar-availability/', views.calendar_availability, name='calendar_availability'),
]
