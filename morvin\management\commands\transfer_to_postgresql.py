import os
import json
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
from django.conf import settings
from django.apps import apps
from django.core.serializers import serialize
from django.core.management.color import no_style
from django.db import transaction


class Command(BaseCommand):
    help = 'Transfer data from SQLite to PostgreSQL database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--backup-first',
            action='store_true',
            help='Create a backup of SQLite database before transfer',
        )
        parser.add_argument(
            '--export-only',
            action='store_true',
            help='Only export data to JSON, dont import to PostgreSQL',
        )

    def handle(self, *args, **options):
        if options['backup_first']:
            self.stdout.write('Creating backup of current database...')
            call_command('backup_database')
            self.stdout.write(self.style.SUCCESS('Backup created successfully'))

        # Export all data from SQLite
        self.stdout.write('Exporting data from SQLite...')
        
        # Get all models
        all_models = apps.get_models()
        
        # Export data
        export_data = []
        for model in all_models:
            if model._meta.app_label in ['auth', 'contenttypes', 'admin', 'sessions']:
                continue  # Skip system models
                
            model_name = f"{model._meta.app_label}.{model._meta.model_name}"
            queryset = model.objects.all()
            
            if queryset.exists():
                self.stdout.write(f'Exporting {queryset.count()} records from {model_name}')
                serialized = serialize('json', queryset)
                export_data.extend(json.loads(serialized))

        # Save to file
        export_file = 'data_export.json'
        with open(export_file, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        self.stdout.write(self.style.SUCCESS(f'Data exported to {export_file}'))
        
        if options['export_only']:
            self.stdout.write(self.style.SUCCESS('Export completed. Run without --export-only to import to PostgreSQL'))
            return

        # Import to PostgreSQL
        self.stdout.write('Importing data to PostgreSQL...')
        
        # Switch to PostgreSQL temporarily
        self.stdout.write('Make sure your DATABASE_URL environment variable is set for PostgreSQL')
        self.stdout.write('Then run: python manage.py loaddata data_export.json')
        
        self.stdout.write(self.style.SUCCESS('Transfer preparation completed!'))
        self.stdout.write(self.style.WARNING('Next steps:'))
        self.stdout.write('1. Set DATABASE_URL environment variable to your PostgreSQL connection string')
        self.stdout.write('2. Run: python manage.py migrate')
        self.stdout.write('3. Run: python manage.py loaddata data_export.json')
        self.stdout.write('4. Deploy to Railway') 