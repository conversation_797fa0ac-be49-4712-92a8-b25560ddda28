// Calender
var data = calenderEvents;  

var formattedEvents = [];
  
  // Assuming serializedEvents is an object with numeric keys
  for (var key in data) {
      if (calenderEvents.hasOwnProperty(key)) {
          var event = calenderEvents[key];
          formattedEvents.push({
              title: event.bill_no + " - " + event.customer_name,
              start: event.event_date,
              allDay: true,
              extendedProps: {
                  billNo: event.bill_no,
                  customerName: event.customer_name
              }
          });
      }
  }
  
  console.log("Formatted events:", formattedEvents);


      var calendarEl = document.getElementById('calendar');
      var calendar = new FullCalendar.Calendar(calendarEl, {
        //   plugins: ["bootstrap", "interaction", "dayGrid", "timeGrid"],
          editable: true,
          droppable: true,
          selectable: true,
          defaultView: "dayGridMonth",
          themeSystem: "bootstrap",
          header: {
              left: "prev,next today",
              center: "title",
              right: "dayGridMonth,timeGridWeek,timeGridDay,listMonth",
          },
          events: formattedEvents,

          eventClick: function (e) {
            a.modal("show");

            
            var value = serializedEvents;

            console.log(typeof(value));
            l = e.event;

            var customerName = l.title;
            var eventDate = l.start.toISOString().split('T')[0];
            var eventTime = 'Night'; 



            var eventTime = null;
            for (var i = 0; i < value.length; i++) {
                var event = value[i].fields;
                if (event.event_title === customerName) {
                  eventTime = event.event_time;
                  break;
                }
              }
              
              if (eventTime !== null) {
                console.log("Event time for " + customerName + ": " + eventTime);
              } else {
                console.log("Event not found for customer: " + customerName);
              }
                    
      
            console.log(customerName);
            console.log(eventDate);
            console.log(eventTime);

            v("#Customer_Name").val(customerName);
            v("#Event_Date").val(eventDate); 
            v("#Event_Time").val(eventTime);
        },
        
          dateClick: function (e) {
              o(e);
          }
      });

      calendar.render();
























/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////







// Sales bar chart - Add error checking
try {
    var saleElement = document.getElementById("sale");
    console.log("Sale element found:", saleElement);
    
    if (saleElement) {
        var montly_sale = saleElement.getAttribute("data-my-variable");
        console.log("Monthly sale data:", montly_sale);
        
        if (montly_sale) {
            var totalSales = JSON.parse(montly_sale);
            console.log("Parsed sales data:", totalSales);
            
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const salesChartMonthlyCtx = document.getElementById('salesChartMonthly');
            console.log("Sales chart canvas found:", salesChartMonthlyCtx);
            
            if (salesChartMonthlyCtx) {
                const salesChartMonthly = new Chart(salesChartMonthlyCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: months,
                        datasets: [{
                            label: 'Monthly Sales',
                            data: totalSales,
                            backgroundColor: 'rgba(51, 255, 189, 0.5)',
                            borderColor: 'rgba(0, 0, 0, 0.5)',
                            borderWidth: 2
                        }],
                    },
                });
                console.log("Sales chart created successfully");
            } else {
                console.error("Canvas element 'salesChartMonthly' not found in DOM");
            }
        } else {
            console.error("No monthly sales data attribute found");
        }
    } else {
        console.error("Element with id 'sale' not found");
    }
} catch (e) {
    console.error("Error creating sales chart:", e);
}

// Expense heads Pie Chart - Add error checking
try {
    var expenseHeadsElement = document.getElementById("expense_heads");
    if (expenseHeadsElement) {
        var expense_heads = expenseHeadsElement.getAttribute("data-my-variable");
        if (expense_heads) {
            var expense_headsArray = expense_heads.split(",");
            var expense_headsIntegers = expense_headsArray.map(function (str) {
                return parseInt(str, 10) || 0;
            });

            const heads = ['Construction and repairs', 'Daily expenses', 'Other expenses', 'Salaries', 'Event expense'];
            const expenseHeadsCtx = document.getElementById('expense-heads');
            
            if (expenseHeadsCtx) {
                const expenseHeads = new Chart(expenseHeadsCtx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: heads,
                        datasets: [{
                            data: expense_headsIntegers,
                            backgroundColor: [
                                '#7FB3D5', '#A3CDA5', '#E6C76A', '#F5B24D', '#BFA2E1'
                            ],
                            borderWidth: 0
                        }],
                    },
                });
            }
        }
    }
} catch (e) {
    console.error("Error creating expense heads chart:", e);
}

// Area Chart For Expense
try {
    var expenseElement = document.getElementById("expense");
    if (expenseElement) {
        var montly_expense = expenseElement.getAttribute("data-my-variable");
        if (montly_expense) {
            var totalExpense = JSON.parse(montly_expense);

            var options1 = {
                chart: { type: "area", sparkline: { enabled: true } },
                series: [{ data: totalExpense }],
                stroke: { curve: "smooth", width: 2 },
                colors: ["#525ce5"],
                tooltip: {
                    fixed: { enabled: false },
                    x: { show: false },
                    y: {
                        title: {
                            formatter: function (e) {
                                return "";
                            },
                        },
                    },
                    marker: { show: false },
                },
            };

            function setChartHeight(chart) {
                var chartHeight = window.innerWidth >= 1800 ? 250 : 80;
                chart.updateOptions({
                    chart: {
                        height: chartHeight
                    }
                });
            }

            var chart = new ApexCharts(document.querySelector("#stastics-chartss"), options1);
            chart.render();
            setChartHeight(chart);

            window.addEventListener('resize', function () {
                setChartHeight(chart);
            });
        }
    }
} catch (e) {
    console.error("Error creating expense area chart:", e);
}



