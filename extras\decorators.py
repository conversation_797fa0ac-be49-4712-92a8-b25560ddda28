from django.shortcuts import redirect
from django.contrib import messages
from functools import wraps
from .models import UserProfile

def admin_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # Allow superusers access without needing a UserProfile
        if request.user.is_superuser:
            return view_func(request, *args, **kwargs)
        
        try:
            profile = UserProfile.objects.get(user=request.user)
            if profile.role == UserProfile.ADMIN:
                return view_func(request, *args, **kwargs)
            else:
                messages.error(request, "Access denied. Admin privileges required.")
                return redirect('index')
        except UserProfile.DoesNotExist:
            # Create profile for users who don't have one (with default role)
            profile = UserProfile.objects.create(user=request.user)
            messages.error(request, "Access denied. Admin privileges required.")
            return redirect('index')
    return _wrapped_view 