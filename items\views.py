from django.shortcuts import render, get_object_or_404
from django.views.generic.base import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from .models import MyProducts, Deals
from ecommerce.models import EventSale, EventExpense
import json
from django.shortcuts import render , redirect,get_object_or_404
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views import View
from django.contrib.auth.decorators import login_required
import csv
import io
from django.db import transaction
from .models import Category, Brand, Unit
from vendors.models import Vendor




from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic.base import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from .models import MyProducts, Deals
from django.contrib import messages
from django.http import JsonResponse
import json

class create_menu(LoginRequiredMixin, TemplateView):
    template_name = "items/create-deal.html"
    LOGIN_URL = "account/login"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        products = MyProducts.objects.filter(status=1)
        product_json = []
        for product in products:
            product_json.append({
                'id': product.id, 
                'name': product.product_name, 
                'price': float(product.price)
            })
        context['page_title'] = "Create Deal"
        context['products'] = products
        context['product_json'] = json.dumps(product_json)
        return context

    def post(self, request):
        try:
            deal_name = request.POST.get('deal_name', '').strip()
            product_ids = request.POST.getlist('product_ids[]')
            
            print(f"Received deal_name: '{deal_name}'")  # Debug
            print(f"Received product_ids: {product_ids}")  # Debug
            print(f"POST data: {dict(request.POST)}")  # Debug

            # Validate input
            if not deal_name:
                return JsonResponse({
                    'success': False, 
                    'message': 'Deal name is required.'
                }, status=400)
                
            if not product_ids or len(product_ids) == 0:
                return JsonResponse({
                    'success': False, 
                    'message': 'Please select at least one product.'
                }, status=400)

            # Check if deal name already exists
            if Deals.objects.filter(code=deal_name).exists():
                return JsonResponse({
                    'success': False, 
                    'message': 'A deal with this name already exists.'
                }, status=400)

            # Create the deal first
            deal = Deals.objects.create(code=deal_name)
            print(f"Deal created with ID: {deal.id}")  # Debug
            
            # Validate products exist and are active
            valid_products = []
            for product_id in product_ids:
                try:
                    product = MyProducts.objects.get(id=int(product_id), status=1)
                    valid_products.append(product)
                    print(f"Added product: {product.product_name}")  # Debug
                except (MyProducts.DoesNotExist, ValueError) as e:
                    deal.delete()  # Cleanup
                    print(f"Product error: {e}")  # Debug
                    return JsonResponse({
                        'success': False,
                        'message': f'Product with ID {product_id} not found or inactive.'
                    }, status=400)
            
            # Add products to the deal
            if valid_products:
                deal.menu_items.set(valid_products)
                print(f"Successfully added {len(valid_products)} products to deal")  # Debug
            
            return JsonResponse({
                'success': True,
                'message': f'Deal "{deal_name}" created successfully with {len(valid_products)} items!',
                'deal_id': deal.id
            })
            
        except Exception as e:
            print(f"Unexpected error: {str(e)}")  # Debug
            # Cleanup if deal was created
            if 'deal' in locals() and hasattr(deal, 'id'):
                try:
                    deal.delete()
                    print("Cleaned up deal due to error")  # Debug
                except:
                    pass
            
            return JsonResponse({
                'success': False,
                'message': f'An error occurred: {str(e)}'
            }, status=500)

# Simple function-based view for deleting deals
def delete_deal(request):
    if request.method == "POST":
        try:
            deal_id = request.POST.get('deal_id')
            print(f"Attempting to delete deal with ID: {deal_id}")  # Debug
            
            if not deal_id:
                return JsonResponse({
                    'success': False, 
                    'message': 'Deal ID is required.'
                }, status=400)
            
            deal = get_object_or_404(Deals, pk=deal_id)
            deal_name = deal.code
            deal.delete()
            
            print(f"Successfully deleted deal: {deal_name}")  # Debug
            
            return JsonResponse({
                'success': True, 
                'message': f'Deal "{deal_name}" deleted successfully!'
            })
        except Exception as e:
            print(f"Error deleting deal: {str(e)}")  # Debug
            return JsonResponse({
                'success': False, 
                'message': f'Error deleting deal: {str(e)}'
            }, status=500)
    
    return JsonResponse({
        'success': False, 
        'message': 'Invalid request method'
    }, status=405)




class Calculate(LoginRequiredMixin,TemplateView):
    template_name = "items/pos.html"
    
    LOGIN_URL = "account/login"  
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        products = MyProducts.objects.filter(status=1)
        product_json = []
        for product in products:
            product_json.append({'id': product.id, 'name': product.product_name, 'price': float(product.price)})
        context['page_title'] = "Point of Sale"
        context['products'] = products
        context['product_json'] = json.dumps(product_json)
        return context
    

     

def custom_menu(request):
            context = {}
        # try:
            if request.method == "POST":
                

                    event_status = request.POST.get('status')
                    event_time = request.POST.get('event-time')

                    event_date = request.POST.get('event-date')
                    number_of_people = request.POST.get('no-of-people')
                    setup = request.POST.get('setup')

                    deal_number = request.POST.get('deals')

                    # deal_id = request.POST.get('deals')
                    # deal = Deals.objects.get(id=deal_id)

                    deal = request.POST.get('deals')
                    location = request.POST.get('location')

                
                    customer_name = request.POST.get('customer-name')
                    customer_number = request.POST.get('customer-number')
                    per_head = request.POST.get('per-head')
                    hall = request.POST.get('hall')
                    per_head = float(per_head)

                    hall = float(hall)

                    extra_charge = request.POST.get('extra-charges')
                    stage_charge = request.POST.get('stage-charges')
                    entry_charge = request.POST.get('entry-charges')
                    
                    menu_amount = request.POST.get('menu-amount')
                    menu_amount = float(menu_amount)
                    food_menu = request.POST.get('food-menu')

                    gents = request.POST.get('gents')
                    ladies = request.POST.get('ladies')

                    details = request.POST.get('details')
                    received_ammount = request.POST.get('received-amount')
                    discount_amount = request.POST.get('discount-amount')
                    discount_type = request.POST.get('discount')

                    request.session['deals'] = deal_number
                    total = (int(number_of_people) * int(per_head)) + (int(extra_charge) + int(stage_charge) + int(entry_charge)) 

                    
                    discount = 0

                    # Calculate by discount
                    if discount_type == 'percent':
                        
                        discount =  ((int(discount_amount) / 100) * total)
                        total = int(total) - int(discount)


                    # Calculate by fix price
                    elif discount_type == "fix":
                        discount = discount_amount
                        total = int(total) - int(discount)
                        
                    # Calculate by per head
                    elif discount_type == "per-head":

                        discount = total - (int(number_of_people) * int(discount_amount))
                        per_head = int(discount_amount)
                        total = int(total) - int(discount)



                    add_event_sale = EventSale(
                        
                        status=event_status,
                        event_timing=event_time,
                        event_date=event_date,
                        no_of_people=number_of_people,
                        setup=setup,
                        deals=deal,
                        customer_name=customer_name,
                        customer_number=customer_number,
                        per_head=per_head,
                        extra_charges=extra_charge,
                        stage_charges= stage_charge,
                        entry_charges=entry_charge,
                        hall_charges = hall,
                        location=location,
                        gents=gents,
                        ladies= ladies,
                        total_menu= int(menu_amount),
                        food_menu=food_menu,
                        detials=details,
                        total_amount= total ,
                        recieved_amount=received_ammount, 
                        discount_amount= discount,
                        remaining_amount = total - (int(received_ammount) + int(discount)),

                    )
                    add_event_sale.save()

                    # EventExpense.objects.create(bill = add_event_sale, customer_name=customer_name,pakwan_bill=int(menu_amount), total_expense=int(menu_amount))
                    messages.success(request, "Event added Successfully")
                    return redirect('event-sale')
        # except:
            
        #     messages.error(request, "Invalid Form Submission,  Hint: Check for empty form fields, make sure to not add text in filed of number, remove for decimal number")
        #     return redirect('calculate_menu')




class Pre_Deals(LoginRequiredMixin,TemplateView):
    template_name = "items/deals-calculator.html"
    LOGIN_URL = "account/login"  
    
    def get(self, request, deal_id):
        try:
            # Get the deal and its items
            deal = get_object_or_404(Deals, pk=deal_id)
            items = deal.menu_items.all()  # Get all menu items for this deal
            
            # Create food menu string
            food_list = [item.product_name for item in items]
            food_menu = ', '.join(food_list)
            
            # Create product JSON for JavaScript
            product_json = []
            for item in items:
                product_json.append({
                    'id': item.id,
                    'name': item.product_name,
                    'price': float(item.price)
                })
            
            context = {
                'items': items,
                'Deal_name': deal,
                'food_menu': food_menu,
                'product_json': json.dumps(product_json),
                'deal': deal_id
            }
            
            return render(request, self.template_name, context)
            
        except Exception as e:
            messages.error(request, f"Error loading deal: {str(e)}")
            return redirect('calculate_menu')
    
    def post(self, request, deal_id):
        
            
        
                context = {}
            # try:
                event_status = request.POST.get('status')
                event_time = request.POST.get('event-time')

                event_date = request.POST.get('event-date')
                number_of_people = request.POST.get('no-of-people')
                setup = request.POST.get('setup')

                deal_number = request.POST.get('deals')


                deal = request.POST.get('deals')
                location = request.POST.get('location')
            
                customer_name = request.POST.get('customer-name')
                customer_number = request.POST.get('customer-number')
                per_head = request.POST.get('per-head')
                per_head = float(per_head)

                extra_charge = request.POST.get('extra-charges')
                stage_charge = request.POST.get('stage-charges')
                entry_charge = request.POST.get('entry-charges')
                hall = request.POST.get('hall')
                hall = float(hall)
                menu_amount = request.POST.get('menu-amount')
                menu_amount = float(menu_amount)
                food_menu = request.POST.get('food-menu')
                gents = request.POST.get('gents')
                ladies = request.POST.get('ladies')
                
                discount_amount = request.POST.get('discount-amount')
                discount_type = request.POST.get('discount')
                
                details = request.POST.get('details')
                received_ammount = request.POST.get('received-amount')
                
                    

                request.session['deals'] = deal_number
                total = (int(number_of_people) * int(per_head)) + (int(extra_charge) + int(stage_charge) + int(entry_charge)) 

                discount = 0

                # Calculate by discount
                if discount_type == 'percent':
                    
                    discount =  ((int(discount_amount) / 100) * total)

                # Calculate by fix price
                elif discount_type == "fix":
                    discount = discount_amount
                    
                    
                # Calculate by per head
                elif discount_type == "per-head":

                    discount = total - (int(number_of_people) * int(discount_amount))
                    per_head = int(discount_amount)

                add_event_sale = EventSale(
                    
                    status=event_status,
                    event_timing=event_time,
                    event_date=event_date,
                    no_of_people=number_of_people,
                    setup=setup,
                    deals=deal,
                    customer_name=customer_name,
                    customer_number=customer_number,
                    per_head=int(per_head),
                    extra_charges=extra_charge,
                    total_menu = int(menu_amount),
                    hall_charges = int(hall),
                    stage_charges= stage_charge,
                    entry_charges=entry_charge,
                    location = location,
                    gents= gents,
                    food_menu=food_menu,
                    detials=details,
                    total_amount= total ,
                    recieved_amount=received_ammount, 
                    discount_amount=discount,
                    remaining_amount = total - (int(received_ammount) - int(discount)),
                )
                add_event_sale.save()

                # EventExpense.objects.create(bill = add_event_sale.id, customer=customer_name,pakwan_bill=int(menu_amount))
            
                messages.success(request, "Event added Successfully")
                return redirect('event-sale')
            # except:
            #     messages.error(request, "Invalid Form Submission,  Hint: Check for empty form fields, make sure to not add text in filed of number, remove for decimal number")
            #     return redirect('event-sale')
            
class DealsList(LoginRequiredMixin, TemplateView):
    template_name = "items/deals_list.html"
    LOGIN_URL = "account/login"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['deals'] = Deals.objects.all()
        return context

class EditDeal(LoginRequiredMixin, TemplateView):
    template_name = "items/create-deal.html"
    LOGIN_URL = "account/login"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        deal_id = self.kwargs.get('deal_id')
        deal = get_object_or_404(Deals, pk=deal_id)
        
        products = MyProducts.objects.filter(status=1)
        product_json = []
        for product in products:
            product_json.append({
                'id': product.id, 
                'product_name': product.product_name, 
                'price': float(product.price)
            })
        
        context['page_title'] = "Edit Deal"
        context['products'] = products
        context['product_json'] = json.dumps(product_json)
        context['deal'] = deal
        context['selected_products'] = [item.id for item in deal.menu_items.all()]
        return context

    def post(self, request, deal_id):
        try:
            deal_name = request.POST.get('deal_name', '').strip()
            product_ids = request.POST.getlist('product_ids[]')
            
            print(f"Received deal_name: '{deal_name}'")  # Debug
            print(f"Received product_ids: {product_ids}")  # Debug
            print(f"POST data: {dict(request.POST)}")  # Debug

            # Validate input
            if not deal_name:
                return JsonResponse({
                    'success': False, 
                    'message': 'Deal name is required.'
                }, status=400)
                
            if not product_ids or len(product_ids) == 0:
                return JsonResponse({
                    'success': False, 
                    'message': 'Please select at least one product.'
                }, status=400)

            # Get the existing deal
            deal = get_object_or_404(Deals, pk=deal_id)
            
            # Check if new name conflicts with other deals
            if Deals.objects.filter(code=deal_name).exclude(pk=deal_id).exists():
                return JsonResponse({
                    'success': False, 
                    'message': 'A deal with this name already exists.'
                }, status=400)

            # Update deal name
            deal.code = deal_name
            
            # Validate products exist and are active
            valid_products = []
            for product_id in product_ids:
                try:
                    product = MyProducts.objects.get(id=int(product_id), status=1)
                    valid_products.append(product)
                    print(f"Added product: {product.product_name}")  # Debug
                except (MyProducts.DoesNotExist, ValueError) as e:
                    print(f"Product error: {e}")  # Debug
                    return JsonResponse({
                        'success': False,
                        'message': f'Product with ID {product_id} not found or inactive.'
                    }, status=400)
            
            # Update products in the deal
            deal.menu_items.clear()  # Remove existing items
            if valid_products:
                deal.menu_items.set(valid_products)
                print(f"Successfully updated {len(valid_products)} products in deal")  # Debug
            
            deal.save()
            
            return JsonResponse({
                'success': True,
                'message': f'Deal "{deal_name}" updated successfully with {len(valid_products)} items!',
                'deal_id': deal.id
            })
            
        except Exception as e:
            print(f"Unexpected error: {str(e)}")  # Debug
            return JsonResponse({
                'success': False,
                'message': f'An error occurred: {str(e)}'
            }, status=500)
          
            
        
   
        
       

        

        




     
            

@login_required
def import_products_csv(request):
    """Handle CSV import for products"""
    if request.method == 'POST':
        if 'csv_file' not in request.FILES:
            messages.error(request, 'No file selected.')
            return redirect('ecommerce-product-list')
        
        csv_file = request.FILES['csv_file']
        
        # Validate file type
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'Please upload a CSV file.')
            return redirect('ecommerce-product-list')
        
        try:
            # Read CSV file
            data_set = csv_file.read().decode('UTF-8')
            io_string = io.StringIO(data_set)
            reader = csv.DictReader(io_string)
            
            success_count = 0
            error_count = 0
            errors = []
            
            with transaction.atomic():
                for row_num, row in enumerate(reader, start=2):  # Start from 2 because row 1 is header
                    try:
                        # Get or create related objects
                        category_name = row.get('category', '').strip()
                        brand_name = row.get('brand', '').strip()
                        unit_name = row.get('unit', '').strip()
                        
                        # Validate required fields
                        product_name = row.get('product_name', '').strip()
                        if not product_name:
                            errors.append(f"Row {row_num}: Product name is required")
                            error_count += 1
                            continue
                        
                        # Get or create Category
                        category = None
                        if category_name:
                            category, created = Category.objects.get_or_create(
                                name=category_name,
                                defaults={'description': f'Auto-created from CSV import', 'status': 1}
                            )
                        
                        # Get or create Brand
                        brand = None
                        if brand_name:
                            brand, created = Brand.objects.get_or_create(
                                name=brand_name,
                                defaults={'desc': f'Auto-created from CSV import'}
                            )
                        
                        # Get or create Unit
                        unit = None
                        if unit_name:
                            unit, created = Unit.objects.get_or_create(
                                name=unit_name,
                                defaults={'short_name': unit_name[:10], 'unit': unit_name}
                            )
                        
                        # Get other product fields
                        price = int(row.get('price', 0)) if row.get('price', '').strip() else 0
                        qty = int(row.get('qty', 0)) if row.get('qty', '').strip() else 0
                        product_desc = row.get('description', '').strip()
                        
                        # Check if product exists by name
                        product, created = MyProducts.objects.get_or_create(
                            product_name=product_name,
                            defaults={
                                'code': '',  # Empty code since it's not in the form
                                'category_id': category,
                                'brand': brand,
                                'unit': unit,
                                'price': price,
                                'cost': 0,  # Not included in form
                                'qty': qty,
                                'product_desc': product_desc,
                                'status': 1  # Default to active
                            }
                        )
                        
                        # If product exists, update it
                        if not created:
                            product.category_id = category if category else product.category_id
                            product.brand = brand if brand else product.brand
                            product.unit = unit if unit else product.unit
                            product.price = price if price else product.price
                            product.qty = qty if qty else product.qty
                            product.product_desc = product_desc if product_desc else product.product_desc
                            product.save()
                        
                        success_count += 1
                        
                    except Exception as e:
                        error_count += 1
                        errors.append(f"Row {row_num}: {str(e)}")
                        continue
            
            # Prepare result message
            if success_count > 0:
                messages.success(request, f'Successfully imported {success_count} products.')
            
            if error_count > 0:
                error_msg = f'Failed to import {error_count} products.\n' + '\n'.join(errors[:10])  # Show first 10 errors
                if len(errors) > 10:
                    error_msg += f'\n... and {len(errors) - 10} more errors.'
                messages.error(request, error_msg)
            
        except Exception as e:
            messages.error(request, f'Error processing CSV file: {str(e)}')
    
    return redirect('ecommerce-product-list')

def download_product_csv_template(request):
    """Download CSV template for products"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="product_template.csv"'
    
    writer = csv.writer(response)
    writer.writerow([
        'product_name', 'brand', 'unit', 'category', 'price', 'qty', 'description'
    ])
    writer.writerow([
        'Samsung Galaxy S21', 'Samsung', 'pcs', 'Electronics', '50000', '10', 'High-end smartphone with excellent camera'
    ])
    writer.writerow([
        'Dell Laptop', 'Dell', 'pcs', 'Computers', '35000', '8', 'Business laptop with Intel i5 processor'
    ])
    
    return response
            